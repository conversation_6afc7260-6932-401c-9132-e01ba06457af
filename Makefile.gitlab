# Variables for GitLab Registry
REGISTRY_URL = registry.gitlab.com
REPO = uims/ssv
TAG = latest
PLATFORM = linux/arm64 # Target platform for ARM Linux

# GitLab credentials from an external file
GITLAB_USERNAME = sandeepsuman
GITLAB_TOKEN_FILE = ./.gitlab-token # Path to the external file containing the GitLab token

# Docker login using token from external file
login:
	@if [ -f $(GITLAB_TOKEN_FILE) ]; then \
		GITLAB_TOKEN=$$(cat $(GITLAB_TOKEN_FILE)); \
		echo $$GITLAB_TOKEN | docker login $(REGISTRY_URL) -u $(GITLAB_USERNAME) --password-stdin; \
	else \
		echo "GitLab token file not found! Please create $(GITLAB_TOKEN_FILE) with your token."; \
		exit 1; \
	fi

# Docker commands with platform support

build_backend:
	docker build --platform $(PLATFORM) -t $(REGISTRY_URL)/$(REPO)/backend:$(TAG) ./backend

push_backend:
	docker push $(REGISTRY_URL)/$(REPO)/backend:$(TAG)

# Combined commands

deploy_backend: login build_backend push_backend


# Deploy all
deploy_all: login deploy_backend
