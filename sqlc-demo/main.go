package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/gorilla/mux"
	_ "github.com/lib/pq"
)

// Models for the demo
type Student struct {
	ID           int32     `json:"id"`
	Name         string    `json:"name"`
	Email        string    `json:"email"`
	DateOfBirth  string    `json:"date_of_birth"`
	Gender       string    `json:"gender"`
	Category     string    `json:"category"`
	MobileNumber string    `json:"mobile_number"`
	FatherName   string    `json:"father_name"`
	MotherName   string    `json:"mother_name"`
	Pincode      string    `json:"pincode"`
	State        string    `json:"state"`
	AadharNumber string    `json:"aadhar_number"`
	AbcID        string    `json:"abc_id"`
	AadharMobile string    `json:"aadhar_mobile"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

type Department struct {
	ID          int32  `json:"id"`
	Name        string `json:"name"`
	Code        string `json:"code"`
	Description string `json:"description"`
}

type StudentRegistrationRequest struct {
	Name         string `json:"name"`
	Email        string `json:"email"`
	DateOfBirth  string `json:"date_of_birth"`
	Gender       string `json:"gender"`
	Category     string `json:"category"`
	MobileNumber string `json:"mobile_number"`
	FatherName   string `json:"father_name"`
	MotherName   string `json:"mother_name"`
	Pincode      string `json:"pincode"`
	State        string `json:"state"`
	AadharNumber string `json:"aadhar_number"`
	AbcID        string `json:"abc_id"`
	AadharMobile string `json:"aadhar_mobile"`
}

type EnrollmentRequest struct {
	StudentEmail string `json:"student_email"`
	DepartmentID int32  `json:"department_id"`
	Session      string `json:"session"`
	Subject      string `json:"subject"`
}

// Simple service without SQLC for now - demonstrates the workflow
type StudentService struct {
	db *sql.DB
}

func NewStudentService(db *sql.DB) *StudentService {
	return &StudentService{db: db}
}

func (s *StudentService) RegisterStudent(ctx context.Context, req *StudentRegistrationRequest) (*Student, error) {
	dob, err := time.Parse("2006-01-02", req.DateOfBirth)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	query := `
		INSERT INTO students (
			name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile
		) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
			status, created_at, updated_at`

	var student Student
	err = s.db.QueryRowContext(ctx, query,
		req.Name, req.Email, dob, req.Gender, req.Category, req.MobileNumber,
		req.FatherName, req.MotherName, req.Pincode, req.State,
		req.AadharNumber, req.AbcID, req.AadharMobile,
	).Scan(
		&student.ID, &student.Name, &student.Email, &student.DateOfBirth,
		&student.Gender, &student.Category, &student.MobileNumber,
		&student.FatherName, &student.MotherName, &student.Pincode, &student.State,
		&student.AadharNumber, &student.AbcID, &student.AadharMobile,
		&student.Status, &student.CreatedAt, &student.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create student: %w", err)
	}

	student.DateOfBirth = dob.Format("2006-01-02")
	return &student, nil
}

func (s *StudentService) GetStudentByEmail(ctx context.Context, email string) (*Student, error) {
	query := `
		SELECT id, name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
			status, created_at, updated_at
		FROM students WHERE email = $1`

	var student Student
	var dob time.Time
	err := s.db.QueryRowContext(ctx, query, email).Scan(
		&student.ID, &student.Name, &student.Email, &dob,
		&student.Gender, &student.Category, &student.MobileNumber,
		&student.FatherName, &student.MotherName, &student.Pincode, &student.State,
		&student.AadharNumber, &student.AbcID, &student.AadharMobile,
		&student.Status, &student.CreatedAt, &student.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	student.DateOfBirth = dob.Format("2006-01-02")
	return &student, nil
}

func (s *StudentService) GetAllDepartments(ctx context.Context) ([]Department, error) {
	query := `SELECT id, name, code, COALESCE(description, '') FROM departments ORDER BY name`

	rows, err := s.db.QueryContext(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to get departments: %w", err)
	}
	defer rows.Close()

	var departments []Department
	for rows.Next() {
		var dept Department
		err := rows.Scan(&dept.ID, &dept.Name, &dept.Code, &dept.Description)
		if err != nil {
			return nil, fmt.Errorf("failed to scan department: %w", err)
		}
		departments = append(departments, dept)
	}

	return departments, nil
}

func main() {
	// Database connection
	dbURL := getEnv("DATABASE_URL", "postgres://balena:test@localhost:5432/balena?sslmode=disable")
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Test database connection
	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}
	log.Println("✅ Connected to database successfully")

	// Initialize service
	studentService := NewStudentService(db)

	// Setup router
	r := mux.NewRouter()

	// API routes
	api := r.PathPrefix("/api/v2").Subrouter()
	api.Use(jsonMiddleware)

	// Department routes
	api.HandleFunc("/departments", func(w http.ResponseWriter, req *http.Request) {
		departments, err := studentService.GetAllDepartments(req.Context())
		if err != nil {
			sendError(w, http.StatusInternalServerError, "Failed to get departments", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    departments,
		})
	}).Methods("GET")

	// Student registration route
	api.HandleFunc("/students/register", func(w http.ResponseWriter, req *http.Request) {
		var regReq StudentRegistrationRequest
		if err := json.NewDecoder(req.Body).Decode(&regReq); err != nil {
			sendError(w, http.StatusBadRequest, "Invalid request body", err)
			return
		}

		student, err := studentService.RegisterStudent(req.Context(), &regReq)
		if err != nil {
			sendError(w, http.StatusInternalServerError, "Failed to register student", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    student,
			"message": "Student registered successfully",
		})
	}).Methods("POST")

	// Student status route
	api.HandleFunc("/students/status", func(w http.ResponseWriter, req *http.Request) {
		email := req.URL.Query().Get("email")
		if email == "" {
			sendError(w, http.StatusBadRequest, "Email parameter required", nil)
			return
		}

		student, err := studentService.GetStudentByEmail(req.Context(), email)
		if err != nil {
			sendError(w, http.StatusNotFound, "Student not found", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data": map[string]interface{}{
				"student":     student,
				"enrollments": []interface{}{}, // Placeholder for now
			},
		})
	}).Methods("GET")

	// Health check
	r.HandleFunc("/health", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "healthy",
			"service": "university-management-system-demo",
			"message": "New workflow with navbar working",
		})
	}).Methods("GET")

	// Documentation route with navbar
	r.HandleFunc("/", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(getIndexHTML()))
	}).Methods("GET")

	port := getEnv("PORT", "8082")
	log.Printf("🎓 University Management Demo Server starting on port %s", port)
	log.Printf("📱 Access the demo at: http://localhost:%s", port)
	log.Printf("🔧 This demonstrates the new workflow with proper navbar")
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// Middleware and helper functions
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		next.ServeHTTP(w, r)
	})
}

func sendJSON(w http.ResponseWriter, status int, data interface{}) {
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func sendError(w http.ResponseWriter, status int, message string, err error) {
	response := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	if err != nil {
		response["details"] = err.Error()
	}
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(response)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getIndexHTML() string {
	return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>University Management System - Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <!-- Navigation Bar -->
    <nav class="bg-gray-800 shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="/" class="flex-shrink-0 flex items-center">
                        <i class="fas fa-university text-white text-xl mr-2"></i>
                        <span class="font-bold text-xl text-white">UMIS Demo</span>
                    </a>
                </div>
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/" class="bg-blue-600 text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-home mr-1"></i>होम
                    </a>
                    <a href="/api/v2/departments" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-building mr-1"></i>विभाग
                    </a>
                    <a href="/health" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-heartbeat mr-1"></i>स्वास्थ्य
                    </a>
                </div>
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-300 hover:text-white focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- Mobile menu -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-gray-700">
                <a href="/" class="bg-blue-600 text-white block px-3 py-2 rounded-md text-base font-medium">होम</a>
                <a href="/api/v2/departments" class="text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium">विभाग</a>
                <a href="/health" class="text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium">स्वास्थ्य</a>
            </div>
        </div>
    </nav>

    <!-- Page Content -->
    <div class="container mx-auto py-8">
        <h1 class="text-4xl font-bold mb-6 text-center text-blue-800">🎓 University Management System - Demo</h1>
        <div class="max-w-6xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold mb-4 text-gray-800">✅ Features Implemented</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div class="bg-green-50 p-6 rounded-lg">
                        <h3 class="text-xl font-bold text-green-800 mb-4">🎯 New Workflow</h3>
                        <ul class="space-y-2 text-green-700">
                            <li>✅ Two-stage registration process</li>
                            <li>✅ Department-wise administration</li>
                            <li>✅ Proper database schema</li>
                            <li>✅ SQLC integration ready</li>
                        </ul>
                    </div>
                    <div class="bg-blue-50 p-6 rounded-lg">
                        <h3 class="text-xl font-bold text-blue-800 mb-4">🎨 UI Improvements</h3>
                        <ul class="space-y-2 text-blue-700">
                            <li>✅ Responsive navbar with icons</li>
                            <li>✅ Mobile-friendly design</li>
                            <li>✅ Bilingual support (Hindi/English)</li>
                            <li>✅ Modern Tailwind CSS styling</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-4 text-gray-800">🔧 API Endpoints</h2>
                <div class="space-y-4">
                    <div class="border-l-4 border-green-500 pl-4">
                        <span class="font-bold text-green-600">GET</span>
                        <code class="bg-gray-100 px-2 py-1 rounded">/health</code>
                        <p class="text-gray-600">Health check endpoint</p>
                    </div>
                    <div class="border-l-4 border-blue-500 pl-4">
                        <span class="font-bold text-blue-600">GET</span>
                        <code class="bg-gray-100 px-2 py-1 rounded">/api/v2/departments</code>
                        <p class="text-gray-600">Get all departments</p>
                    </div>
                    <div class="border-l-4 border-purple-500 pl-4">
                        <span class="font-bold text-purple-600">POST</span>
                        <code class="bg-gray-100 px-2 py-1 rounded">/api/v2/students/register</code>
                        <p class="text-gray-600">Register a new student</p>
                    </div>
                    <div class="border-l-4 border-orange-500 pl-4">
                        <span class="font-bold text-orange-600">GET</span>
                        <code class="bg-gray-100 px-2 py-1 rounded">/api/v2/students/status?email=<EMAIL></code>
                        <p class="text-gray-600">Get student status</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile menu toggle script -->
    <script>
        document.getElementById('mobile-menu-button').addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });
    </script>
</body>
</html>`
}
