version: "2.1"

services:
  postgres:
    build: ./postgres
    restart: always
    environment:
      POSTGRES_USER: balena
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: balena
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    command:
      [
        "postgres",
        "-c",
        "config_file=/docker-entrypoint-initdb.d/postgresql.conf",
      ]

  app:
    build: ./backend
    restart: always
    environment:
      DB_USER: balena
      DB_PASSWORD: ${POSTGRES_PASSWORD} # This stays secret in Balena Dashboard
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: balena
      JWT_SECRET: fkldsfjlalk
      APP_USERNAME: ${APP_USERNAME}
      APP_PASSWORD: ${APP_PASSWORD}
    ports:
      - "8000:80"
    depends_on:
      - postgres

  cloudflared:
    image: cloudflare/cloudflared:latest
    restart: always
    command: tunnel --no-autoupdate run

volumes:
  db_data:
