# Define default environment variables
POSTGRES_USER=balena
POSTGRES_PASSWORD ?= $(shell openssl rand -base64 16)  # Generate a random password if not set
DB_NAME=balena
DB_HOST=postgres
DB_PORT=5432
JWT_SECRET=fkldsfjlalk
CLOUDFLARE_TUNNEL_TOKEN = ""

# Default target
.PHONY: help
help:
	@echo "Makefile commands:"
	@echo "  make setup          - Set environment variables and display them"
	@echo "  make up             - Start all services"
	@echo "  make down           - Stop all services"
	@echo "  make restart        - Restart all services"
	@echo "  make up-postgres    - Start only PostgreSQL"
	@echo "  make up-app         - Start only the backend app"
	@echo "  make up-cloudflared - Start only the Cloudflare tunnel"
	@echo "  make logs           - Show logs for all services"
	@echo "  make clean          - Remove containers, volumes, and reset everything"

# Display environment variables (useful for debugging)
.PHONY: setup
setup:
	@echo "POSTGRES_USER=$(POSTGRES_USER)"
	@echo "POSTGRES_PASSWORD=$(POSTGRES_PASSWORD)"
	@echo "DB_NAME=$(DB_NAME)"
	@echo "DB_HOST=$(DB_HOST)"
	@echo "DB_PORT=$(DB_PORT)"
	@echo "JWT_SECRET=$(JWT_SECRET)"
	@echo "CLOUDFLARE_TUNNEL_TOKEN=$(CLOUDFLARE_TUNNEL_TOKEN)"

# Start all services
.PHONY: up
up: setup
	POSTGRES_PASSWORD=$(POSTGRES_PASSWORD) \
	CLOUDFLARE_TUNNEL_TOKEN=$(CLOUDFLARE_TUNNEL_TOKEN) \
	docker compose up -d

# Stop all services
.PHONY: down
down:
	docker compose down

# Restart all services
.PHONY: restart
restart: down up

# Start only PostgreSQL
.PHONY: up-postgres
up-postgres: setup
	POSTGRES_PASSWORD=$(POSTGRES_PASSWORD) \
	docker compose up -d postgres

# Start only the backend app
.PHONY: up-app
up-app: setup
	POSTGRES_PASSWORD=$(POSTGRES_PASSWORD) \
	APP_USERNAME=$(APP_USERNAME) \
	APP_PASSWORD=$(APP_PASSWORD) \
	docker compose up -d app

# Start only the Cloudflare tunnel
.PHONY: up-cloudflared
up-cloudflared: setup
	CLOUDFLARE_TUNNEL_TOKEN=$(CLOUDFLARE_TUNNEL_TOKEN) \
	docker compose up -d cloudflared

# Show logs for all services
.PHONY: logs
logs:
	docker compose logs -f

# Clean up everything (containers, volumes, networks)
.PHONY: clean
clean: down
	@docker volume rm $$(docker volume ls -q) || true
	@echo "Cleanup complete."

# Test commands
.PHONY: test test-unit test-integration test-coverage test-bench test-clean
test:
	@echo "Running all tests..."
	cd backend/app && ./scripts/run_tests.sh all

test-unit:
	@echo "Running unit tests..."
	cd backend/app && ./scripts/run_tests.sh unit

test-integration:
	@echo "Running integration tests..."
	cd backend/app && ./scripts/run_tests.sh integration

test-coverage:
	@echo "Running tests with coverage..."
	cd backend/app && ./scripts/run_tests.sh coverage

test-bench:
	@echo "Running benchmarks..."
	cd backend/app && ./scripts/run_tests.sh bench

test-clean:
	@echo "Cleaning up test artifacts..."
	cd backend/app && ./scripts/run_tests.sh cleanup

# Build commands
.PHONY: build build-docker dev dev-watch
build:
	@echo "Building application..."
	cd backend/app && go build -o bin/server ./cmd/server

build-docker:
	@echo "Building Docker image..."
	docker compose build app

dev:
	@echo "Starting development server..."
	cd backend/app && go run ./cmd/server

dev-watch:
	@echo "Starting development server with file watching..."
	cd backend/app && air

# Code quality commands
.PHONY: lint fmt vet
lint:
	@echo "Running linter..."
	cd backend/app && golangci-lint run

fmt:
	@echo "Formatting code..."
	cd backend/app && go fmt ./...

vet:
	@echo "Running go vet..."
	cd backend/app && go vet ./...

# Database commands
.PHONY: db-migrate db-reset
db-migrate:
	@echo "Running database migrations..."
	cd backend/app && sqlc generate

db-reset:
	@echo "Resetting database..."
	docker compose down -v
	docker compose up -d postgres
	@echo "Waiting for database to be ready..."
	sleep 5

# Help command
.PHONY: help
help:
	@echo "University Exam Form Management System (UMIS) - Makefile Commands"
	@echo ""
	@echo "Service Management:"
	@echo "  up              Start all services"
	@echo "  down            Stop all services"
	@echo "  restart         Restart all services"
	@echo "  logs            Show logs for all services"
	@echo "  clean           Clean up containers and volumes"
	@echo ""
	@echo "Individual Services:"
	@echo "  up-postgres    Start PostgreSQL only"
	@echo "  up-app         Start backend app only"
	@echo "  up-cloudflared Start Cloudflare tunnel only"
	@echo ""
	@echo "Testing:"
	@echo "  test           Run all tests"
	@echo "  test-unit      Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  test-coverage  Run tests with coverage report"
	@echo "  test-bench     Run benchmarks"
	@echo "  test-clean     Clean up test artifacts"
	@echo ""
	@echo "Development:"
	@echo "  build          Build application binary"
	@echo "  build-docker   Build Docker image"
	@echo "  dev            Start development server"
	@echo "  dev-watch      Start development server with file watching"
	@echo ""
	@echo "Code Quality:"
	@echo "  lint           Run linter"
	@echo "  fmt            Format code"
	@echo "  vet            Run go vet"
	@echo ""
	@echo "Database:"
	@echo "  db-migrate     Generate SQLC code"
	@echo "  db-reset       Reset database"
	@echo ""
	@echo "Other:"
	@echo "  setup          Display environment variables"
	@echo "  help           Show this help message"
