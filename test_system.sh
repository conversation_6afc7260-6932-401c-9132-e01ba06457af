#!/bin/bash

# Quick Test System Runner
# This script provides an easy way to run the automated testing system

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🧪 University Exam Form Management System${NC}"
echo -e "${BLUE}   Automated Testing System${NC}"
echo -e "${BLUE}========================================${NC}\n"

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Docker is not running. Please start Docker and try again.${NC}"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  docker-compose is not installed. Please install it and try again.${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}\n"

# Generate test data if Python is available
if command -v python3 >/dev/null 2>&1; then
    echo -e "${BLUE}📊 Generating advanced test data...${NC}"
    python3 scripts/generate_test_data.py 2>/dev/null || echo -e "${YELLOW}⚠️  Could not generate advanced test data, using default data${NC}"
fi

# Run the automated test
echo -e "${BLUE}🚀 Starting automated testing...${NC}\n"
./scripts/automated_test.sh

echo -e "\n${GREEN}🎉 Testing completed!${NC}"
echo -e "${BLUE}Check the output above for detailed results.${NC}"
