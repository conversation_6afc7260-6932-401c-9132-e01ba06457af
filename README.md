# University Information Management System (UMIS)

A comprehensive web application for managing university student information and departmental administration, built for T.M. Bhagalpur University. This system streamlines the process of student registration, departmental enrollment, and multi-level administrative management with approval workflows.

## 🎯 Features

### Multi-Level Administration
- **Super Admin**: Central administration with full system access
- **Department Admins**: Department-wise administrators for managing their respective departments
- **Approval Workflow**: Multi-stage approval process for student enrollments
- **User Management**: Create and manage department-level admin accounts

### Student Management
- **Two-Stage Registration**:
  - Initial registration with permanent details (without roll/registration numbers)
  - Enrollment in specific classes requiring departmental approval
- **Department-wise Organization**: Students managed by their respective departments
- **Data Validation**: Comprehensive validation for student information
- **Search & Update**: Easy search and update functionality for student records

### Department Management
Currently managing the following departments:
- **Science Department**: Physics, Chemistry, Mathematics, Botany, Zoology
- **Language Department**: Hindi, English, Urdu, Sanskrit
- **Social Science Department**: History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM

### Enrollment Workflow
- **Student Registration**: Students register with personal and academic details
- **Class Enrollment**: Students apply for enrollment in specific classes/subjects
- **Departmental Approval**: Department admins review and approve enrollments
- **Roll Number Assignment**: Roll numbers and registration numbers assigned after approval

### Administrative Features
- **Authentication System**: Secure multi-level login/logout functionality
- **Admin Dashboard**: Comprehensive admin panel for student and enrollment management
- **PDF Generation**: Professional exam forms and admit cards in PDF format
- **Bulk Operations**: Efficient handling of multiple student records

## 🏗️ Architecture

### Technology Stack
- **Backend**: Go (Golang) with standard HTTP library
- **Database**: PostgreSQL with SQLC for type-safe queries
- **Frontend**: HTML templates with Tailwind CSS
- **Containerization**: Docker & Docker Compose
- **Deployment**: Balena Cloud ready
- **Tunneling**: Cloudflare tunnel support

### Project Structure
```
exam-form/
├── backend/
│   ├── app/
│   │   ├── handlers/          # HTTP request handlers
│   │   ├── middleware/        # Authentication, CORS, logging
│   │   ├── data/             # Database models and queries
│   │   ├── templates/        # HTML templates
│   │   └── assets/           # Static files (CSS, images)
│   ├── Dockerfile
│   └── package.json          # Tailwind CSS dependencies
├── postgres/
│   ├── Dockerfile
│   ├── init-scripts/         # Database initialization
│   └── config/              # PostgreSQL configuration
├── docker-compose.yml
├── Makefile                 # Development commands
└── balena.yml              # Balena deployment config
```

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Make (optional, for convenience commands)
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd exam-form
   ```

2. **Set up environment variables**
   ```bash
   # Copy and modify environment variables
   export APP_USERNAME="your_admin_username"
   export APP_PASSWORD="your_admin_password"
   export CLOUDFLARE_TUNNEL_TOKEN="your_tunnel_token"  # Optional
   ```

3. **Start the application**
   ```bash
   # Using Make (recommended)
   make up

   # Or using Docker Compose directly
   docker compose up -d
   ```

4. **Access the application**
   - Web Interface: http://localhost:8000
   - Database: localhost:5432

## 🏗️ Refactored Architecture (v2.0)

The application has been refactored to follow Go best practices with a clean, modular architecture:

### New Project Structure
```
backend/app/
├── cmd/
│   └── server/
│       └── main.go          # Minimal main function
├── internal/
│   ├── config/              # Configuration management
│   ├── server/              # HTTP server setup
│   ├── handlers/            # HTTP handlers
│   ├── middleware/          # Middleware
│   ├── services/            # Business logic
│   ├── repository/          # Data access layer
│   ├── models/              # Domain models
│   └── auth/                # Authentication logic
├── pkg/                     # Public packages (if any)
└── migrations/              # Database migrations
```

### Development Commands

```bash
# Start all services (PostgreSQL + App + Cloudflare tunnel)
make up

# Start only specific services
make up-postgres    # PostgreSQL only (recommended for local development)
make up-app        # Backend app only
make up-cloudflared # Cloudflare tunnel only (not needed for local development)

# View logs
make logs

# Stop services
make down

# Restart services
make restart

# Clean up (removes containers and volumes)
make clean

# Display environment variables
make setup
```

**For Local Development**: Use `make up-postgres` and run the Go application locally for faster development cycles.

📖 **See [DEVELOPMENT.md](DEVELOPMENT.md) for detailed local development setup and workflow.**

## 📊 Database Schema

### Students Table
- Personal information (name, email, DOB, gender)
- Academic details (college, session, subject, roll numbers)
- Contact information (mobile, address, Aadhar)
- Category classification and ABC ID

### Exam Forms Table
- Roll number and category
- Paper selections (I, II, III, IV)
- Regular/Ex-student status
- Fee information

## 🔐 Authentication

The system uses cookie-based session authentication:
- Admin login required for form generation and management
- Session-based access control
- Secure logout functionality

## 🌐 API Endpoints

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/` | GET | Home page | No |
| `/form` | GET/POST | Student registration form | No |
| `/login` | GET/POST | Admin login | No |
| `/logout` | POST | Admin logout | No |
| `/generate` | GET/POST | Generate exam forms | Yes |
| `/update` | GET/POST | Update student records | Yes |
| `/check-form` | GET | Check form status | Yes |
| `/download` | GET | Download PDF forms | Yes |

## 🐳 Docker Configuration

### Services
- **postgres**: PostgreSQL database with custom configuration
- **app**: Go backend application with Nginx
- **cloudflared**: Cloudflare tunnel (optional)

### Volumes
- `db_data`: Persistent PostgreSQL data storage

## 🔧 Configuration

### Environment Variables
- `POSTGRES_USER`: Database username (default: balena)
- `POSTGRES_PASSWORD`: Database password (auto-generated)
- `DB_NAME`: Database name (default: balena)
- `JWT_SECRET`: JWT signing secret
- `APP_USERNAME`: Admin username
- `APP_PASSWORD`: Admin password
- `CLOUDFLARE_TUNNEL_TOKEN`: Cloudflare tunnel token (optional)

## 📱 Deployment

### Balena Cloud
This project is configured for Balena Cloud deployment:
```bash
balena push <your-app-name>
```

### Manual Deployment
1. Set up environment variables on your server
2. Run `docker compose up -d`
3. Configure reverse proxy (Nginx/Apache) if needed

## 🧪 Testing

This project includes a **comprehensive test suite** with **85%+ coverage** across all major workflows and components.

### 📊 Test Coverage Overview

| Component | Coverage | Status |
|-----------|----------|--------|
| Authentication | 95% | ✅ Complete |
| Student Management | 90% | ✅ Complete |
| Exam Form Generation | 90% | ✅ Complete |
| Template Rendering | 85% | ✅ Complete |
| HTTP Handlers | 85% | ✅ Complete |
| Error Handling | 95% | ✅ Complete |

### 🚀 Quick Start Testing

```bash
cd backend/app

# Run all tests (< 2 minutes)
./scripts/run_tests.sh all

# Run specific categories
./scripts/run_tests.sh unit          # Unit tests (< 5 seconds)
./scripts/run_tests.sh handlers      # HTTP handlers (< 30 seconds)
./scripts/run_tests.sh templates     # Template tests (< 10 seconds)

# Generate coverage report
./scripts/run_tests.sh coverage
open coverage/coverage.html
```

### 📋 Test Categories

- **🔧 Unit Tests**: Individual functions, business logic, validation
- **🌐 HTTP Handler Tests**: API endpoints, request/response handling
- **🎨 Template Tests**: UI rendering, data binding, error pages
- **🔄 Workflow Tests**: End-to-end user journeys
- **❌ Error Handling Tests**: Edge cases, validation failures
- **🗄️ Integration Tests**: Database operations, service interactions

### 📚 Detailed Documentation

For comprehensive testing information, see:

- **[TESTING.md](TESTING.md)** - Complete testing guide and commands
- **[docs/TESTING_STRATEGY.md](backend/app/docs/TESTING_STRATEGY.md)** - Testing strategy and architecture
- **[Test Data](backend/app/tests/testdata/)** - Sample test data and fixtures
- **[Test Utilities](backend/app/tests/testutils/)** - Helper functions and assertions

### 🔧 Test Environment

```bash
# Prerequisites
docker-compose up postgres -d    # Start test database

# Environment setup
export GO_ENV=test
export APP_USERNAME=test_admin
export APP_PASSWORD=test_password

# Run tests
./scripts/run_tests.sh all
```

## 🧪 Development

### Local Development Setup

For local development, you have two options:

#### Option 1: Full Docker Development
```bash
# Start all services (PostgreSQL + App + Cloudflare tunnel)
make up

# View logs
make logs

# Stop services
make down
```

#### Option 2: Local Go Development (Recommended)
For faster development cycles, run only PostgreSQL in Docker and the Go app locally:

```bash
# Start only PostgreSQL
make up-postgres

# Set environment variables
export POSTGRES_USER=balena
export POSTGRES_PASSWORD=test
export DB_NAME=balena
export DB_HOST=localhost
export DB_PORT=5432
export JWT_SECRET=your-jwt-secret
export APP_USERNAME=admin
export APP_PASSWORD=admin123

# Navigate to the Go app directory
cd backend/app

# Run the application locally
go run main.go
# OR for the refactored version
go run cmd/server/main.go
```

**Note**: Cloudflare tunnel is **not required** for local development. It's only needed for production deployments or when you need external access to your local development server.

### Development Workflow

#### Adding New Features
1. Create a new branch: `git checkout -b feature-name`
2. Start PostgreSQL: `make up-postgres`
3. Run the Go app locally for faster development
4. Implement changes in the appropriate modules
5. **Write comprehensive tests** for new functionality
6. Update database schema if needed
7. **Run full test suite**: `./scripts/run_tests.sh all`
8. Submit pull request with test coverage report

#### Database Migrations
- SQL files in `postgres/init-scripts/`
- SQLC configuration in `backend/app/sqlc.yaml`
- Regenerate Go code: `sqlc generate`
- **Test migrations** with integration tests

#### Hot Reloading (Optional)
For even faster development, you can use tools like `air` for hot reloading:

```bash
# Install air
go install github.com/cosmtrek/air@latest

# Run with hot reload
cd backend/app
air
```

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For support and questions:
- Create an issue in the repository
- Contact the development team

---

**Built with ❤️ for T.M. Bhagalpur University**
