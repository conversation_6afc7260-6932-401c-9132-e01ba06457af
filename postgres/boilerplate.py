import os

# Define the folder structure
folders = [
    "config",
    "init-scripts"
]

# Define files with their content
files = {
    "docker-compose.yml": """\
version: '2.1'

services:
  postgres:
    build: .
    restart: always
    environment:
      POSTGRES_USER: balena
      POSTGRES_PASSWORD: balena
      POSTGRES_DB: mydatabase
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
""",
    "Dockerfile": """\
FROM postgres:16

# Set the working directory
WORKDIR /docker-entrypoint-initdb.d

# Copy custom configuration
COPY config/postgresql.conf /etc/postgresql/postgresql.conf
COPY config/pg_hba.conf /etc/postgresql/pg_hba.conf

# Ensure configuration is used
CMD ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf"]
""",
    "config/postgresql.conf": """\
listen_addresses = '*'  # Allows external connections
port = 5432
max_connections = 100
""",
    "config/pg_hba.conf": """\
# TYPE  DATABASE        USER            ADDRESS                 METHOD
host    all             all             0.0.0.0/0               md5
host    all             all             ::/0                    md5
""",
    "init-scripts/init.sql": """\
CREATE TABLE test (id SERIAL PRIMARY KEY, name TEXT);
INSERT INTO test (name) VALUES ('Hello Balena');
"""
}

# Create folders
for folder in folders:
    os.makedirs(folder, exist_ok=True)

# Create files and write content
for file_path, content in files.items():
    with open(file_path, "w") as file:
        file.write(content)

print("✅ Balena PostgreSQL boilerplate generated successfully in the root folder!")
