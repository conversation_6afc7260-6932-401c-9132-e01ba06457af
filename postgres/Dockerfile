FROM postgres:16-alpine

# Copy initialization scripts
COPY init-scripts/init.sql /docker-entrypoint-initdb.d/init.sql

# Copy custom configuration files
COPY config/postgresql.conf /docker-entrypoint-initdb.d/postgresql.conf
COPY config/pg_hba.conf /docker-entrypoint-initdb.d/pg_hba.conf

# Ensure correct ownership and permissions
RUN chown postgres:postgres /docker-entrypoint-initdb.d/init.sql /docker-entrypoint-initdb.d/postgresql.conf /docker-entrypoint-initdb.d/pg_hba.conf \
    && chmod 644 /docker-entrypoint-initdb.d/init.sql \
    && chmod 600 /docker-entrypoint-initdb.d/postgresql.conf /docker-entrypoint-initdb.d/pg_hba.conf

# Start PostgreSQL with the custom configuration file
CMD ["postgres", "-c", "config_file=/docker-entrypoint-initdb.d/postgresql.conf"]
