# Basic settings
listen_addresses = '*'  # Allows external connections
port = 5432
max_connections = 100

# Memory tuning (adjust based on your system)
shared_buffers = 256MB
work_mem = 16MB

# Logging (important for debugging)
logging_collector = on
log_directory = 'pg_log'
log_statement = 'all'

# Performance tuning
autovacuum = on
checkpoint_completion_target = 0.9

hba_file = '/docker-entrypoint-initdb.d/pg_hba.conf'
