{{ define "content" }}
<div class="flex justify-center items-start min-h-screen bg-gray-100 p-4">
    <div class="w-full max-w-sm bg-white p-6 rounded-lg shadow-md mt-10">
        <h2 class="text-2xl font-semibold text-center text-black mb-4">
            Sign In
        </h2>

        {{ if .Error }}
        <div class="bg-gray-200 text-black p-3 rounded-md text-center mb-4">
            {{ .Error }}
        </div>
        {{ end }}

        <form
            action="/login"
            method="POST"
            class="space-y-4"
            x-data="{ showPassword: false }"
        >
            <div>
                <label for="username" class="block text-sm text-black"
                    >Username</label
                >
                <input
                    type="text"
                    id="username"
                    name="username"
                    class="w-full px-3 py-2 border border-gray-400 rounded-md bg-white text-black focus:ring-gray-500 focus:border-gray-700 outline-none"
                    required
                />
            </div>

            <div class="relative flex items-center">
                <div class="flex-1">
                    <label for="password" class="block text-sm text-black"
                        >Password</label
                    >
                    <input
                        :type="showPassword ? 'text' : 'password'"
                        id="password"
                        name="password"
                        class="w-full px-3 py-2 border border-gray-400 rounded-md bg-white text-black focus:ring-gray-500 focus:border-gray-700 outline-none pr-10"
                        required
                    />
                </div>
                <button
                    type="button"
                    class="absolute right-3 flex items-center h-full text-gray-700"
                    @click="showPassword = !showPassword"
                >
                    <svg
                        x-show="!showPassword"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.086 3A9.969 9.969 0 0112 19a9.969 9.969 0 01-8.456-4"
                        />
                    </svg>
                    <svg
                        x-show="showPassword"
                        xmlns="http://www.w3.org/2000/svg"
                        class="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M13.875 18.825A9.969 9.969 0 0112 19a9.969 9.969 0 01-8.456-4m15.412-6A9.969 9.969 0 0112 5c-4.478 0-8.268 2.943 9.542 7m1.086 3A9.969 9.969 0 0012 19c4.478 0 8.268-2.943 9.542-7M9.88 9.88l4.24 4.24M14.12 9.88l-4.24 4.24"
                        />
                    </svg>
                </button>
            </div>

            <button
                type="submit"
                class="w-full bg-black text-white py-2 rounded-md hover:bg-gray-800 focus:ring-gray-500 transition"
            >
                Login
            </button>
        </form>
    </div>
</div>
{{ end }}
