{{ define "content" }}
<div class="flex">
    <!-- Sidebar -->
    <aside class="w-64 text-gray-800 h-screen flex flex-col"> <!-- Changed text color to dark -->
        <div class="py-4 px-6 text-lg font-bold">Admin Panel</div>
        <nav class="flex-grow px-4 space-y-4">
            <span class="flex items-center p-2 text-gray-700 cursor-not-allowed" aria-disabled="true">
                <!-- Link is disabled -->
                <i class="iconoir-settings mr-3"></i>
                <span>Users</span>
            </span>
            <a href="/admin"
                class="flex items-center pl-8 text-gray-600 hover:text-white hover:bg-blue-500 rounded transition duration-200">
                <!-- Improved visibility on hover -->
                <span>New User</span>
            </a>
            <a href="/admin/users"
                class="flex items-center pl-8 text-gray-600 hover:text-white hover:bg-blue-500 rounded transition duration-200">
                <!-- Improved visibility on hover -->
                <span>List Users</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content -->
    <div class="container mx-auto py-8 max-w-3xl" x-data="{ search: '', deleteMessage: '' }">
        <!-- Search bar -->
        <div class="mb-8">
            <input type="text" x-model="search" placeholder="Search users..."
                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Message Display -->
        <div x-show="deleteMessage" class="bg-green-100 text-green-800 p-4 rounded mb-4">
            <p x-text="deleteMessage"></p>
        </div>

        <!-- User List -->
        <div class="bg-white shadow-md rounded-lg p-4" id="user-list">
            <ul class="space-y-4">
                {{ range .Users }}
                <li class="flex justify-between items-center p-4 border-b"
                    x-show="search === '' || {{ .Username | json }}.toLowerCase().includes(search.toLowerCase())">
                    <span class="font-medium">{{ .Username }}</span>
                    <button hx-delete="/admin/users/delete/{{ .Username }}" hx-target="#user-list" hx-swap="outerHTML"
                        hx-trigger="click"
                        hx-on="htmx:afterRequest: deleteMessage = 'User {{ .Username }} deleted successfully.'"
                        class="text-red-600 hover:text-red-800">
                        Delete
                    </button>
                </li>
                {{ end }}
            </ul>
        </div>
    </div>
</div>
{{ end }}