{{define "content"}}
<div class="max-w-4xl mx-auto p-6 bg-white shadow-md rounded-md">
    <h2 class="text-2xl font-bold mb-4 text-center">Update Student Record</h2>
    {{if .Success}}
    <div id="fading-message" class="bg-green-100 text-green-700 px-4 py-2 rounded-md mb-4 text-center">
        {{.Success}}
    </div>
    {{else if .Error}}
    <div id="fading-message" class="bg-red-100 text-red-700 px-4 py-2 rounded-md mb-4 text-center">
        {{.Error}}
    </div>
    {{end}}

    <!-- Compact Search Box -->
    <form id="searchForm" method="POST" action="/update" class="flex flex-wrap items-center gap-2 bg-gray-100 p-3 rounded-md shadow-sm mb-4">
        <label for="search_type" class="text-gray-700 text-sm">Search By:</label>
        <select name="search_type" id="search_type" required class="p-1 border border-gray-300 rounded-md text-sm">
            <option value="">-- Select --</option>
            <option value="exam_roll">Exam Roll Number</option>
            <option value="abc_id">ABC ID</option>
            <option value="aadhaar">Aadhaar Number</option>
        </select>

        <input type="text" name="search_value" id="search_value" required placeholder="Enter value" class="p-1 border border-gray-300 rounded-md text-sm w-40">

        <button type="submit" class="bg-blue-600 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-700 transition">
            Search
        </button>
    </form>

    <!-- Student Record Form -->
    {{if and .Student .Student.ID}}
    <form id="updateForm" method="POST" action="/update" class="space-y-4">
        <input type="hidden" name="id" value="{{.Student.ID}}">
        <input type="hidden" name="update" value="true">

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Name -->
            <div>
                <label class="block text-gray-700">Name:</label>
                <input type="text" name="name" value="{{.Student.Name}}" required class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Email -->
            <div>
                <label class="block text-gray-700">Email:</label>
                <input type="email" name="email" value="{{.Student.Email}}" required class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Date of Birth -->
            <div>
                <label class="block text-gray-700">Date of Birth:</label>
                <input type="date" name="date_of_birth" value="{{if not .Student.DateOfBirth.IsZero}}{{.Student.DateOfBirth.Format "2006-01-02"}}{{end}}" required class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Gender -->
            <div>
                <label class="block text-gray-700">Gender:</label>
                <input type="text" name="gender" value="{{.Student.Gender}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Category -->
            <div>
                <label class="block text-gray-700">Category:</label>
                <input type="text" name="category" value="{{.Student.Category}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Mobile Number -->
            <div>
                <label class="block text-gray-700">Mobile Number:</label>
                <input type="text" name="mobile_number" value="{{.Student.MobileNumber}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Session -->
            <div>
                <label class="block text-gray-700">Session:</label>
                <input type="text" name="session" value="{{.Student.Session}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Subject -->
            <div>
                <label class="block text-gray-700">Subject:</label>
                <input type="text" name="subject" value="{{.Student.Subject}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Class Roll Number -->
            <div>
                <label class="block text-gray-700">Class Roll Number:</label>
                <input type="text" name="class_roll_number" value="{{.Student.ClassRollNumber}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- University Roll Number -->
            <div>
                <label class="block text-gray-700">University Roll Number:</label>
                <input type="text" name="university_roll_number" value="{{.Student.UniversityRollNumber}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Registration Number -->
            <div>
                <label class="block text-gray-700">Registration Number:</label>
                <input type="text" name="registration_number" value="{{.Student.RegistrationNumber}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Father Name -->
            <div>
                <label class="block text-gray-700">Father Name:</label>
                <input type="text" name="father_name" value="{{.Student.FatherName}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Mother Name -->
            <div>
                <label class="block text-gray-700">Mother Name:</label>
                <input type="text" name="mother_name" value="{{.Student.MotherName}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Pincode -->
            <div>
                <label class="block text-gray-700">Pincode:</label>
                <input type="text" name="pincode" value="{{.Student.Pincode}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- State -->
            <div>
                <label class="block text-gray-700">State:</label>
                <input type="text" name="state" value="{{.Student.State}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- ABC ID -->
            <div>
                <label class="block text-gray-700">ABC ID:</label>
                <input type="text" name="abc_id" value="{{.Student.AbcID}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Aadhaar Number -->
            <div>
                <label class="block text-gray-700">Aadhaar Number:</label>
                <input type="text" name="aadhar_number" value="{{.Student.AadharNumber}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <!-- Aadhaar Mobile -->
            <div>
                <label class="block text-gray-700">Aadhaar Mobile:</label>
                <input type="text" name="aadhar_mobile" value="{{.Student.AadharMobile}}" class="w-full p-2 border border-gray-300 rounded-md">
            </div>
        </div>

        <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition">
            Update
        </button>
    </form>
    {{else}}
    <div class="text-center text-gray-600 mt-8">
        <p>Please search for a student using the search form above to update their record.</p>
    </div>
    {{end}}
</div>

<!-- jQuery for Form Validation & Auto-hide Messages -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    // Auto-hide success message after 3 seconds
    setTimeout(function() {
        $("#fading-message").fadeOut("slow");
    }, 3000);

    // Form Validation for Search Box
    $("#searchForm").on("submit", function(e) {
        let valid = true;

        if ($("#search_type").val().trim() === "") {
            $("#search_type").addClass("border-red-500");
            valid = false;
        } else {
            $("#search_type").removeClass("border-red-500");
        }

        if ($("#search_value").val().trim() === "") {
            $("#search_value").addClass("border-red-500");
            valid = false;
        } else {
            $("#search_value").removeClass("border-red-500");
        }

        if (!valid) {
            alert("Please select a search type and enter a value.");
            e.preventDefault();
        }
    });

    // Form Validation for Student Record Update
    $("#updateForm").on("submit", function(e) {
        let valid = true;
        $("#updateForm input[required]").each(function() {
            if ($(this).val().trim() === "") {
                $(this).addClass("border-red-500");
                valid = false;
            } else {
                $(this).removeClass("border-red-500");
            }
        });

        if (!valid) {
            alert("Please fill all required fields.");
            e.preventDefault();
        }
    });
});
</script>
{{end}}
