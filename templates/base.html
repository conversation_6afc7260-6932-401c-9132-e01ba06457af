<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{{.Title}}</title>
        <link rel="stylesheet" href="/assets/css/output.css" />
        <script
            src="https://cdn.jsdelivr.net/npm/alpinejs@3.x/dist/cdn.min.js"
            defer
        ></script>
        <script
            src="https://unpkg.com/htmx.org@2.0.3"
            integrity="sha384-0895/pl2MU10Hqc6jd4RvrthNlDiE9U1tWmX7WRESftEDRosgxNsQG/Ze9YMRzHq"
            crossorigin="anonymous"
        ></script>
        <script
            src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"
            defer
        ></script>
        <!-- Added MathJax -->
        <script>
            MathJax = {
                tex: {
                    packages: { "[+]": ["ams", "base"] }, // Added amsmath support only
                    inlineMath: [
                        ["$", "$"],
                        ["\\(", "\\)"],
                    ],
                    displayMath: [
                        ["$$", "$$"],
                        ["\\[", "\\]"],
                    ],
                    processEnvironments: true, // Process environments like align
                    processRefs: true, // Process references
                },
                svg: {
                    fontCache: "global",
                },
            };
        </script>
        <style>
            @media print {
                @page {
                    size: A4;
                    margin: 0;
                }
            }
        </style>
    </head>

    <body class="bg-gray-100">
        <!-- Navigation Bar -->
        <nav
            x-data="{ open: false }"
            class="flex items-center justify-between flex-wrap bg-gray-800 p-6 shadow print:hidden"
        >
            <div class="flex items-center flex-shrink-0 text-white mr-6">
                <span class="font-semibold text-xl tracking-tight"
                    >University Information Management System</span
                >
            </div>

            <div @click="open = !open" class="lg:hidden">
                <button
                    class="flex items-center px-3 py-2 border rounded text-white border-white hover:text-red hover:border-red"
                >
                    <svg
                        class="fill-current h-3 w-3"
                        viewBox="0 0 20 20"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <title>Menu</title>
                        <path
                            x-show="!open"
                            d="M0 3h20v2H0V3zm0 6h20v2H0V9zm0 6h20v2H0v-2z"
                        />
                        <path
                            x-show="open"
                            d="M10 3h10v2H10V3zm0 6h10v2H10V9zm0 6h10v2H10v-2z"
                        />
                    </svg>
                </button>
            </div>

            <div
                :class="{'block': open, 'hidden': !open}"
                class="w-full lg:flex lg:items-center lg:w-auto"
            >
                <div class="text-sm lg:flex-grow"></div>
                <div class="mt-3 lg:mt-0">
                    <a
                        href="/"
                        class="block mt-2 lg:inline-block text-white hover:text-red"
                        >Home</a
                    >
                    {{ if .IsAuthenticated }}
                    <a
                        href="/update"
                        class="block mt-2 lg:inline-block text-white hover:text-red lg:ml-2"
                        >Update</a
                    >
                    <a
                        href="/generate"
                        class="block mt-2 lg:inline-block text-white hover:text-red lg:ml-2"
                        >Form</a
                    >
                    <span
                        class="text-white hidden lg:inline-block lg:block mx-2"
                        >|</span
                    >
                    <a
                        href="/logout"
                        class="block mt-2 lg:inline-block text-white hover:text-red"
                        >Logout</a
                    >
                    {{ else }}
                    <span
                        class="text-white hidden lg:inline-block lg:block mx-2"
                        >|</span
                    >
                    <a
                        href="/login"
                        class="block mt-2 lg:inline-block text-white hover:text-red"
                        >Login</a
                    >
                    {{ end }}
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div class="container mx-auto px-4 py-8">
            {{ block "content" . }}{{ end }}
        </div>
        <script src="/static/dist/js/bundle.js"></script>
    </body>
</html>
