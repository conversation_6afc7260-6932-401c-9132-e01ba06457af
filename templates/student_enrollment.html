{{ define "content" }}
<div class="container mx-auto py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">कक्षा में नामांकन</h1>
        <h2 class="text-xl text-gray-600 mb-8 text-center">Class Enrollment - Step 2</h2>
        
        <div class="bg-yellow-50 border-l-4 border-yellow-500 p-4 mb-6">
            <h3 class="font-bold text-yellow-800 mb-2">नामांकन प्रक्रिया</h3>
            <p class="text-gray-700">
                अब आप अपने चुने गए विभाग में कक्षा के लिए नामांकन कर सकते हैं। 
                नामांकन के बाद विभागीय एडमिन द्वारा अनुमोदन के बाद आपको रोल नंबर प्रदान किया जाएगा।
            </p>
        </div>

        <form id="enrollmentForm" class="bg-white shadow-lg rounded-lg p-8">
            <!-- Department Selection -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">विभाग चयन</h3>
                
                <div class="grid md:grid-cols-1 gap-6">
                    <div>
                        <label for="department_id" class="block text-sm font-medium text-gray-700 mb-2">
                            विभाग / Department <span class="text-red-500">*</span>
                        </label>
                        <select id="department_id" name="department_id" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            onchange="updateSubjects()">
                            <option value="">विभाग चुनें / Select Department</option>
                            {{ range .Departments }}
                            <option value="{{ .ID }}" data-subjects="{{ .GetSubjects | join "," }}">
                                {{ .Name }} ({{ .Code }})
                            </option>
                            {{ end }}
                        </select>
                    </div>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">शैक्षणिक जानकारी</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="session" class="block text-sm font-medium text-gray-700 mb-2">
                            सत्र / Session <span class="text-red-500">*</span>
                        </label>
                        <select id="session" name="session" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">सत्र चुनें / Select Session</option>
                            <option value="2022-25">2022-25 (वर्तमान पार्ट-3)</option>
                            <option value="2023-27">2023-27 (सेमेस्टर-3)</option>
                            <option value="2024-28">2024-28 (वर्तमान सेमेस्टर-1)</option>
                            <option value="2025-29">2025-29 (नया सत्र)</option>
                        </select>
                    </div>

                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                            विषय / Subject <span class="text-red-500">*</span>
                        </label>
                        <select id="subject" name="subject" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">पहले विभाग चुनें / Select Department First</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Roll Number Information (Optional - will be assigned after approval) -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">रोल नंबर जानकारी (वैकल्पिक)</h3>
                <p class="text-sm text-gray-600 mb-4">
                    यदि आपके पास पहले से रोल नंबर है तो भरें, अन्यथा अनुमोदन के बाद प्रदान किया जाएगा।
                </p>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <div>
                        <label for="class_roll_number" class="block text-sm font-medium text-gray-700 mb-2">
                            कक्षा रोल नंबर / Class Roll Number
                        </label>
                        <input type="text" id="class_roll_number" name="class_roll_number" maxlength="4"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="जैसे: 001">
                    </div>

                    <div>
                        <label for="university_roll_number" class="block text-sm font-medium text-gray-700 mb-2">
                            विश्वविद्यालय रोल नंबर / University Roll Number
                        </label>
                        <input type="text" id="university_roll_number" name="university_roll_number" maxlength="10"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="जैसे: 2024001001">
                    </div>

                    <div>
                        <label for="registration_number" class="block text-sm font-medium text-gray-700 mb-2">
                            पंजीकरण संख्या / Registration Number
                        </label>
                        <input type="text" id="registration_number" name="registration_number" maxlength="20"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="जैसे: REG2024001">
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" 
                    class="px-8 py-3 bg-gradient-to-r from-green-500 to-blue-600 text-white font-bold rounded-lg hover:from-green-600 hover:to-blue-700 transition duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    नामांकन के लिए आवेदन करें / Apply for Enrollment
                </button>
            </div>
        </form>
    </div>
</div>

<script>
// Department subjects mapping
const departmentSubjects = {
    'SCI': ['Physics', 'Chemistry', 'Mathematics', 'Botany', 'Zoology'],
    'LANG': ['Hindi', 'English', 'Urdu', 'Sanskrit'],
    'SOC': ['History', 'Political Science', 'Economics', 'Sociology', 'Philosophy', 'Psychology', 'AIHC', 'IRPM']
};

function updateSubjects() {
    const departmentSelect = document.getElementById('department_id');
    const subjectSelect = document.getElementById('subject');
    const selectedOption = departmentSelect.options[departmentSelect.selectedIndex];
    
    // Clear existing options
    subjectSelect.innerHTML = '<option value="">विषय चुनें / Select Subject</option>';
    
    if (selectedOption.value) {
        const subjects = selectedOption.getAttribute('data-subjects');
        if (subjects) {
            subjects.split(',').forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.trim();
                option.textContent = subject.trim();
                subjectSelect.appendChild(option);
            });
        }
    }
}

document.getElementById('enrollmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Basic validation
    const requiredFields = ['department_id', 'session', 'subject'];
    
    for (let field of requiredFields) {
        const element = document.getElementById(field);
        if (!element.value.trim()) {
            alert(`कृपया ${element.previousElementSibling.textContent} भरें`);
            element.focus();
            return;
        }
    }
    
    // Submit form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('/api/students/enroll', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('नामांकन आवेदन सफलतापूर्वक जमा किया गया! विभागीय अनुमोदन का इंतज़ार करें।');
            window.location.href = '/student/status';
        } else {
            alert('त्रुटि: ' + (data.message || 'नामांकन में समस्या हुई'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('नामांकन में त्रुटि हुई। कृपया पुनः प्रयास करें।');
    });
});
</script>
{{ end }}
