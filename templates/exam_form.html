{{ define "personalDetails" }}
<div class="form-section">
    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 70%; padding: 8px 8px 8px 0">
                <strong>Name of the Candidate:</strong> {{ .Student.Name }}
            </td>
            <td style="text-align: right; width: 30%; padding: 8px">
                <strong>Class Roll No:</strong> {{ .Student.ClassRollNumber }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 100%; padding: 8px 8px 8px 0">
                <strong>Name of Father/Husband:</strong> {{ .Student.FatherName
                }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 100%; padding: 8px 8px 8px 0">
                <strong>Name of Mother:</strong> {{ .Student.MotherName }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 60%; padding: 8px 8px 8px 0">
                <strong>Date of Birth:</strong> {{ .Student.DateOfBirth |
                formatDate }}
            </td>
            <td style="text-align: left; width: 40%; padding: 8px">
                <strong>Mobile No:</strong> {{ .Student.MobileNumber }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 60%; padding: 8px 8px 8px 0">
                <strong>Registration No:</strong> {{ .Student.RegistrationNumber
                }}
            </td>
            <td style="text-align: left; width: 40%; padding: 8px">
                <strong>Roll Number:</strong> {{ .Student.UniversityRollNumber
                }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 60%; padding: 8px 8px 8px 0">
                <strong>Aadhaar No:</strong> {{ .Student.AadharNumber }}
            </td>
            <td style="text-align: left; width: 40%; padding: 8px">
                <strong>ABC ID:</strong> {{ .Student.AbcID }}
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 100%; padding: 8px 8px 8px 0">
                <strong>Dept./College:</strong> SSV College, Kahalgaon
            </td>
        </tr>
    </table>

    <table style="width: 70%; border-collapse: collapse">
        <tr>
            <td style="text-align: left; width: 100%; padding: 8px 8px 8px 0">
                <strong>Faculty:</strong> Science / Commerce / Humanities /
                Social Sc.
            </td>
        </tr>
    </table>
</div>

{{ end }} {{ define "examDetails" }}
<p class="sub-header">BA/BSc/BCom Degree Part III Exam</p>
<p class="sub-header">Exam: 2025</p>
{{ end }}

<!doctype html>
<html>
    <head>
        <meta charset="UTF-8" />
        <title>Exam Form</title>
        <style>
            @page {
                size: A4;
                margin: 1cm;
                background: url("templates/background10.png") no-repeat center
                    center;
                background-size: 100% auto;
            }
            .page-break {
                page-break-before: always; /* Forces a new page */
            }
            body {
                font-family:
                    "Times New Roman", "FreeSerif", "Liberation Serif",
                    "Noto Serif", serif;
                text-align: center;
                margin: 0;
                padding: 0;
            }
            .header {
                display: flex;
                align-items: center;
                justify-content: flex-start;
                margin-bottom: 0px;
            }
            .header img {
                width: 60px;
                height: 60px;
                margin-right: 10px;
                padding-top: 20px;
                margin-left: 10px;
            }
            .header h1 {
                font-size: 26px;
                margin: 0;
                align-self: center;
                width: 750px;
                margin-left: 10px;
                margin-right: 100px;
            }
            .below-header {
                margin-top: -20px;
                margin-left: -50px;
            }
            .sub-header {
                font-size: 16px;
            }
            .exam-form {
                display: flex;
                justify-content: left; /* Center the main content */
                align-items: center; /* Vertically align everything */
                position: relative;
                font-size: 20px;
                font-weight: bold;
                width: 100%;
            }

            .form-row {
                display: flex;
                justify-content: space-between;
                width: 100%;
            }

            .main-text {
                text-align: center;
                margin-left: 275px;
            }

            .category {
                font-size: 14px;
                font-weight: normal;
                margin-left: 15px; /* Adjust spacing as needed */
                align-self: center; /* Ensure it aligns in the middle */
            }
            .subject {
                text-align: left;
                margin-top: 10px;
            }
            .photo-box {
                position: absolute;
                top: 70px;
                right: 20px;
                width: 120px;
                height: 150px;
                border: 1px solid black;
                text-align: center;
                font-size: 12px;
                padding: 5px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
            .signature-box {
                position: absolute;
                top: 231px;
                right: 20px;
                width: 120px;
                height: 50px;
                border: 1px solid black;
                text-align: center;
                font-size: 12px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding: 5px;
            }
            .signature-instruction {
                position: absolute;
                top: 280px;
                right: 6px;
                width: 150px;
                height: 50px;
                text-align: center;
                font-size: 12px;
                display: flex;
                flex-direction: column;
                justify-content: flex-end;
                padding: 5px;
            }
            .form-section {
                text-align: left;
                font-size: 14px;
                margin-top: 20px;
                padding: 10px;
            }
            .form-section p {
                margin: 5px 0;
            }

            table {
                border-collapse: collapse; /* Ensures no extra spacing */
                width: 70%; /* Keep consistent width */
            }

            td {
                padding: 0; /* Remove padding */
            }

            .dotted-line {
                border-bottom: 1px dotted black;
                display: inline-block;
                width: 50%;
            }
            /* Styles for the blank tables */
            .subject-title {
                font-style: italic;
                text-align: left;
                font-weight: bold;
            }
            .promo-title {
                text-align: left;
                margin-top: 20px;
                font-weight: bold;
            }
            .table-section {
                margin: 20px 10px;
            }
            .blank-table {
                width: 100%;
                border: 1px solid black;
                border-collapse: collapse;
            }
            .blank-table td {
                border: 1px solid black;
                height: 50px; /* Adjust the height as needed */
                width: 20%;
            }
            .signature-section {
                margin-top: 50px;
                text-align: center;
                font-size: 14px;
                text-align: left;
            }
            .signature-section p {
                margin: 5px 0;
            }
            .signature-container {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
                font-weight: bold;
            }
            .note {
                font-size: 12px;
                font-style: italic;
                position: absolute;
                bottom: 20px;
                width: 100%;
                text-align: left;
            }
            .signature-section-admit-card {
                display: flex;
                justify-content: space-between;
                margin-top: 50px;
                text-align: center;
            }

            .signature-box-admit-card {
                width: 30%;
                border-top: 1px solid black;
                padding-top: 10px;
                font-weight: bold;
                font-style: italic;
            }

            .controller-signature {
                text-align: right;
                width: 35%; /* Slightly wider for better spacing */
            }

            .attendance-table {
                width: 100%;
                border-collapse: collapse;
                text-align: center;
            }

            .attendance-table th,
            .attendance-table td {
                border: 1px solid black;
                padding: 8px;
                height: 30px;
            }

            .attendance-sheet h3 {
                text-align: center;
                font-weight: bold;
            }
            .signature-section-cs {
                display: flex;
                justify-content: flex-end; /* Pushes content to the right */
                margin-top: 50px;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <img src="templates/logo.jpeg" alt="University Logo" />
            <h1>T.M. Bhagalpur University, Bhagalpur</h1>
        </div>
        <div class="below-header">
            {{ template "examDetails" . }}
            <div class="exam-form">
                <div class="main-text">Examination Form</div>
                <div class="category">(Category- Reg/Ex)</div>
            </div>
            <p class="sub-header">Subject: {{ .Student.Subject }}</p>
            <div class="photo-box">
                <div
                    style="
                        display: flex;
                        align-items: center;
                        height: 150px;
                        width: 100px;
                    "
                >
                    Affix a recent passport size colour photograph must be
                    attested by HOD/Principal
                </div>
            </div>
            <div class="signature-box"></div>
            <div class="signature-instruction">
                <span>Signature of the Examinee</span>
                <span>परीक्षार्थी के हस्ताक्षर</span>
            </div>
        </div>

        {{ template "personalDetails" . }}

        <!-- Two Blank Tables Added Below -->
        <div class="table-section">
            <div class="subject-title">Subject of Examinations:</div>
            <table class="blank-table">
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
            </table>
            <div class="promo-title">
                Details of Papers in which you were Promoted with Examination
                Year
            </div>
            <table class="blank-table">
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
            </table>
        </div>

        <div class="signature-section">
            <p>
                <strong><em>Forwarding of H.O.D./Principal</em></strong>
            </p>
            <p>
                Certified that the candidate has completed 75% Class attendance
                & has appeared in all C.I.A.
            </p>
            <div class="signature-container">
                <span>Signature of the Candidate</span>
                <span>Signature of H.O.D./Principal</span>
            </div>
            <p class="note">
                Note: The Price of Exam Form Rs.10 will be deposited with exam
                fee.
            </p>
        </div>

        <div class="page-break"></div>

        <div class="header">
            <img src="templates/logo.jpeg" alt="University Logo" />
            <h1>T.M. Bhagalpur University, Bhagalpur</h1>
        </div>
        <div class="below-header">
            {{ template "examDetails" . }}
            <div class="exam-form">
                <div class="main-text">Admit Card</div>
                <div class="category">(Category- Reg/Ex)</div>
            </div>
            <p class="sub-header">Subject: {{ .Student.Subject }}</p>
            <div class="photo-box">
                <div
                    style="
                        display: flex;
                        align-items: center;
                        height: 150px;
                        width: 100px;
                    "
                >
                    Affix a recent passport size colour photograph must be
                    attested by HOD/Principal
                </div>
            </div>
            <div class="signature-box"></div>
            <div class="signature-instruction">
                <span>Signature of the Examinee</span>
                <span>परीक्षार्थी के हस्ताक्षर</span>
            </div>
        </div>

        {{ template "personalDetails" . }}

        <div class="signature-section-admit-card">
            <div class="signature-box-admit-card" style="text-align: left">
                <span>Signature of Examinee</span>
            </div>
            <div class="signature-box-admit-card">
                <span>Assistant (Exam)</span>
            </div>
            <div class="signature-box-admit-card controller-signature">
                <span>Controller of Examinations</span>
            </div>
        </div>

        <div class="attendance-sheet">
            <h3>ATTENDANCE SHEET</h3>
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th>Date of Exam</th>
                        <th>Subject</th>
                        <th>Paper</th>
                        <th>Answer Book No.</th>
                        <th>Room No.</th>
                        <th>Candidate’s Signature</th>
                        <th>Invigilator’s Signature</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="signature-section-cs">
            <div class="signature-box-admit-card controller-signature">
                <span>Center Supritendent</span>
            </div>
        </div>
    </body>
</html>
