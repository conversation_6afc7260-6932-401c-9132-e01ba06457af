{{ define "content" }}
<div class="container mx-auto py-8">
    <div class="max-w-md mx-auto">
        <div class="bg-white shadow-lg rounded-lg p-8">
            <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">Admin Login</h1>
            <h2 class="text-lg text-gray-600 mb-8 text-center">एडमिन लॉगिन</h2>
            
            <div id="error-message" class="hidden bg-red-50 border-l-4 border-red-500 p-4 mb-6">
                <p class="text-red-700" id="error-text"></p>
            </div>

            <form id="loginForm">
                <div class="mb-6">
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email Address <span class="text-red-500">*</span>
                    </label>
                    <input type="email" id="email" name="email" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your email address">
                </div>

                <div class="mb-6">
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password <span class="text-red-500">*</span>
                    </label>
                    <input type="password" id="password" name="password" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your password">
                </div>

                <div class="mb-6">
                    <button type="submit" id="loginButton"
                        class="w-full px-4 py-2 bg-blue-500 text-white font-bold rounded-lg hover:bg-blue-600 transition duration-300">
                        <span id="loginButtonText">Login</span>
                        <span id="loginSpinner" class="hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            Logging in...
                        </span>
                    </button>
                </div>
            </form>

            <div class="text-center">
                <a href="/" class="text-blue-500 hover:text-blue-700 text-sm">
                    ← Back to Home
                </a>
            </div>
        </div>

        <div class="mt-8 bg-blue-50 border-l-4 border-blue-500 p-4">
            <h3 class="font-bold text-blue-800 mb-2">Access Levels</h3>
            <ul class="text-sm text-gray-700">
                <li><strong>Super Admin:</strong> Full system access, can create department admins</li>
                <li><strong>Department Admin:</strong> Manage students and enrollments in assigned department</li>
            </ul>
        </div>
    </div>
</div>

<script>
document.getElementById('loginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    if (!email || !password) {
        showError('Please fill in all fields');
        return;
    }
    
    // Show loading state
    setLoading(true);
    hideError();
    
    // Submit login request
    fetch('/api/auth/login', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            email: email,
            password: password
        })
    })
    .then(response => response.json())
    .then(data => {
        setLoading(false);
        
        if (data.success) {
            // Store token in localStorage
            localStorage.setItem('auth_token', data.data.token);
            localStorage.setItem('user_data', JSON.stringify(data.data.user));
            
            // Redirect to admin dashboard
            window.location.href = '/admin/dashboard';
        } else {
            showError(data.error || 'Login failed');
        }
    })
    .catch(error => {
        setLoading(false);
        console.error('Login error:', error);
        showError('Login failed. Please try again.');
    });
});

function setLoading(loading) {
    const button = document.getElementById('loginButton');
    const buttonText = document.getElementById('loginButtonText');
    const spinner = document.getElementById('loginSpinner');
    
    if (loading) {
        button.disabled = true;
        buttonText.classList.add('hidden');
        spinner.classList.remove('hidden');
    } else {
        button.disabled = false;
        buttonText.classList.remove('hidden');
        spinner.classList.add('hidden');
    }
}

function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    
    errorText.textContent = message;
    errorDiv.classList.remove('hidden');
}

function hideError() {
    const errorDiv = document.getElementById('error-message');
    errorDiv.classList.add('hidden');
}

// Check if already logged in
document.addEventListener('DOMContentLoaded', function() {
    const token = localStorage.getItem('auth_token');
    if (token) {
        // Verify token is still valid
        fetch('/api/auth/refresh', {
            method: 'POST',
            headers: {
                'Authorization': 'Bearer ' + token,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Token is valid, redirect to dashboard
                window.location.href = '/admin/dashboard';
            }
        })
        .catch(error => {
            // Token is invalid, clear it
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');
        });
    }
});
</script>
{{ end }}
