{{ define "content" }}
<div class="container mx-auto py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">Student Status</h1>
        <h2 class="text-xl text-gray-600 mb-8 text-center">छात्र स्थिति जांच</h2>
        
        <!-- Search Form -->
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Check Your Status</h3>
            
            <form id="statusForm" class="mb-6">
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Enter your registered email">
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" 
                            class="w-full px-6 py-2 bg-blue-500 text-white font-bold rounded-lg hover:bg-blue-600 transition duration-300">
                            <i class="fas fa-search mr-2"></i>
                            Check Status
                        </button>
                    </div>
                </div>
            </form>
            
            <div id="error-message" class="hidden bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                <p class="text-red-700" id="error-text"></p>
            </div>
        </div>

        <!-- Student Information -->
        <div id="student-info" class="hidden bg-white shadow-lg rounded-lg p-6 mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Student Information</h3>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <p><strong>Name:</strong> <span id="student-name"></span></p>
                    <p><strong>Email:</strong> <span id="student-email"></span></p>
                    <p><strong>Mobile:</strong> <span id="student-mobile"></span></p>
                    <p><strong>Father's Name:</strong> <span id="student-father"></span></p>
                </div>
                <div>
                    <p><strong>Category:</strong> <span id="student-category"></span></p>
                    <p><strong>State:</strong> <span id="student-state"></span></p>
                    <p><strong>Registration Date:</strong> <span id="student-created"></span></p>
                    <p><strong>Status:</strong> <span id="student-status" class="px-2 py-1 rounded text-sm font-semibold"></span></p>
                </div>
            </div>
        </div>

        <!-- Enrollment Information -->
        <div id="enrollment-info" class="hidden bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">Enrollment Information</h3>
            
            <div id="no-enrollments" class="hidden text-center py-8">
                <i class="fas fa-info-circle text-4xl text-blue-500 mb-4"></i>
                <p class="text-gray-600">No enrollments found. Please complete your enrollment process.</p>
                <a href="/student/enrollment" class="inline-block mt-4 px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                    Start Enrollment
                </a>
            </div>
            
            <div id="enrollments-list"></div>
        </div>
    </div>
</div>

<script>
document.getElementById('statusForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    
    if (!email) {
        showError('Please enter your email address');
        return;
    }
    
    // Hide previous results
    hideError();
    document.getElementById('student-info').classList.add('hidden');
    document.getElementById('enrollment-info').classList.add('hidden');
    
    // Fetch student status
    fetch(`/api/students/status?email=${encodeURIComponent(email)}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStudentInfo(data.data.student);
            displayEnrollments(data.data.enrollments);
        } else {
            showError(data.error || 'Student not found');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('Failed to fetch student status. Please try again.');
    });
});

function displayStudentInfo(student) {
    document.getElementById('student-name').textContent = student.name;
    document.getElementById('student-email').textContent = student.email;
    document.getElementById('student-mobile').textContent = student.mobile_number;
    document.getElementById('student-father').textContent = student.father_name;
    document.getElementById('student-category').textContent = student.category;
    document.getElementById('student-state').textContent = student.state;
    document.getElementById('student-created').textContent = new Date(student.created_at).toLocaleDateString();
    
    const statusElement = document.getElementById('student-status');
    statusElement.textContent = getStatusDisplay(student.status);
    statusElement.className = `px-2 py-1 rounded text-sm font-semibold ${getStatusClass(student.status)}`;
    
    document.getElementById('student-info').classList.remove('hidden');
}

function displayEnrollments(enrollments) {
    const enrollmentInfo = document.getElementById('enrollment-info');
    const noEnrollments = document.getElementById('no-enrollments');
    const enrollmentsList = document.getElementById('enrollments-list');
    
    enrollmentInfo.classList.remove('hidden');
    
    if (!enrollments || enrollments.length === 0) {
        noEnrollments.classList.remove('hidden');
        enrollmentsList.innerHTML = '';
        return;
    }
    
    noEnrollments.classList.add('hidden');
    
    let html = '';
    enrollments.forEach((enrollment, index) => {
        html += `
            <div class="border rounded-lg p-4 mb-4">
                <div class="grid md:grid-cols-2 gap-4">
                    <div>
                        <p><strong>Department:</strong> ${enrollment.department ? enrollment.department.name : 'N/A'}</p>
                        <p><strong>Subject:</strong> ${enrollment.subject}</p>
                        <p><strong>Session:</strong> ${enrollment.session}</p>
                        <p><strong>Applied Date:</strong> ${new Date(enrollment.created_at).toLocaleDateString()}</p>
                    </div>
                    <div>
                        <p><strong>Status:</strong> <span class="px-2 py-1 rounded text-sm font-semibold ${getEnrollmentStatusClass(enrollment.status)}">${getEnrollmentStatusDisplay(enrollment.status)}</span></p>
                        ${enrollment.class_roll_number ? `<p><strong>Class Roll:</strong> ${enrollment.class_roll_number}</p>` : ''}
                        ${enrollment.university_roll_number ? `<p><strong>University Roll:</strong> ${enrollment.university_roll_number}</p>` : ''}
                        ${enrollment.registration_number ? `<p><strong>Registration No:</strong> ${enrollment.registration_number}</p>` : ''}
                        ${enrollment.rejection_reason ? `<p><strong>Rejection Reason:</strong> <span class="text-red-600">${enrollment.rejection_reason}</span></p>` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    
    enrollmentsList.innerHTML = html;
}

function getStatusDisplay(status) {
    switch(status) {
        case 'registered': return 'Registered';
        case 'enrolled': return 'Enrolled';
        case 'approved': return 'Approved';
        case 'rejected': return 'Rejected';
        default: return 'Unknown';
    }
}

function getStatusClass(status) {
    switch(status) {
        case 'registered': return 'bg-blue-100 text-blue-800';
        case 'enrolled': return 'bg-yellow-100 text-yellow-800';
        case 'approved': return 'bg-green-100 text-green-800';
        case 'rejected': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function getEnrollmentStatusDisplay(status) {
    switch(status) {
        case 'pending': return 'Pending Approval';
        case 'approved': return 'Approved';
        case 'rejected': return 'Rejected';
        default: return 'Unknown';
    }
}

function getEnrollmentStatusClass(status) {
    switch(status) {
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'approved': return 'bg-green-100 text-green-800';
        case 'rejected': return 'bg-red-100 text-red-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

function showError(message) {
    const errorDiv = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    
    errorText.textContent = message;
    errorDiv.classList.remove('hidden');
}

function hideError() {
    const errorDiv = document.getElementById('error-message');
    errorDiv.classList.add('hidden');
}
</script>
{{ end }}
