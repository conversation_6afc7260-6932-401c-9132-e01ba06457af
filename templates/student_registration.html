{{ define "content" }}
<div class="container mx-auto py-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">छात्र पंजीकरण</h1>
        <h2 class="text-xl text-gray-600 mb-8 text-center">Student Registration - Step 1</h2>
        
        <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
            <h3 class="font-bold text-blue-800 mb-2">महत्वपूर्ण सूचना</h3>
            <p class="text-gray-700">
                यह प्रारंभिक पंजीकरण है। इस चरण में आपको अपनी व्यक्तिगत जानकारी भरनी है। 
                <b>रोल नंबर और पंजीकरण संख्या की आवश्यकता नहीं है।</b> 
                पंजीकरण के बाद आप विभाग चुनकर नामांकन के लिए आवेदन कर सकेंगे।
            </p>
        </div>

        <form id="studentForm" class="bg-white shadow-lg rounded-lg p-8">
            <!-- Personal Information -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">व्यक्तिगत जानकारी</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            पूरा नाम / Full Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="name" name="name" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="अपना पूरा नाम लिखें">
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            ईमेल / Email <span class="text-red-500">*</span>
                        </label>
                        <input type="email" id="email" name="email" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="<EMAIL>">
                    </div>

                    <div>
                        <label for="date_of_birth" class="block text-sm font-medium text-gray-700 mb-2">
                            जन्म तिथि / Date of Birth <span class="text-red-500">*</span>
                        </label>
                        <input type="date" id="date_of_birth" name="date_of_birth" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>

                    <div>
                        <label for="gender" class="block text-sm font-medium text-gray-700 mb-2">
                            लिंग / Gender <span class="text-red-500">*</span>
                        </label>
                        <select id="gender" name="gender" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें / Select</option>
                            <option value="M">पुरुष / Male</option>
                            <option value="F">महिला / Female</option>
                        </select>
                    </div>

                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                            श्रेणी / Category <span class="text-red-500">*</span>
                        </label>
                        <select id="category" name="category" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें / Select</option>
                            <option value="UR">सामान्य / General (UR)</option>
                            <option value="EWS">आर्थिक रूप से कमजोर वर्ग / EWS</option>
                            <option value="BC">पिछड़ा वर्ग / Backward Class (BC)</option>
                            <option value="EBC">अत्यंत पिछड़ा वर्ग / Extremely Backward Class (EBC)</option>
                            <option value="SC">अनुसूचित जाति / Scheduled Caste (SC)</option>
                            <option value="ST">अनुसूचित जनजाति / Scheduled Tribe (ST)</option>
                        </select>
                    </div>

                    <div>
                        <label for="mobile_number" class="block text-sm font-medium text-gray-700 mb-2">
                            मोबाइल नंबर / Mobile Number <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="mobile_number" name="mobile_number" required pattern="[0-9]{10}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="10 अंकों का मोबाइल नंबर">
                    </div>
                </div>
            </div>

            <!-- Family Information -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">पारिवारिक जानकारी</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="father_name" class="block text-sm font-medium text-gray-700 mb-2">
                            पिता का नाम / Father's Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="father_name" name="father_name" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="पिता का पूरा नाम">
                    </div>

                    <div>
                        <label for="mother_name" class="block text-sm font-medium text-gray-700 mb-2">
                            माता का नाम / Mother's Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="mother_name" name="mother_name" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="माता का पूरा नाम">
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">पता की जानकारी</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="pincode" class="block text-sm font-medium text-gray-700 mb-2">
                            पिन कोड / PIN Code <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="pincode" name="pincode" required pattern="[0-9]{6}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="6 अंकों का पिन कोड">
                    </div>

                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700 mb-2">
                            राज्य / State <span class="text-red-500">*</span>
                        </label>
                        <select id="state" name="state" required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें / Select</option>
                            <option value="BIHAR">बिहार / Bihar</option>
                            <option value="OTHER">अन्य / Other</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Document Information -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-gray-800 border-b pb-2">दस्तावेज़ की जानकारी</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label for="aadhar_number" class="block text-sm font-medium text-gray-700 mb-2">
                            आधार नंबर / Aadhar Number <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="aadhar_number" name="aadhar_number" required pattern="[0-9]{12}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="12 अंकों का आधार नंबर">
                    </div>

                    <div>
                        <label for="abc_id" class="block text-sm font-medium text-gray-700 mb-2">
                            ABC ID <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="abc_id" name="abc_id" required maxlength="12"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="ABC ID (12 characters)">
                    </div>

                    <div>
                        <label for="aadhar_mobile" class="block text-sm font-medium text-gray-700 mb-2">
                            आधार से लिंक्ड मोबाइल / Aadhar Linked Mobile <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" id="aadhar_mobile" name="aadhar_mobile" required pattern="[0-9]{10}"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="आधार से लिंक्ड मोबाइल नंबर">
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center">
                <button type="submit" 
                    class="px-8 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg hover:from-blue-600 hover:to-purple-700 transition duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-user-plus mr-2"></i>
                    पंजीकरण पूरा करें / Complete Registration
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.getElementById('studentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Basic validation
    const requiredFields = ['name', 'email', 'date_of_birth', 'gender', 'category', 'mobile_number', 'father_name', 'mother_name', 'pincode', 'state', 'aadhar_number', 'abc_id', 'aadhar_mobile'];
    
    for (let field of requiredFields) {
        const element = document.getElementById(field);
        if (!element.value.trim()) {
            alert(`कृपया ${element.previousElementSibling.textContent} भरें`);
            element.focus();
            return;
        }
    }
    
    // Submit form data
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('/api/students/register', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('पंजीकरण सफल! अब आप विभाग चुनकर नामांकन के लिए आवेदन कर सकते हैं।');
            window.location.href = '/student/enrollment';
        } else {
            alert('त्रुटि: ' + (data.message || 'पंजीकरण में समस्या हुई'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('पंजीकरण में त्रुटि हुई। कृपया पुनः प्रयास करें।');
    });
});
</script>
{{ end }}
