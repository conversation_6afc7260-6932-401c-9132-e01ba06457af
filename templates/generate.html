{{ define "content" }}
<div
    class="max-w-lg w-[600px] mx-auto p-6 bg-white shadow-lg rounded-lg mt-6 relative"
>
    <h2 class="text-2xl font-bold text-gray-800 mb-2">
        Generate Part III Exam 2025 Form
    </h2>
    <p class="text-red-600 mb-6">
        छात्र विवरण सही हैं यह सुनिश्चित करें। अपडेट करने के लिए
        <a href="/update" class="underline">यहां क्लिक करें</a>।
    </p>

    <!-- Message Box -->
    <div id="message-box" class="hidden p-3 mb-4 text-white rounded-lg"></div>

    <!-- Roll Number Field -->
    <div class="mb-4">
        <label for="roll" class="block text-gray-700 font-medium mb-1"
            >Exam Roll Number</label
        >
        <input
            type="text"
            id="roll"
            required
            {{
            if
            .Roll
            }}
            value="{{ .Roll }}"
            {{
            end
            }}
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your roll number"
        />
    </div>

    <!-- Application Form -->
    <form
        id="exam-form"
        action="/generate"
        method="POST"
        class="relative z-10 hidden"
    >
        <input type="hidden" id="hidden-roll" name="roll" />

        <div class="mb-4">
            <label for="category" class="block text-gray-700 font-medium mb-1"
                >Category</label
            >
            <select
                id="category"
                name="category"
                class="w-full px-4 py-2 border rounded-lg"
            >
                <option value="UR">UR</option>
                <option value="EWS">EWS</option>
                <option value="BC">BC</option>
                <option value="EBC">EBC</option>
                <option value="SC">SC</option>
                <option value="ST">ST</option>
            </select>
        </div>

        <div class="mb-4">
            <label for="examType" class="block text-gray-700 font-medium mb-1"
                >Exam Type</label
            >
            <select
                id="examType"
                name="examType"
                class="w-full px-4 py-2 border rounded-lg"
            >
                <option value="Regular" selected>Regular</option>
                <option value="Ex">Ex</option>
            </select>
        </div>

        <div id="paper-selection" class="mb-4 hidden">
            <label class="block text-gray-700 font-medium mb-1"
                >Select Papers</label
            >
            <div class="flex items-center py-1">
                <input type="checkbox" name="papers[]" value="I" class="mr-2" />
                Paper I
            </div>
            <div class="flex items-center py-1">
                <input
                    type="checkbox"
                    name="papers[]"
                    value="II"
                    class="mr-2"
                />
                Paper II
            </div>
            <div class="flex items-center py-1">
                <input
                    type="checkbox"
                    name="papers[]"
                    value="III"
                    class="mr-2"
                />
                Paper III
            </div>
            <div class="flex items-center py-1">
                <input
                    type="checkbox"
                    name="papers[]"
                    value="IV"
                    class="mr-2"
                />
                Paper IV
            </div>
        </div>

        <button
            type="submit"
            class="w-full bg-blue-500 text-white font-semibold py-2 rounded-lg hover:bg-blue-600 transition"
        >
            Submit
        </button>
    </form>

    <!-- Download Button -->
    <a
        id="download-btn"
        href="#"
        class="block w-full bg-blue-500 text-white font-semibold py-2 rounded-lg hover:bg-blue-600 transition text-center hidden"
    >
        Download Form
    </a>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        let delayTimer;

        function checkRollNumber() {
            clearTimeout(delayTimer); // Clear the previous timer if any
            delayTimer = setTimeout(function () {
                let roll = $("#roll").val().trim();
                $("#hidden-roll").val(roll);

                if (/^\d{5,10}$/.test(roll)) {
                    // Ensure roll number is 5 to 10 digits
                    $.ajax({
                        url: "/check-form?roll=" + roll,
                        type: "GET",
                        success: function (response) {
                            if (response.exists) {
                                $("#exam-form").hide();
                                $("#download-btn")
                                    .attr("href", "/download?roll=" + roll)
                                    .removeClass("hidden");
                                showMessage(
                                    "Form available for download.",
                                    "success",
                                );
                            } else {
                                $("#exam-form").show();
                                $("#download-btn").addClass("hidden");
                                showMessage(
                                    "No form found, please fill out the application.",
                                    "error",
                                );
                            }
                        },
                        error: function () {
                            showMessage("Error checking form status.", "error");
                        },
                    });
                } else {
                    $("#exam-form").hide();
                    $("#download-btn").addClass("hidden");
                    showMessage(
                        "Enter valid examination roll number.",
                        "error",
                    );
                }
            }, 500); // 500ms delay before making the AJAX call
        }

        // Check roll on input change
        $("#roll").on("input", checkRollNumber);

        // Handle Exam Type selection
        $("#examType").change(function () {
            if ($(this).val() === "Ex") {
                $("#paper-selection").slideDown();
            } else {
                $("#paper-selection").slideUp();
                $("input[name='papers[]']").prop("checked", false);
            }
        });

        function showMessage(message, type) {
            let messageBox = $("#message-box");
            messageBox
                .removeClass("hidden bg-red-500 bg-green-500")
                .addClass(type === "success" ? "bg-green-500" : "bg-red-500")
                .text(message);
        }
    });
</script>
{{ end }}
