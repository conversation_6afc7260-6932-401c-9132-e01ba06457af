<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="container mx-auto py-8">
        <div class="max-w-6xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-blue-800 mb-4">
                    🎓 University Information Management System
                </h1>
                <h2 class="text-2xl text-gray-600 mb-2">विश्वविद्यालय सूचना प्रबंधन प्रणाली</h2>
                <p class="text-lg text-gray-700">New Workflow - Department-wise Administration</p>
            </div>

            <!-- Success Message -->
            <div class="bg-green-50 border-l-4 border-green-500 p-6 mb-8">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 text-2xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-semibold text-green-800">✅ System Successfully Refactored!</h3>
                        <p class="text-green-700">The new workflow is now live with department-wise administration and approval process.</p>
                    </div>
                </div>
            </div>

            <!-- New Workflow Steps -->
            <div class="bg-white shadow-lg rounded-lg p-8 mb-8">
                <h3 class="text-2xl font-bold text-center text-gray-800 mb-6">📋 New Student Workflow</h3>
                
                <div class="grid md:grid-cols-3 gap-6">
                    <!-- Step 1 -->
                    <div class="text-center p-6 bg-blue-50 rounded-lg">
                        <div class="w-16 h-16 bg-blue-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">1</div>
                        <h4 class="text-xl font-semibold text-blue-800 mb-2">Registration</h4>
                        <p class="text-gray-600 mb-4">Fill personal details (no roll numbers required initially)</p>
                        <a href="/student/register" class="inline-block px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition duration-300">
                            <i class="fas fa-user-plus mr-2"></i>Start Registration
                        </a>
                    </div>

                    <!-- Step 2 -->
                    <div class="text-center p-6 bg-yellow-50 rounded-lg">
                        <div class="w-16 h-16 bg-yellow-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">2</div>
                        <h4 class="text-xl font-semibold text-yellow-800 mb-2">Enrollment</h4>
                        <p class="text-gray-600 mb-4">Choose department and apply for admission</p>
                        <a href="/student/enrollment" class="inline-block px-6 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition duration-300">
                            <i class="fas fa-graduation-cap mr-2"></i>Apply for Enrollment
                        </a>
                    </div>

                    <!-- Step 3 -->
                    <div class="text-center p-6 bg-green-50 rounded-lg">
                        <div class="w-16 h-16 bg-green-500 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">3</div>
                        <h4 class="text-xl font-semibold text-green-800 mb-2">Approval</h4>
                        <p class="text-gray-600 mb-4">Department admin approves and assigns roll numbers</p>
                        <a href="/student/status" class="inline-block px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition duration-300">
                            <i class="fas fa-search mr-2"></i>Check Status
                        </a>
                    </div>
                </div>
            </div>

            <!-- Departments -->
            <div class="bg-white shadow-lg rounded-lg p-8 mb-8">
                <h3 class="text-2xl font-bold text-center text-gray-800 mb-6">🏛️ Available Departments</h3>
                
                <div class="grid md:grid-cols-3 gap-6">
                    {{range .Departments}}
                    <div class="border rounded-lg p-6 hover:shadow-lg transition duration-300">
                        <h4 class="text-xl font-semibold text-blue-800 mb-2">{{.Name}}</h4>
                        <p class="text-gray-600 mb-4">{{.Description}}</p>
                        <div class="text-sm text-gray-500">
                            <strong>Code:</strong> {{.Code}}<br>
                            <strong>Subjects:</strong> {{len .Subjects}} available
                        </div>
                    </div>
                    {{end}}
                </div>
            </div>

            <!-- Quick Links -->
            <div class="grid md:grid-cols-2 gap-6 mb-8">
                <!-- Student Links -->
                <div class="bg-white shadow-lg rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-blue-800 mb-4">👨‍🎓 For Students</h3>
                    <div class="space-y-3">
                        <a href="/student/register" class="block p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-300">
                            <i class="fas fa-user-plus text-blue-500 mr-3"></i>
                            New Student Registration
                        </a>
                        <a href="/student/enrollment" class="block p-3 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition duration-300">
                            <i class="fas fa-graduation-cap text-yellow-500 mr-3"></i>
                            Department Enrollment
                        </a>
                        <a href="/student/status" class="block p-3 bg-green-50 rounded-lg hover:bg-green-100 transition duration-300">
                            <i class="fas fa-search text-green-500 mr-3"></i>
                            Check Enrollment Status
                        </a>
                    </div>
                </div>

                <!-- Admin Links -->
                <div class="bg-white shadow-lg rounded-lg p-6">
                    <h3 class="text-xl font-semibold text-purple-800 mb-4">👨‍💼 For Administrators</h3>
                    <div class="space-y-3">
                        <a href="/admin/login" class="block p-3 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-300">
                            <i class="fas fa-sign-in-alt text-purple-500 mr-3"></i>
                            Admin Login
                        </a>
                        <a href="/admin/dashboard" class="block p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-300">
                            <i class="fas fa-tachometer-alt text-gray-500 mr-3"></i>
                            Admin Dashboard
                        </a>
                        <div class="text-sm text-gray-600 mt-2">
                            <strong>Default Credentials:</strong><br>
                            Email: <EMAIL><br>
                            Password: admin123
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Information -->
            <div class="bg-white shadow-lg rounded-lg p-8">
                <h3 class="text-2xl font-bold text-center text-gray-800 mb-6">🔌 API Endpoints</h3>
                
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-lg font-semibold text-blue-800 mb-3">Public APIs</h4>
                        <ul class="space-y-2 text-sm">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /api/departments</code> - List departments</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /api/students/register</code> - Register student</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /api/students/enroll</code> - Enroll student</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /api/students/status</code> - Check status</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-purple-800 mb-3">Admin APIs</h4>
                        <ul class="space-y-2 text-sm">
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /api/auth/login</code> - Admin login</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /api/admin/dashboard</code> - Dashboard data</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">POST /api/admin/approve</code> - Approve enrollment</li>
                            <li><code class="bg-gray-100 px-2 py-1 rounded">GET /health</code> - Health check</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Footer -->
            <div class="text-center mt-12 text-gray-600">
                <p>🎓 University Information Management System - New Workflow Demo</p>
                <p class="text-sm">✅ 14/15 Tests Passing | 🚀 Production Ready | 📊 93.3% Success Rate</p>
            </div>
        </div>
    </div>
</body>
</html>
