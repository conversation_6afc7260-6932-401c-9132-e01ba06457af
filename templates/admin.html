{{ define "content" }}
<div class="flex">
    <!-- Sidebar -->
    <aside class="w-64 text-gray-800 h-screen flex flex-col"> <!-- Changed text color to dark -->
        <div class="py-4 px-6 text-lg font-bold">Admin Panel</div>
        <nav class="flex-grow px-4 space-y-4">
            <span class="flex items-center p-2 text-gray-700 cursor-not-allowed" aria-disabled="true"> <!-- Link is disabled -->
                <i class="iconoir-settings mr-3"></i>
                <span>Users</span>
            </span>
            <a href="/admin" class="flex items-center pl-8 text-gray-600 hover:text-white hover:bg-blue-500 rounded transition duration-200"> <!-- Improved visibility on hover -->
                <span>New User</span>
            </a>
            <a href="/admin/users" class="flex items-center pl-8 text-gray-600 hover:text-white hover:bg-blue-500 rounded transition duration-200"> <!-- Improved visibility on hover -->
                <span>List Users</span>
            </a>
        </nav>
    </aside>

    <!-- Main Content -->
    <div class="container mx-auto py-8 max-w-3xl"> <!-- Reduced width -->
        <h1 class="text-2xl font-bold mb-4 text-center">{{ .Title }}</h1>
        <p class="text-lg mb-6 text-center">Manage users in the admin dashboard.</p>

        <!-- Add User Form -->
        <div class="bg-white shadow-md rounded-lg overflow-hidden mb-8">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-700 mb-4">Add User</h2>
                <form id="addUserForm" method="POST" action="/admin/add-user" hx-post="/admin/add-user"
                    hx-target="#response" hx-swap="innerHTML" class="space-y-4" hx-on="htmx:afterRequest: this.reset()">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" name="email" id="email" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" name="password" id="password" required
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="submit"
                        class="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg shadow-md hover:bg-blue-600 transition-all duration-300 transform hover:scale-105">
                        <i class="iconoir-user-plus text-lg"></i>
                        <span class="ml-2 font-semibold">Add User</span>
                    </button>
                    <div id="response"></div> <!-- Error or success messages will be shown here -->
                </form>
            </div>
        </div>
    </div>
</div>
{{ end }}
