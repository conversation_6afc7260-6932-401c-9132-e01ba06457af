{{ define "content" }}
<div class="container mx-auto py-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center text-blue-800">Admin Dashboard</h1>
        
        <!-- Quick Stats -->
        <div class="grid md:grid-cols-4 gap-6 mb-8">
            <div class="bg-blue-500 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-users text-3xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-semibold">Total Students</h3>
                        <p class="text-2xl font-bold">{{ .Stats.TotalStudents }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-green-500 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-building text-3xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-semibold">Departments</h3>
                        <p class="text-2xl font-bold">{{ .Stats.TotalDepartments }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-yellow-500 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-clock text-3xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-semibold">Pending Enrollments</h3>
                        <p class="text-2xl font-bold">{{ .Stats.PendingEnrollments }}</p>
                    </div>
                </div>
            </div>
            
            <div class="bg-purple-500 text-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <i class="fas fa-user-shield text-3xl mr-4"></i>
                    <div>
                        <h3 class="text-lg font-semibold">Department Admins</h3>
                        <p class="text-2xl font-bold">{{ .Stats.TotalAdmins }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <button onclick="showTab('departments')" id="departments-tab" 
                    class="tab-button active whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Departments
                </button>
                <button onclick="showTab('users')" id="users-tab" 
                    class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Department Admins
                </button>
                <button onclick="showTab('enrollments')" id="enrollments-tab" 
                    class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    Pending Enrollments
                </button>
                <button onclick="showTab('students')" id="students-tab" 
                    class="tab-button whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    All Students
                </button>
            </nav>
        </div>

        <!-- Departments Tab -->
        <div id="departments-content" class="tab-content">
            <div class="bg-white shadow-lg rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold">Departments</h2>
                    <button onclick="showCreateDepartmentModal()" 
                        class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
                        <i class="fas fa-plus mr-2"></i>Add Department
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ range .Departments }}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ .Name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Code }}</td>
                                <td class="px-6 py-4 text-sm text-gray-500">{{ .Description }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="editDepartment({{ .ID }})" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                    <button onclick="deleteDepartment({{ .ID }})" class="text-red-600 hover:text-red-900">Delete</button>
                                </td>
                            </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Users Tab -->
        <div id="users-content" class="tab-content hidden">
            <div class="bg-white shadow-lg rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold">Department Admins</h2>
                    <button onclick="showCreateUserModal()" 
                        class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600">
                        <i class="fas fa-user-plus mr-2"></i>Add Admin
                    </button>
                </div>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ range .Users }}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ .Email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Role }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ if .Department }}{{ .Department.Name }}{{ else }}N/A{{ end }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {{ if .IsActive }}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                    {{ else }}
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                                    {{ end }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="editUser({{ .ID }})" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                                    <button onclick="toggleUserStatus({{ .ID }})" class="text-yellow-600 hover:text-yellow-900 mr-3">
                                        {{ if .IsActive }}Deactivate{{ else }}Activate{{ end }}
                                    </button>
                                    <button onclick="deleteUser({{ .ID }})" class="text-red-600 hover:text-red-900">Delete</button>
                                </td>
                            </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Enrollments Tab -->
        <div id="enrollments-content" class="tab-content hidden">
            <div class="bg-white shadow-lg rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-6">Pending Enrollments</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Applied Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ range .PendingEnrollments }}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ if .Student }}{{ .Student.Name }}{{ end }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ if .Department }}{{ .Department.Name }}{{ end }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Subject }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Session }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .CreatedAt.Format "2006-01-02" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="viewEnrollment({{ .ID }})" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                    <button onclick="approveEnrollment({{ .ID }})" class="text-green-600 hover:text-green-900 mr-3">Approve</button>
                                    <button onclick="rejectEnrollment({{ .ID }})" class="text-red-600 hover:text-red-900">Reject</button>
                                </td>
                            </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Students Tab -->
        <div id="students-content" class="tab-content hidden">
            <div class="bg-white shadow-lg rounded-lg p-6">
                <h2 class="text-xl font-semibold mb-6">All Students</h2>
                
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mobile</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {{ range .Students }}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ .Name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .Email }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .MobileNumber }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ if eq .Status "registered" }}bg-blue-100 text-blue-800{{ end }}
                                        {{ if eq .Status "enrolled" }}bg-yellow-100 text-yellow-800{{ end }}
                                        {{ if eq .Status "approved" }}bg-green-100 text-green-800{{ end }}
                                        {{ if eq .Status "rejected" }}bg-red-100 text-red-800{{ end }}">
                                        {{ .GetStatusDisplay }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ .CreatedAt.Format "2006-01-02" }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <button onclick="viewStudent({{ .ID }})" class="text-blue-600 hover:text-blue-900 mr-3">View</button>
                                    <button onclick="editStudent({{ .ID }})" class="text-green-600 hover:text-green-900">Edit</button>
                                </td>
                            </tr>
                            {{ end }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.add('hidden');
    });
    
    // Remove active class from all tabs
    document.querySelectorAll('.tab-button').forEach(button => {
        button.classList.remove('active', 'border-blue-500', 'text-blue-600');
        button.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
    });
    
    // Show selected tab content
    document.getElementById(tabName + '-content').classList.remove('hidden');
    
    // Add active class to selected tab
    const activeTab = document.getElementById(tabName + '-tab');
    activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
    activeTab.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
}

// Modal and action functions would be implemented here
function showCreateDepartmentModal() {
    // Implementation for creating department
    alert('Create Department Modal - To be implemented');
}

function showCreateUserModal() {
    // Implementation for creating user
    alert('Create User Modal - To be implemented');
}

function editDepartment(id) {
    alert('Edit Department ' + id + ' - To be implemented');
}

function deleteDepartment(id) {
    if (confirm('Are you sure you want to delete this department?')) {
        // Implementation for deleting department
        alert('Delete Department ' + id + ' - To be implemented');
    }
}

function editUser(id) {
    alert('Edit User ' + id + ' - To be implemented');
}

function toggleUserStatus(id) {
    alert('Toggle User Status ' + id + ' - To be implemented');
}

function deleteUser(id) {
    if (confirm('Are you sure you want to delete this user?')) {
        alert('Delete User ' + id + ' - To be implemented');
    }
}

function viewEnrollment(id) {
    alert('View Enrollment ' + id + ' - To be implemented');
}

function approveEnrollment(id) {
    alert('Approve Enrollment ' + id + ' - To be implemented');
}

function rejectEnrollment(id) {
    alert('Reject Enrollment ' + id + ' - To be implemented');
}

function viewStudent(id) {
    alert('View Student ' + id + ' - To be implemented');
}

function editStudent(id) {
    alert('Edit Student ' + id + ' - To be implemented');
}

// Initialize with departments tab active
document.addEventListener('DOMContentLoaded', function() {
    showTab('departments');
});
</script>

<style>
.tab-button.active {
    border-color: #3B82F6;
    color: #2563EB;
}

.tab-button:not(.active) {
    border-color: transparent;
    color: #6B7280;
}

.tab-button:not(.active):hover {
    color: #374151;
    border-color: #D1D5DB;
}
</style>
{{ end }}
