package main

import (
	"bytes"
	"html/template"
	"testing"
)

// Test that templates can be parsed successfully
func TestTemplatesParsing(t *testing.T) {
	tests := []struct {
		name      string
		templates []string
	}{
		{
			name:      "update_form template",
			templates: []string{"templates/base.html", "templates/update_form.html"},
		},
		{
			name:      "update_form_simple template",
			templates: []string{"templates/base.html", "templates/update_form_simple.html"},
		},
		{
			name:      "search_form template",
			templates: []string{"templates/base.html", "templates/search_form.html"},
		},
		{
			name:      "generate template",
			templates: []string{"templates/base.html", "templates/generate.html"},
		},
		{
			name:      "form template",
			templates: []string{"templates/base.html", "templates/form.html"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := template.ParseFiles(tt.templates...)
			if err != nil {
				t.<PERSON>("Failed to parse templates %v: %v", tt.templates, err)
			}
		})
	}
}

// Test that the update handler template data structure works
func TestUpdateTemplateData(t *testing.T) {
	// This tests the data structure used in the update handler
	type UpdateData struct {
		Title           string
		IsAuthenticated bool
		Student         interface{} // Using interface{} to avoid import issues
		Error           string
		Success         string
		SearchType      string
		SearchValue     string
	}

	data := UpdateData{
		Title:           "Update Student Record",
		IsAuthenticated: true,
		Student:         nil,
		Error:           "",
		Success:         "",
		SearchType:      "exam_roll",
		SearchValue:     "12345",
	}

	tmpl, err := template.ParseFiles("templates/base.html", "templates/update_form.html")
	if err != nil {
		t.Fatalf("Failed to parse templates: %v", err)
	}

	// Test that we can execute the template with our data
	// We'll use a buffer to capture output
	var buf bytes.Buffer
	err = tmpl.ExecuteTemplate(&buf, "base.html", data)
	if err != nil {
		t.Errorf("Failed to execute template: %v", err)
	}

	// Check that some content was generated
	if buf.Len() == 0 {
		t.Error("Template execution produced no output")
	}
}
