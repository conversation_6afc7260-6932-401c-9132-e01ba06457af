# Test Configuration File
# This file contains configuration for the test environment

# Test Database Configuration
TEST_DB_HOST=localhost
TEST_DB_PORT=5433
TEST_DB_USER=balena
TEST_DB_PASSWORD=
TEST_DB_NAME=balena_test

# Test Application Configuration
APP_USERNAME=test_admin
APP_PASSWORD=test_password
JWT_SECRET=test_jwt_secret_for_testing_only_do_not_use_in_production

# Test Environment Settings
GO_ENV=test
LOG_LEVEL=error
APP_ENV=test

# Test Coverage Settings
COVERAGE_THRESHOLD=80
COVERAGE_DIR=coverage

# Test Execution Settings
TEST_TIMEOUT=30s
TEST_PARALLEL=4
TEST_VERBOSE=true

# Database Test Settings
TEST_DB_CLEANUP=true
TEST_DB_MIGRATIONS=true
TEST_DB_SEED=false

# Template Test Settings
TEMPLATE_DIR=templates
TEMPLATE_TEST_DATA=tests/testdata

# Integration Test Settings
INTEGRATION_TESTS_ENABLED=true
SKIP_SLOW_TESTS=false

# Mock Settings
USE_MOCKS=false
MOCK_EXTERNAL_APIS=true

# Test Reporting
GENERATE_HTML_COVERAGE=true
GENERATE_XML_COVERAGE=false
UPLOAD_COVERAGE=false

# Performance Test Settings
BENCHMARK_ENABLED=false
BENCHMARK_TIME=10s
BENCHMARK_MEMORY=true

# Security Test Settings
SECURITY_TESTS_ENABLED=true
VULNERABILITY_SCAN=false

# Load Test Settings
LOAD_TESTS_ENABLED=false
LOAD_TEST_DURATION=60s
LOAD_TEST_USERS=10

# Test Data Settings
TEST_DATA_DIR=tests/testdata
GENERATE_TEST_DATA=true
CLEANUP_TEST_DATA=true

# Debugging Settings
DEBUG_TESTS=false
VERBOSE_LOGGING=false
TRACE_ENABLED=false

# CI/CD Settings
CI_MODE=false
FAIL_FAST=false
RETRY_FAILED_TESTS=false
