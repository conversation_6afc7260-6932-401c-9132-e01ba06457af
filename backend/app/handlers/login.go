package handlers

import (
	"app/middleware" // Adjust import path if needed
	"html/template"
	"net/http"
	"os"
	"time"
)

// LoginHandler handles login page rendering and authentication
func LoginHandler(w http.ResponseWriter, r *http.Request) {
	// Get authentication status from middleware context
	isAuthenticated, _ := r.Context().Value(middleware.AuthenticatedKey).(bool)

	// Redirect authenticated users to the dashboard
	if isAuthenticated {
		http.Redirect(w, r, "/generate", http.StatusSeeOther)
		return
	}

	if r.Method == http.MethodGet {
		// Render login.html inside base.html
		tmpl, err := template.ParseFiles("templates/base.html", "templates/login.html")
		if err != nil {
			http.Error(w, "Unable to load template", http.StatusInternalServerError)
			return
		}
		data := struct {
			Title           string
			IsAuthenticated bool
			Error           string
		}{
			Title:           "Login",
			IsAuthenticated: isAuthenticated,
			Error:           "",
		}

		// Render template
		err = tmpl.ExecuteTemplate(w, "base.html", data)
		if err != nil {
			http.Error(w, "Unable to render template", http.StatusInternalServerError)
		}
		return
	}

	if r.Method == http.MethodPost {
		r.ParseForm()
		username := r.Form.Get("username")
		password := r.Form.Get("password")

		// Check credentials
		if username == os.Getenv("APP_USERNAME") && password == os.Getenv("APP_PASSWORD") {
			http.SetCookie(w, &http.Cookie{
				Name:     "session",
				Value:    "authenticated",
				Path:     "/",
				HttpOnly: true,
				Secure:   false,
			})
			http.Redirect(w, r, "/generate", http.StatusSeeOther)
		} else {
			http.Error(w, "Invalid credentials", http.StatusUnauthorized)
		}
	}
}

// LogoutHandler handles user logout
func LogoutHandler(w http.ResponseWriter, r *http.Request) {
	// Clear the session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "session",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Expires:  time.Now().Add(-1 * time.Hour),
	})

	// Redirect to login page
	http.Redirect(w, r, "/login", http.StatusSeeOther)
}
