package handlers

import (
	"database/sql"
	"encoding/json"
	"net/http"
	"time"
)

// HealthStatus represents the health status of the application
type HealthStatus struct {
	Status    string            `json:"status"`
	Timestamp string            `json:"timestamp"`
	Version   string            `json:"version"`
	Services  map[string]string `json:"services"`
}

// HealthHandler handles health check requests
func HealthHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		health := HealthStatus{
			Status:    "healthy",
			Timestamp: time.Now().UTC().Format(time.RFC3339),
			Version:   "1.0.0",
			Services:  make(map[string]string),
		}

		// Check database connectivity
		if db != nil {
			ctx := r.Context()
			err := db.PingContext(ctx)
			if err != nil {
				health.Status = "unhealthy"
				health.Services["database"] = "down"
			} else {
				health.Services["database"] = "up"
			}
		} else {
			health.Status = "unhealthy"
			health.Services["database"] = "not_configured"
		}

		// Set response headers
		w.Header().Set("Content-Type", "application/json")
		
		// Set status code based on health
		if health.Status == "healthy" {
			w.WriteHeader(http.StatusOK)
		} else {
			w.WriteHeader(http.StatusServiceUnavailable)
		}

		// Encode and send response
		if err := json.NewEncoder(w).Encode(health); err != nil {
			http.Error(w, "Failed to encode health status", http.StatusInternalServerError)
			return
		}
	}
}
