package handlers

import (
	"app/data"
	"app/middleware"
	"context"
	"database/sql"
	"html/template"
	"net/http"
	"strconv"
	"time"
)

type UpdateData struct {
	Title           string
	IsAuthenticated bool
	Student         *data.Student
	Error           string
	Success         string
	SearchType      string
	SearchValue     string
}

func UpdateHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// Get authentication status from middleware context
		isAuthenticated, _ := r.Context().Value(middleware.AuthenticatedKey).(bool)

		if !isAuthenticated {
			http.Redirect(w, r, "/login", http.StatusSeeOther)
			return
		}

		switch r.Method {
		case http.MethodGet:
			handleSearchForm(w, r)
		case http.MethodPost:
			if r.FormValue("update") == "true" {
				handleUpdatePost(w, r, db)
			} else {
				handleSearchAndUpdate(w, r, db)
			}
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

// Display the search form
func handleSearchForm(w http.ResponseWriter, r *http.Request) {
	tmpl, err := template.ParseFiles("templates/base.html", "templates/search_form.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}

	isAuthenticated, _ := r.Context().Value(middleware.AuthenticatedKey).(bool)

	viewData := UpdateData{
		Title:           "Search Student",
		IsAuthenticated: isAuthenticated,
	}

	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

// Handle search and display update form
func handleSearchAndUpdate(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	queries := data.New(db)
	ctx := context.Background()

	// Parse form data
	err := r.ParseForm()
	if err != nil {
		handleError(w, r, "Error parsing form data", err)
		return
	}

	searchType := r.FormValue("search_type")
	searchValue := r.FormValue("search_value")

	// Ensure search criteria is provided
	if searchType == "" || searchValue == "" {
		handleError(w, r, "Please select a search type and enter a value", err)
		return
	}

	var studentData data.Student
	switch searchType {
	case "exam_roll":
		studentData, err = queries.GetStudentByUniversityRollNumber(ctx, searchValue)
	case "abc_id":
		studentData, err = queries.GetStudentByABCID(ctx, searchValue)
	case "aadhaar":
		studentData, err = queries.GetStudentByAadhar(ctx, searchValue)
	default:
		handleError(w, r, "Invalid search type", err)
		return
	}

	// Handle student not found error
	if err != nil {
		handleError(w, r, "Student not found", err)
		return
	}

	// Prepare data for rendering
	viewData := UpdateData{
		Title:           "Update Student Record",
		IsAuthenticated: true,
		Student:         &studentData,
		SearchType:      searchType,
		SearchValue:     searchValue, // Retain search value in the form
	}

	// Render the template
	tmpl, err := template.ParseFiles("templates/base.html", "templates/update_form.html")
	if err != nil {
		handleError(w, r, "Unable to load template", err)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

// Handle updating student data
func handleUpdatePost(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	queries := data.New(db)
	ctx := context.Background()

	// Parse form data
	err := r.ParseForm()
	if err != nil {
		handleError(w, r, "Error parsing form data", err)
		return
	}

	// Convert student ID to int32
	idInt, err := strconv.Atoi(r.FormValue("id"))
	if err != nil {
		handleError(w, r, "Invalid student ID", err)
		return
	}
	id := int32(idInt) // Convert to int32

	// Parse date of birth
	dob, err := time.Parse("2006-01-02", r.FormValue("date_of_birth"))
	if err != nil {
		handleError(w, r, "Invalid date format", err)
		return
	}

	// Create student update parameters
	params := data.UpdateStudentParams{
		ID:                   id,
		Name:                 r.FormValue("name"),
		Email:                r.FormValue("email"),
		DateOfBirth:          dob,
		Gender:               r.FormValue("gender"),
		Category:             r.FormValue("category"),
		MobileNumber:         r.FormValue("mobile_number"),
		Session:              r.FormValue("session"),
		Subject:              r.FormValue("subject"),
		ClassRollNumber:      r.FormValue("class_roll_number"),
		UniversityRollNumber: r.FormValue("university_roll_number"),
		RegistrationNumber:   r.FormValue("registration_number"),
		FatherName:           r.FormValue("father_name"),
		MotherName:           r.FormValue("mother_name"),
		Pincode:              r.FormValue("pincode"),
		State:                r.FormValue("state"),
		AadharNumber:         r.FormValue("aadhar_number"),
		AbcID:                r.FormValue("abc_id"),
		AadharMobile:         r.FormValue("aadhar_mobile"),
	}

	// Update student record using SQLC query
	updatedStudent, err := queries.UpdateStudent(ctx, params)
	if err != nil {
		handleError(w, r, "Error updating student record", err)
		return
	}

	// Retrieve search details (if they exist) to retain search state
	searchType := r.FormValue("search_type")
	searchValue := r.FormValue("search_value")

	// Reload the form with updated data
	viewData := UpdateData{
		Title:           "Update Student Record",
		Student:         &updatedStudent,
		Success:         "Student record updated successfully!",
		SearchType:      searchType,  // Retain search type
		SearchValue:     searchValue, // Retain search value
		IsAuthenticated: true,
	}

	// Render the template
	tmpl, err := template.ParseFiles("templates/base.html", "templates/update_form.html")
	if err != nil {
		handleError(w, r, "Unable to load template", err)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

func handleError(w http.ResponseWriter, r *http.Request, message string, err error) {
	searchType := r.FormValue("search_type")
	searchValue := r.FormValue("search_value")

	viewData := UpdateData{
		Title:           "Update Student Record",
		Error:           message + ": " + err.Error(),
		SearchType:      searchType,
		SearchValue:     searchValue,
		IsAuthenticated: true,
	}

	tmpl, err := template.ParseFiles("templates/base.html", "templates/update_form.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}
