package handlers

import (
	"app/middleware" // Adjust this import path
	"html/template"
	"net/http"
)

// PageData holds data to pass to the template
type PageData struct {
	Title           string
	IsAuthenticated bool
}

// IndexHandler serves the index page or redirects to dashboard if logged in
func IndexHandler(w http.ResponseWriter, r *http.Request) {
	// Get authentication status from middleware context
	isAuthenticated, _ := r.Context().Value(middleware.AuthenticatedKey).(bool)

	// Render the index page for unauthenticated users
	data := PageData{
		Title:           "University Information Management System",
		IsAuthenticated: isAuthenticated,
	}

	// Parse the templates
	tmpl, err := template.ParseFiles("templates/base.html", "templates/index.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}

	// Execute template with data
	if err := tmpl.ExecuteTemplate(w, "base.html", data); err != nil {
		http.Error(w, "Unable to render template", http.StatusInternalServerError)
	}
}
