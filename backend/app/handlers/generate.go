package handlers

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"io"
	"net/http"
	"os"
	"os/exec"
	"strings"
	"time"

	"app/data"
	"app/middleware" // Ensure correct import
)

type ExamFormData struct {
	Student        data.Student
	Roll           string
	Category       string
	ExamType       string
	SelectedPapers []string
	Fee            string
}

func GenerateHandler(db *sql.DB) http.HandlerFunc {
	queries := data.New(db)

	return func(w http.ResponseWriter, r *http.Request) {
		// Get authentication status from middleware context
		isAuthenticated, _ := r.Context().Value(middleware.AuthenticatedKey).(bool)

		if !isAuthenticated {
			http.Redirect(w, r, "/login", http.StatusSeeOther)
			return
		}

		switch r.Method {
		case http.MethodGet:
			handleGenerateGet(w, isAuthenticated)
		case http.MethodPost:
			handleGeneratePost(w, r, queries, isAuthenticated)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

func handleGenerateGet(w http.ResponseWriter, isAuthenticated bool) {
	viewData := FormData{
		Title:           "Download Exam Form",
		IsAuthenticated: isAuthenticated,
		Error:           "",
		Success:         "",
	}
	tmpl, err := template.ParseFiles("templates/base.html", "templates/generate.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

func handleGeneratePost(w http.ResponseWriter, r *http.Request, queries *data.Queries, isAuthenticated bool) {
	ctx := context.Background()

	viewData := FormData{
		Title:           "Download Exam Form",
		IsAuthenticated: isAuthenticated,
		Error:           "",
		Success:         "",
	}

	if err := r.ParseForm(); err != nil {
		http.Error(w, "Error parsing form data", http.StatusBadRequest)
		return
	}

	roll := r.Form.Get("roll")
	category := r.Form.Get("category")
	examType := r.Form.Get("examType")
	selectedPapers := r.Form["papers[]"]

	if roll == "" {
		http.Error(w, "Roll number is required", http.StatusBadRequest)
		return
	}

	_, err := queries.GetExamFormByRoll(ctx, roll)
	if err == nil {
		http.Redirect(w, r, "/download?roll="+roll, http.StatusSeeOther)
		return
	} else if err != sql.ErrNoRows {
		http.Error(w, "Database error", http.StatusInternalServerError)
		return
	}

	_, err = queries.GetStudentByUniversityRollNumber(ctx, roll)
	if err != nil {
		http.Error(w, "No student found for this roll number", http.StatusNotFound)
		return
	}

	paperI, paperII, paperIII, paperIV := false, false, false, false
	for _, paper := range selectedPapers {
		switch paper {
		case "I":
			paperI = true
		case "II":
			paperII = true
		case "III":
			paperIII = true
		case "IV":
			paperIV = true
		}
	}

	_, err = queries.CreateExamForm(ctx, data.CreateExamFormParams{
		RollNumber: roll,
		Category:   category,
		IsRegular:  examType == "Regular",
		PaperI:     paperI,
		PaperIi:    paperII,
		PaperIii:   paperIII,
		PaperIv:    paperIV,
		Fee:        "1000.00",
	})
	if err != nil {
		http.Error(w, "Error saving exam form", http.StatusInternalServerError)
		return
	}

	viewData.Success = "Exam form generated successfully"
	viewData.Roll = roll
	tmpl, err := template.ParseFiles("templates/base.html", "templates/generate.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

// renderPartialTemplate renders just the form part for HTMX requests
func renderPartialTemplate(w http.ResponseWriter, data FormData) {
	tmpl, err := template.ParseFiles("templates/base.html", "templates/generate.html")
	if err != nil {
		http.Error(w, "Template error", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", data)
}

// formatDate formats a time.Time into "02 Jan 2006" format
func formatDate(t time.Time) string {
	return t.Format("02 Jan 2006")
}

func CheckFormHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		roll := r.URL.Query().Get("roll")
		if roll == "" {
			http.Error(w, `{"error": "Roll number required"}`, http.StatusBadRequest)
			return
		}

		ctx := context.Background()
		queries := data.New(db) // Create queries instance using db

		_, err := queries.GetExamFormByRoll(ctx, roll)
		if err == nil {
			fmt.Println("✅ Form exists for roll:", roll)
		} else {
			fmt.Println("❌ No form found for roll:", roll)
		}

		response := struct {
			Exists bool `json:"exists"`
		}{
			Exists: err == nil,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	}
}

func DownloadHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		roll := r.URL.Query().Get("roll")
		if roll == "" {
			http.Error(w, "Roll number required", http.StatusBadRequest)
			return
		}

		ctx := context.Background()
		queries := data.New(db) // Create queries instance using db
		examForm, err := queries.GetExamFormByRoll(ctx, roll)
		if err != nil {
			http.Error(w, "Form not found", http.StatusNotFound)
			return
		}

		// Fetch student details
		student, err := queries.GetStudentByUniversityRollNumber(ctx, examForm.RollNumber)
		if err != nil {
			http.Error(w, "Student not found", http.StatusNotFound)
			return
		}

		// Determine exam type
		examType := "Regular"
		if !examForm.IsRegular {
			examType = "Ex"
		}

		// Get selected papers
		var selectedPapers []string
		if examForm.PaperI {
			selectedPapers = append(selectedPapers, "I")
		}
		if examForm.PaperIi {
			selectedPapers = append(selectedPapers, "II")
		}
		if examForm.PaperIii {
			selectedPapers = append(selectedPapers, "III")
		}
		if examForm.PaperIv {
			selectedPapers = append(selectedPapers, "IV")
		}

		// Prepare data for template
		examFormData := ExamFormData{
			Student:        student,
			Roll:           examForm.RollNumber,
			Category:       examForm.Category,
			ExamType:       examType,
			SelectedPapers: selectedPapers,
			Fee:            examForm.Fee,
		}

		// Generate PDF from stored data
		tpl, err := template.New("exam_form.html").Funcs(template.FuncMap{
			"formatDate": formatDate,
		}).ParseFiles("templates/exam_form.html")
		if err != nil {
			fmt.Println(err)
			return
		}

		var htmlBuffer bytes.Buffer
		err = tpl.Execute(&htmlBuffer, examFormData)
		if err != nil {
			http.Error(w, "Template execution error", http.StatusInternalServerError)
			return
		}

		pr, pw := io.Pipe()
		go func() {
			defer pw.Close()
			cmd := exec.Command("weasyprint", "-", "-")
			cmd.Stdin = strings.NewReader(htmlBuffer.String())
			cmd.Stdout = pw
			cmd.Stderr = os.Stderr
			if err := cmd.Run(); err != nil {
				http.Error(w, "PDF generation failed", http.StatusInternalServerError)
				pw.CloseWithError(err)
			}
		}()

		w.Header().Set("Content-Type", "application/pdf")
		w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"exam_form_%s.pdf\"", roll))
		io.Copy(w, pr)
	}
}
