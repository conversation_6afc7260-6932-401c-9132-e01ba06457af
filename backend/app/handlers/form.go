// handlers/form.go
package handlers

import (
	"app/data"
	"context"
	"database/sql"
	"errors"
	"fmt"
	"html/template"
	"net/http"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgconn"
)

type FormData struct {
	Title           string
	IsAuthenticated bool
	Roll            string
	Error           string
	Success         string
}

func FormHandler(db *sql.DB) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		switch r.Method {
		case http.MethodGet:
			handleGet(w, r)
		case http.MethodPost:
			fmt.Println("POST method activated")
			handlePost(w, r, db)
		default:
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		}
	}
}

func handleGet(w http.ResponseWriter, r *http.Request) {
	viewData := FormData{
		Title:           "Student Registration Form",
		IsAuthenticated: false,
		Error:           "",
	}

	// Check for success message in URL query
	if success := r.URL.Query().Get("success"); success != "" {
		viewData.Success = success
	}

	tmpl, err := template.ParseFiles("templates/base.html", "templates/form.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", viewData)
}

func handlePost(w http.ResponseWriter, r *http.Request, db *sql.DB) {
	queries := data.New(db)
	viewData := FormData{
		Title:           "Student Registration Form",
		IsAuthenticated: false,
		Error:           "",
	}

	ctx := context.Background()
	err := r.ParseForm()
	if err != nil {
		viewData.Error = "Error parsing form data"
		renderTemplate(w, viewData)
		return
	}

	// Parse date of birth
	dob, err := time.Parse("2006-01-02", r.FormValue("date_of_birth"))
	if err != nil {
		viewData.Error = "Invalid date format for date of birth"
		renderTemplate(w, viewData)
		return
	}

	// Get form values
	params := data.CreateStudentParams{
		Name:                 r.FormValue("name"),
		Email:                r.FormValue("email"),
		DateOfBirth:          dob,
		Gender:               r.FormValue("gender"),
		Category:             r.FormValue("category"),
		MobileNumber:         r.FormValue("mobile_number"),
		Session:              r.FormValue("session"),
		Subject:              r.FormValue("subject"),
		ClassRollNumber:      r.FormValue("class_roll_number"),
		UniversityRollNumber: r.FormValue("university_roll_number"),
		RegistrationNumber:   r.FormValue("registration_number"),
		FatherName:           r.FormValue("father_name"),
		MotherName:           r.FormValue("mother_name"),
		Pincode:              r.FormValue("pincode"),
		State:                r.FormValue("state"),
		AadharNumber:         r.FormValue("aadhar_number"),
		AbcID:                r.FormValue("abc_id"),
		AadharMobile:         r.FormValue("aadhar_mobile"),
	}
	fmt.Println(params)
	// Validate required fields
	if params.Name == "" || params.Email == "" {
		viewData.Error = "Please fill all required fields"
		renderTemplate(w, viewData)
		return
	}

	// Check if email already exists
	_, err = queries.GetStudentByEmail(ctx, params.Email)
	if err == nil {
		viewData.Success = "बधाई हो! आपका फ़ॉर्म जमा हो चुका है। 🎉"
		renderTemplate(w, viewData)
		return
	}

	// Create student record
	_, err = queries.CreateStudent(ctx, params)
	if err != nil {
		// Print full error for debugging
		fmt.Printf("Full Error: %+v\n", err)

		// Check if it's a unique constraint violation
		var pgErr *pgconn.PgError
		if errors.As(err, &pgErr) {
			// PostgreSQL error detected
			fmt.Println("PostgreSQL Error Code:", pgErr.Code)
			if pgErr.Code == "23505" { // Unique violation
				fmt.Println("Error: Student record already exists")
				viewData.Success = "बधाई हो! आपका फ़ॉर्म जमा हो चुका है। 🎉"
			} else {
				fmt.Println("Other PG error:", pgErr.Message)
				viewData.Error = "डेटाबेस में एक त्रुटि हुई। कृपया पुनः प्रयास करें।"
			}
		} else if strings.Contains(err.Error(), "duplicate key value violates unique constraint") {
			// Error is wrapped or returned in a different format
			fmt.Println("Error: Duplicate entry detected")
			viewData.Success = "बधाई हो! आपका फ़ॉर्म जमा हो चुका है। 🎉"
		} else {
			// Some other error
			fmt.Println("Non-PG error:", err)
			viewData.Error = "Error creating student record"
		}

		renderTemplate(w, viewData)
		return
	}

	// Redirect on success
	http.Redirect(w, r, "/form?success=Registration+successful", http.StatusSeeOther)
}

func renderTemplate(w http.ResponseWriter, data FormData) {
	tmpl, err := template.ParseFiles("templates/base.html", "templates/form.html")
	if err != nil {
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}
	tmpl.ExecuteTemplate(w, "base.html", data)
}
