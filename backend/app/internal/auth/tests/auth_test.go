package auth_test

import (
	"context"
	"testing"
	"time"

	"app/internal/auth"
	"app/tests/testutils"
)

func TestInMemorySessionStore(t *testing.T) {
	store := auth.NewInMemorySessionStore()
	ctx := context.Background()

	t.Run("create and get session", func(t *testing.T) {
		session := &auth.Session{
			Token:     "test_token",
			Username:  "test_user",
			CreatedAt: time.Now(),
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := store.Create(ctx, session)
		testutils.AssertNoError(t, err)

		retrieved, err := store.Get(ctx, "test_token")
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, session.Token, retrieved.Token)
		testutils.AssertEqual(t, session.Username, retrieved.Username)
	})

	t.Run("get non-existent session", func(t *testing.T) {
		_, err := store.Get(ctx, "non_existent_token")
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "session not found")
	})

	t.Run("get expired session", func(t *testing.T) {
		expiredSession := &auth.Session{
			Token:     "expired_token",
			Username:  "test_user",
			CreatedAt: time.Now().Add(-2 * time.Hour),
			ExpiresAt: time.Now().Add(-time.Hour), // Expired 1 hour ago
		}

		err := store.Create(ctx, expiredSession)
		testutils.AssertNoError(t, err)

		_, err = store.Get(ctx, "expired_token")
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "session expired")
	})

	t.Run("delete session", func(t *testing.T) {
		session := &auth.Session{
			Token:     "delete_token",
			Username:  "test_user",
			CreatedAt: time.Now(),
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := store.Create(ctx, session)
		testutils.AssertNoError(t, err)

		err = store.Delete(ctx, "delete_token")
		testutils.AssertNoError(t, err)

		_, err = store.Get(ctx, "delete_token")
		testutils.AssertError(t, err)
	})

	t.Run("cleanup expired sessions", func(t *testing.T) {
		// Create expired session
		expiredSession := &auth.Session{
			Token:     "cleanup_expired",
			Username:  "test_user",
			CreatedAt: time.Now().Add(-2 * time.Hour),
			ExpiresAt: time.Now().Add(-time.Hour),
		}

		// Create valid session
		validSession := &auth.Session{
			Token:     "cleanup_valid",
			Username:  "test_user",
			CreatedAt: time.Now(),
			ExpiresAt: time.Now().Add(time.Hour),
		}

		err := store.Create(ctx, expiredSession)
		testutils.AssertNoError(t, err)

		err = store.Create(ctx, validSession)
		testutils.AssertNoError(t, err)

		// Run cleanup
		err = store.Cleanup(ctx)
		testutils.AssertNoError(t, err)

		// Expired session should be gone
		_, err = store.Get(ctx, "cleanup_expired")
		testutils.AssertError(t, err)

		// Valid session should still exist
		_, err = store.Get(ctx, "cleanup_valid")
		testutils.AssertNoError(t, err)
	})
}

func TestAuthService(t *testing.T) {
	cfg := testutils.TestConfig()
	logger := testutils.TestLogger()
	authService := auth.NewAuthService(cfg, logger)
	ctx := context.Background()

	t.Run("successful login", func(t *testing.T) {
		token, err := authService.Login(ctx, cfg.Auth.AdminUsername, cfg.Auth.AdminPassword)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", token)

		// Verify token is valid
		valid, err := authService.ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)
	})

	t.Run("invalid username", func(t *testing.T) {
		_, err := authService.Login(ctx, "invalid_user", cfg.Auth.AdminPassword)
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "invalid credentials")
	})

	t.Run("invalid password", func(t *testing.T) {
		_, err := authService.Login(ctx, cfg.Auth.AdminUsername, "invalid_password")
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "invalid credentials")
	})

	t.Run("validate invalid session", func(t *testing.T) {
		valid, err := authService.ValidateSession(ctx, "invalid_token")
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)
	})

	t.Run("validate empty session", func(t *testing.T) {
		valid, err := authService.ValidateSession(ctx, "")
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)
	})

	t.Run("logout", func(t *testing.T) {
		// Login first
		token, err := authService.Login(ctx, cfg.Auth.AdminUsername, cfg.Auth.AdminPassword)
		testutils.AssertNoError(t, err)

		// Verify session is valid
		valid, err := authService.ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)

		// Logout
		err = authService.Logout(ctx, token)
		testutils.AssertNoError(t, err)

		// Verify session is no longer valid
		valid, err = authService.ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)
	})

	t.Run("logout with empty token", func(t *testing.T) {
		err := authService.Logout(ctx, "")
		testutils.AssertNoError(t, err) // Should not error
	})

	t.Run("create session", func(t *testing.T) {
		token, err := authService.CreateSession(ctx, "test_user")
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", token)

		// Verify session is valid
		valid, err := authService.ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)

		// Get session user
		username, err := authService.GetSessionUser(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, "test_user", username)
	})

	t.Run("refresh session", func(t *testing.T) {
		// Create initial session
		oldToken, err := authService.CreateSession(ctx, "test_user")
		testutils.AssertNoError(t, err)

		// Refresh session
		newToken, err := authService.RefreshSession(ctx, oldToken)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", newToken)
		testutils.AssertNotEqual(t, oldToken, newToken)

		// Old token should be invalid
		valid, err := authService.ValidateSession(ctx, oldToken)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)

		// New token should be valid
		valid, err = authService.ValidateSession(ctx, newToken)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)

		// Username should be preserved
		username, err := authService.GetSessionUser(ctx, newToken)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, "test_user", username)
	})

	t.Run("refresh invalid session", func(t *testing.T) {
		_, err := authService.RefreshSession(ctx, "invalid_token")
		testutils.AssertError(t, err)
	})

	t.Run("get session user for invalid token", func(t *testing.T) {
		_, err := authService.GetSessionUser(ctx, "invalid_token")
		testutils.AssertError(t, err)
	})
}

func TestSessionTokenGeneration(t *testing.T) {
	cfg := testutils.TestConfig()
	logger := testutils.TestLogger()
	authService := auth.NewAuthService(cfg, logger)
	ctx := context.Background()

	// Generate multiple tokens and ensure they're unique
	tokens := make(map[string]bool)
	for i := 0; i < 100; i++ {
		token, err := authService.CreateSession(ctx, "test_user")
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", token)

		// Check token is unique
		if tokens[token] {
			t.Fatalf("Generated duplicate token: %s", token)
		}
		tokens[token] = true

		// Check token length (should be 64 hex characters)
		testutils.AssertEqual(t, 64, len(token))
	}
}

func TestSessionExpiration(t *testing.T) {
	cfg := testutils.TestConfig()
	cfg.Auth.SessionTimeout = 100 * time.Millisecond // Very short timeout for testing
	logger := testutils.TestLogger()
	authService := auth.NewAuthService(cfg, logger)
	ctx := context.Background()

	// Create session
	token, err := authService.CreateSession(ctx, "test_user")
	testutils.AssertNoError(t, err)

	// Session should be valid initially
	valid, err := authService.ValidateSession(ctx, token)
	testutils.AssertNoError(t, err)
	testutils.AssertEqual(t, true, valid)

	// Wait for session to expire
	time.Sleep(150 * time.Millisecond)

	// Session should now be invalid
	valid, err = authService.ValidateSession(ctx, token)
	testutils.AssertNoError(t, err)
	testutils.AssertEqual(t, false, valid)
}
