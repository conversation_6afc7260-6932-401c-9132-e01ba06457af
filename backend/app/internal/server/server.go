package server

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"app/internal/config"
	"app/internal/handlers"
	"app/internal/middleware"
	"app/internal/repository"
	"app/internal/services"

	_ "github.com/lib/pq" // PostgreSQL driver
)

// Server represents the HTTP server
type Server struct {
	config     *config.Config
	logger     *slog.Logger
	httpServer *http.Server
	db         *sql.DB
	repository repository.RepositoryManager
	services   services.ServiceManager
}

// New creates a new server instance
func New(cfg *config.Config, logger *slog.Logger) (*Server, error) {
	// Initialize database connection
	db, err := initDatabase(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// Initialize repository layer
	repo := repository.NewRepository(db)

	// Initialize service layer
	serviceDeps := &services.Dependencies{
		Config:     cfg,
		Repository: repo,
		Logger:     logger,
	}
	svc := services.NewServiceManager(serviceDeps)

	// Initialize HTTP server
	httpServer := &http.Server{
		Addr:         cfg.GetServerAddress(),
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	server := &Server{
		config:     cfg,
		logger:     logger,
		httpServer: httpServer,
		db:         db,
		repository: repo,
		services:   svc,
	}

	// Setup routes
	server.setupRoutes()

	return server, nil
}

// Start starts the HTTP server
func (s *Server) Start(ctx context.Context) error {
	// Start server in a goroutine
	go func() {
		s.logger.Info("Starting HTTP server", "address", s.httpServer.Addr)
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.Error("HTTP server error", "error", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-quit:
		s.logger.Info("Shutdown signal received")
	case <-ctx.Done():
		s.logger.Info("Context cancelled")
	}

	return s.Shutdown(ctx)
}

// Shutdown gracefully shuts down the server
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down server...")

	// Create a context with timeout for shutdown
	shutdownCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Shutdown HTTP server
	if err := s.httpServer.Shutdown(shutdownCtx); err != nil {
		s.logger.Error("HTTP server shutdown error", "error", err)
		return err
	}

	// Close database connection
	if err := s.repository.Close(); err != nil {
		s.logger.Error("Database close error", "error", err)
		return err
	}

	s.logger.Info("Server shutdown complete")
	return nil
}

// HealthCheck performs a health check
func (s *Server) HealthCheck(ctx context.Context) error {
	return s.services.HealthCheck(ctx)
}

// setupRoutes configures the HTTP routes
func (s *Server) setupRoutes() {
	// Initialize handlers
	handlerDeps := &handlers.Dependencies{
		Config:   s.config,
		Services: s.services,
		Logger:   s.logger,
	}

	h := handlers.NewHandlers(handlerDeps)

	// Create router
	mux := http.NewServeMux()

	// Static file serving
	staticDir := s.config.App.StaticDir
	mux.Handle("/assets/", http.StripPrefix("/assets/", http.FileServer(http.Dir(staticDir))))

	// Health check endpoint
	mux.HandleFunc("/health", h.HealthCheck)

	// Public routes (no authentication required)
	mux.Handle("/", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.Index)))
	mux.Handle("/form", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.StudentForm)))

	// New workflow routes
	mux.Handle("/student/register", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.StudentRegistration)))
	mux.Handle("/student/enrollment", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.StudentEnrollment)))
	mux.Handle("/student/status", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.StudentStatus)))
	mux.Handle("/admin/login", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.AdminLogin)))

	// Authentication routes
	mux.Handle("/login", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.Login)))
	mux.Handle("/logout", middleware.AuthStatusMiddleware(s.services.Auth())(http.HandlerFunc(h.Logout)))

	// Protected routes (authentication required)
	// TODO: Fix middleware interface mismatch
	mux.HandleFunc("/admin", h.Admin)
	mux.HandleFunc("/admin/dashboard", h.AdminDashboard)
	mux.HandleFunc("/generate", h.GenerateExamForm)
	mux.HandleFunc("/update", h.UpdateStudent)
	mux.HandleFunc("/check-form", h.CheckForm)
	mux.HandleFunc("/download", h.DownloadForm)

	// API routes (for future API endpoints)
	apiMux := http.NewServeMux()
	// TODO: Fix middleware interface mismatch
	apiMux.HandleFunc("/students", h.StudentsAPI)
	apiMux.HandleFunc("/exam-forms", h.ExamFormsAPI)
	mux.Handle("/api/", http.StripPrefix("/api", apiMux))

	// Apply middleware chain
	var handler http.Handler = mux
	handler = middleware.LoggingMiddleware(s.logger)(handler)
	handler = middleware.CORSMiddleware()(handler)
	handler = middleware.RecoveryMiddleware(s.logger)(handler)

	s.httpServer.Handler = handler
}

// initDatabase initializes the database connection
func initDatabase(cfg *config.Config) (*sql.DB, error) {
	db, err := sql.Open("postgres", cfg.GetDatabaseURL())
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}
