package handlers

import (
	"encoding/json"
	"fmt"
	"html/template"
	"log/slog"
	"net/http"

	"app/internal/config"
	"app/internal/errors"
	"app/internal/middleware"
	"app/internal/models"
	"app/internal/services"
)

// Dependencies holds all dependencies needed by handlers
type Dependencies struct {
	Config   *config.Config
	Services services.ServiceManager
	Logger   *slog.Logger
}

// Handlers contains all HTTP handlers
type Handlers struct {
	deps *Dependencies
}

// PageContext holds common data for all page templates
type PageContext struct {
	Title           string
	CurrentPage     string
	IsAuthenticated bool
	UserRole        string
	UserName        string
	UserEmail       string
	Error           string
	Success         string
	// Additional data for specific pages
	Departments        interface{}
	Students           interface{}
	Users              interface{}
	PendingEnrollments interface{}
	Stats              map[string]interface{}
}

// NewHandlers creates a new handlers instance
func NewHandlers(deps *Dependencies) *Handlers {
	return &Handlers{
		deps: deps,
	}
}

// createPageContext creates a page context with authentication info
func (h *Handlers) createPageContext(title, currentPage string, r *http.Request) PageContext {
	// Check authentication status
	isAuthenticated := middleware.IsAuthenticated(r.Context())

	// Get user info if authenticated
	userRole := ""
	userName := ""
	userEmail := ""

	if isAuthenticated {
		if user, ok := middleware.GetAuthenticatedUser(r.Context()); ok {
			userName = user
			userEmail = user // For now, using username as email
			// TODO: Get actual user role from database/service
			userRole = "admin" // Default role for now
		}
	}

	return PageContext{
		Title:           title,
		CurrentPage:     currentPage,
		IsAuthenticated: isAuthenticated,
		UserRole:        userRole,
		UserName:        userName,
		UserEmail:       userEmail,
		Error:           r.URL.Query().Get("error"),
		Success:         r.URL.Query().Get("success"),
	}
}

// HealthCheck handles health check requests
func (h *Handlers) HealthCheck(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Perform health check
	if err := h.deps.Services.HealthCheck(r.Context()); err != nil {
		h.deps.Logger.Error("Health check failed", "error", err)
		http.Error(w, "Service unavailable", http.StatusServiceUnavailable)
		return
	}

	response := map[string]interface{}{
		"status":      "healthy",
		"service":     "University Information Management System",
		"version":     "2.0.0",
		"environment": h.deps.Config.App.Environment,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// Index handles the home page
func (h *Handlers) Index(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := h.createPageContext("University Information Management System", "home", r)
	h.renderTemplate(w, "index.html", data)
}

// StudentForm handles student registration form
func (h *Handlers) StudentForm(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleStudentFormGet(w, r)
	case http.MethodPost:
		h.handleStudentFormPost(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// Login handles user authentication
func (h *Handlers) Login(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleLoginGet(w, r)
	case http.MethodPost:
		h.handleLoginPost(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// Logout handles user logout
func (h *Handlers) Logout(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get session cookie
	cookie, err := r.Cookie("session")
	if err == nil {
		// Logout from service
		h.deps.Services.Auth().Logout(r.Context(), cookie.Value)
	}

	// Clear session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "session",
		Value:    "",
		Path:     "/",
		MaxAge:   -1,
		HttpOnly: true,
		Secure:   h.deps.Config.Auth.CookieSecure,
	})

	http.Redirect(w, r, "/", http.StatusSeeOther)
}

// Admin handles admin dashboard
func (h *Handlers) Admin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	user, _ := middleware.GetAuthenticatedUser(r.Context())

	data := struct {
		Title           string
		IsAuthenticated bool
		User            string
	}{
		Title:           "Admin Dashboard",
		IsAuthenticated: true,
		User:            user,
	}

	h.renderTemplate(w, "admin.html", data)
}

// GenerateExamForm handles exam form generation
func (h *Handlers) GenerateExamForm(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleGenerateExamFormGet(w, r)
	case http.MethodPost:
		h.handleGenerateExamFormPost(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// UpdateStudent handles student record updates
func (h *Handlers) UpdateStudent(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleUpdateStudentGet(w, r)
	case http.MethodPost:
		h.handleUpdateStudentPost(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// CheckForm handles form status checking
func (h *Handlers) CheckForm(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	rollNumber := r.URL.Query().Get("roll")
	if rollNumber == "" {
		h.writeJSONError(w, errors.BadRequest("Roll number required"), http.StatusBadRequest)
		return
	}

	// Check if exam form exists
	examForm, err := h.deps.Services.ExamForm().GetExamFormByRollNumber(r.Context(), rollNumber)
	if err != nil {
		if errors.IsAppError(err) {
			appErr := errors.GetAppError(err)
			if appErr.Code == errors.ErrCodeFormNotFound {
				h.writeJSONError(w, errors.FormNotFound("Exam form not found"), http.StatusNotFound)
				return
			}
		}
		h.writeJSONError(w, err, http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"exists":      true,
		"roll_number": examForm.RollNumber,
		"category":    examForm.Category,
		"exam_type":   examForm.GetExamType(),
		"papers":      examForm.GetSelectedPapers(),
		"fee":         examForm.Fee,
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// DownloadForm handles form download
func (h *Handlers) DownloadForm(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	rollNumber := r.URL.Query().Get("roll")
	if rollNumber == "" {
		http.Error(w, "Roll number required", http.StatusBadRequest)
		return
	}

	// Get exam form with student data
	examFormWithStudent, err := h.deps.Services.ExamForm().GenerateExamFormWithStudent(r.Context(), rollNumber)
	if err != nil {
		h.deps.Logger.Error("Failed to get exam form with student", "rollNumber", rollNumber, "error", err)
		http.Error(w, "Failed to get exam form", http.StatusInternalServerError)
		return
	}

	// Generate PDF (placeholder for now)
	pdfData, err := h.deps.Services.PDF().GenerateExamForm(r.Context(), examFormWithStudent)
	if err != nil {
		h.deps.Logger.Error("Failed to generate PDF", "rollNumber", rollNumber, "error", err)
		http.Error(w, "Failed to generate PDF", http.StatusInternalServerError)
		return
	}

	// Set headers for PDF download
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", "attachment; filename=exam_form_"+rollNumber+".pdf")
	w.Write(pdfData)
}

// StudentsAPI handles student API endpoints
func (h *Handlers) StudentsAPI(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleGetStudents(w, r)
	case http.MethodPost:
		h.handleCreateStudentAPI(w, r)
	default:
		h.writeJSONError(w, fmt.Errorf("method not allowed"), http.StatusMethodNotAllowed)
	}
}

// ExamFormsAPI handles exam form API endpoints
func (h *Handlers) ExamFormsAPI(w http.ResponseWriter, r *http.Request) {
	switch r.Method {
	case http.MethodGet:
		h.handleGetExamForms(w, r)
	case http.MethodPost:
		h.handleCreateExamFormAPI(w, r)
	default:
		h.writeJSONError(w, fmt.Errorf("method not allowed"), http.StatusMethodNotAllowed)
	}
}

// Helper methods for handlers
func (h *Handlers) handleStudentFormGet(w http.ResponseWriter, r *http.Request) {
	data := struct {
		Title           string
		IsAuthenticated bool
		Error           string
		Success         string
	}{
		Title:           "Student Registration Form",
		IsAuthenticated: middleware.IsAuthenticated(r.Context()),
		Error:           r.URL.Query().Get("error"),
		Success:         r.URL.Query().Get("success"),
	}
	h.renderTemplate(w, "form.html", data)
}

func (h *Handlers) handleStudentFormPost(w http.ResponseWriter, r *http.Request) {
	// Parse form data
	if err := r.ParseForm(); err != nil {
		h.deps.Logger.Error("Failed to parse form", "error", err)
		http.Redirect(w, r, "/form?error=Invalid+form+data", http.StatusSeeOther)
		return
	}

	// Create student request from form data
	studentReq, err := h.createStudentRequestFromForm(r)
	if err != nil {
		h.deps.Logger.Error("Failed to create student request from form", "error", err)
		http.Redirect(w, r, "/form?error="+err.Error(), http.StatusSeeOther)
		return
	}

	// Save student to database
	savedStudent, err := h.deps.Services.Student().CreateStudent(r.Context(), studentReq)
	if err != nil {
		h.deps.Logger.Error("Failed to save student", "error", err)
		http.Redirect(w, r, "/form?error=Failed+to+save+student+data", http.StatusSeeOther)
		return
	}

	h.deps.Logger.Info("Student registered successfully", "student_id", savedStudent.ID, "email", savedStudent.Email)
	http.Redirect(w, r, "/form?success=Registration+successful", http.StatusSeeOther)
}

func (h *Handlers) handleLoginGet(w http.ResponseWriter, r *http.Request) {
	// Redirect if already authenticated
	if middleware.IsAuthenticated(r.Context()) {
		http.Redirect(w, r, "/admin", http.StatusSeeOther)
		return
	}

	data := struct {
		Title           string
		IsAuthenticated bool
		Error           string
	}{
		Title:           "Admin Login",
		IsAuthenticated: false,
		Error:           r.URL.Query().Get("error"),
	}
	h.renderTemplate(w, "login.html", data)
}

func (h *Handlers) handleLoginPost(w http.ResponseWriter, r *http.Request) {
	username := r.FormValue("username")
	password := r.FormValue("password")

	// Authenticate user
	token, err := h.deps.Services.Auth().Login(r.Context(), username, password)
	if err != nil {
		h.deps.Logger.Warn("Login failed", "username", username, "error", err)
		http.Redirect(w, r, "/login?error=Invalid+credentials", http.StatusSeeOther)
		return
	}

	// Set session cookie
	http.SetCookie(w, &http.Cookie{
		Name:     h.deps.Config.Auth.CookieName,
		Value:    token,
		Path:     "/",
		HttpOnly: h.deps.Config.Auth.CookieHTTPOnly,
		Secure:   h.deps.Config.Auth.CookieSecure,
		MaxAge:   int(h.deps.Config.Auth.SessionTimeout.Seconds()),
	})

	http.Redirect(w, r, "/admin", http.StatusSeeOther)
}

func (h *Handlers) handleGenerateExamFormGet(w http.ResponseWriter, r *http.Request) {
	user, _ := middleware.GetAuthenticatedUser(r.Context())
	data := struct {
		Title           string
		IsAuthenticated bool
		User            string
	}{
		Title:           "Generate Exam Form",
		IsAuthenticated: true,
		User:            user,
	}
	h.renderTemplate(w, "generate.html", data)
}

func (h *Handlers) handleGenerateExamFormPost(w http.ResponseWriter, r *http.Request) {
	// TODO: Implement exam form generation
	h.deps.Logger.Info("Exam form generation requested")
	http.Redirect(w, r, "/generate?success=Form+generated+successfully", http.StatusSeeOther)
}

func (h *Handlers) handleUpdateStudentGet(w http.ResponseWriter, r *http.Request) {
	user, _ := middleware.GetAuthenticatedUser(r.Context())

	data := struct {
		Title           string
		IsAuthenticated bool
		User            string
		Student         *models.Student
		Error           string
		Success         string
	}{
		Title:           "Update Student Record",
		IsAuthenticated: true,
		User:            user,
		Student:         nil, // No student selected initially
		Error:           r.URL.Query().Get("error"),
		Success:         r.URL.Query().Get("success"),
	}
	h.renderTemplate(w, "update_form_simple.html", data)
}

func (h *Handlers) handleUpdateStudentPost(w http.ResponseWriter, r *http.Request) {
	if err := r.ParseForm(); err != nil {
		h.deps.Logger.Error("Failed to parse form", "error", err)
		http.Redirect(w, r, "/update?error=Invalid+form+data", http.StatusSeeOther)
		return
	}

	user, _ := middleware.GetAuthenticatedUser(r.Context())

	// Check if this is a search request or update request
	if r.FormValue("update") == "true" {
		// Handle student update
		h.handleStudentUpdate(w, r, user)
	} else {
		// Handle student search
		h.handleStudentSearch(w, r, user)
	}
}

func (h *Handlers) renderTemplate(w http.ResponseWriter, templateName string, data interface{}) {
	// Build template file paths
	templateDir := h.deps.Config.App.TemplateDir
	basePath := fmt.Sprintf("%s/base.html", templateDir)
	templatePath := fmt.Sprintf("%s/%s", templateDir, templateName)
	navbarPath := fmt.Sprintf("%s/components/navbar.html", templateDir)

	// Parse the templates including the navbar component
	tmpl, err := template.ParseFiles(basePath, templatePath, navbarPath)
	if err != nil {
		h.deps.Logger.Error("Failed to parse templates", "error", err, "template", templateName)
		http.Error(w, "Unable to load template", http.StatusInternalServerError)
		return
	}

	// Set content type
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	// Execute template with data
	if err := tmpl.ExecuteTemplate(w, "base.html", data); err != nil {
		h.deps.Logger.Error("Failed to execute template", "error", err, "template", templateName)
		http.Error(w, "Unable to render template", http.StatusInternalServerError)
		return
	}
}

func (h *Handlers) createStudentRequestFromForm(r *http.Request) (*models.CreateStudentRequest, error) {
	studentReq := &models.CreateStudentRequest{
		Name:         r.FormValue("name"),
		Email:        r.FormValue("email"),
		DateOfBirth:  r.FormValue("date_of_birth"),
		Gender:       r.FormValue("gender"),
		Category:     r.FormValue("category"),
		MobileNumber: r.FormValue("mobile_number"),
		FatherName:   r.FormValue("father_name"),
		MotherName:   r.FormValue("mother_name"),
		Pincode:      r.FormValue("pincode"),
		State:        r.FormValue("state"),
		AadharNumber: r.FormValue("aadhar_number"),
		AbcID:        r.FormValue("abc_id"),
		AadharMobile: r.FormValue("aadhar_mobile"),
	}

	// Validate required fields
	if studentReq.Name == "" || studentReq.Email == "" || studentReq.AadharNumber == "" {
		return nil, fmt.Errorf("required fields missing")
	}

	return studentReq, nil
}

// New handlers for the refactored workflow

// StudentRegistration handles the new student registration page
func (h *Handlers) StudentRegistration(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := h.createPageContext("Student Registration", "register", r)
	h.renderTemplate(w, "student_registration.html", data)
}

// StudentEnrollment handles the new student enrollment page
func (h *Handlers) StudentEnrollment(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get departments for the dropdown
	departments, err := h.deps.Services.Department().GetAllDepartments(r.Context())
	if err != nil {
		h.deps.Logger.Error("Failed to get departments", "error", err)
		http.Error(w, "Failed to load departments", http.StatusInternalServerError)
		return
	}

	data := struct {
		Title           string
		IsAuthenticated bool
		Departments     interface{}
	}{
		Title:           "Student Enrollment",
		IsAuthenticated: false,
		Departments:     departments,
	}

	h.renderTemplate(w, "student_enrollment.html", data)
}

// AdminLogin handles the admin login page
func (h *Handlers) AdminLogin(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := h.createPageContext("Admin Login", "login", r)
	h.renderTemplate(w, "admin_login.html", data)
}

// AdminDashboard handles the new admin dashboard
func (h *Handlers) AdminDashboard(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// Get dashboard data
	studentStats, _ := h.deps.Services.Student().GetStudentStats(r.Context())
	enrollmentStats, _ := h.deps.Services.Enrollment().GetEnrollmentStats(r.Context())
	userStats, _ := h.deps.Services.User().GetUserStats(r.Context())
	departmentStats, _ := h.deps.Services.Department().GetDepartmentStats(r.Context())

	departments, _ := h.deps.Services.Department().GetAllDepartments(r.Context())
	users, _ := h.deps.Services.User().GetAllUsers(r.Context())
	students, _, _ := h.deps.Services.Student().GetAllStudents(r.Context(), 1, 100)
	pendingEnrollments, _ := h.deps.Services.Enrollment().GetPendingEnrollments(r.Context())

	data := struct {
		Title              string
		IsAuthenticated    bool
		Stats              map[string]interface{}
		Departments        interface{}
		Users              interface{}
		Students           interface{}
		PendingEnrollments interface{}
	}{
		Title:           "Admin Dashboard",
		IsAuthenticated: true,
		Stats: map[string]interface{}{
			"TotalStudents":      studentStats["total_students"],
			"TotalDepartments":   departmentStats["total_departments"],
			"PendingEnrollments": enrollmentStats["pending_enrollments"],
			"TotalAdmins":        userStats["department_admins"],
		},
		Departments:        departments,
		Users:              users,
		Students:           students,
		PendingEnrollments: pendingEnrollments,
	}

	h.renderTemplate(w, "admin_dashboard.html", data)
}

// StudentStatus handles the student status page
func (h *Handlers) StudentStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	data := h.createPageContext("Student Status", "status", r)
	h.renderTemplate(w, "student_status.html", data)
}

func (h *Handlers) writeJSONError(w http.ResponseWriter, err error, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	var response map[string]interface{}
	if appErr, ok := err.(*errors.AppError); ok {
		response = map[string]interface{}{
			"error": map[string]interface{}{
				"code":    appErr.Code,
				"message": appErr.Message,
				"details": appErr.Details,
			},
		}
	} else {
		response = map[string]interface{}{
			"error": map[string]interface{}{
				"code":    "UNKNOWN_ERROR",
				"message": err.Error(),
			},
		}
	}

	json.NewEncoder(w).Encode(response)
}

// API helper methods
func (h *Handlers) handleGetStudents(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	page := 1
	pageSize := 20

	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if p, err := fmt.Sscanf(pageStr, "%d", &page); err != nil || p != 1 {
			page = 1
		}
	}

	if sizeStr := r.URL.Query().Get("page_size"); sizeStr != "" {
		if s, err := fmt.Sscanf(sizeStr, "%d", &pageSize); err != nil || s != 1 {
			pageSize = 20
		}
	}

	// Get students from service
	students, total, err := h.deps.Services.Student().GetAllStudents(r.Context(), page, pageSize)
	if err != nil {
		h.deps.Logger.Error("Failed to get students", "error", err)
		h.writeJSONError(w, err, http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"students": students,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	h.writeJSONResponse(w, response, http.StatusOK)
}

func (h *Handlers) handleCreateStudentAPI(w http.ResponseWriter, r *http.Request) {
	var req models.CreateStudentRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.deps.Logger.Error("Failed to decode student request", "error", err)
		h.writeJSONError(w, fmt.Errorf("invalid JSON"), http.StatusBadRequest)
		return
	}

	student, err := h.deps.Services.Student().CreateStudent(r.Context(), &req)
	if err != nil {
		h.deps.Logger.Error("Failed to create student via API", "error", err)
		h.writeJSONError(w, err, http.StatusBadRequest)
		return
	}

	h.writeJSONResponse(w, student, http.StatusCreated)
}

func (h *Handlers) writeJSONResponse(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(data)
}

func (h *Handlers) handleGetExamForms(w http.ResponseWriter, r *http.Request) {
	// Parse pagination parameters
	page := 1
	pageSize := 20

	if pageStr := r.URL.Query().Get("page"); pageStr != "" {
		if p, err := fmt.Sscanf(pageStr, "%d", &page); err != nil || p != 1 {
			page = 1
		}
	}

	if sizeStr := r.URL.Query().Get("page_size"); sizeStr != "" {
		if s, err := fmt.Sscanf(sizeStr, "%d", &pageSize); err != nil || s != 1 {
			pageSize = 20
		}
	}

	// Get exam forms from service
	examForms, total, err := h.deps.Services.ExamForm().GetAllExamForms(r.Context(), page, pageSize)
	if err != nil {
		h.deps.Logger.Error("Failed to get exam forms", "error", err)
		h.writeJSONError(w, err, http.StatusInternalServerError)
		return
	}

	response := map[string]interface{}{
		"exam_forms": examForms,
		"pagination": map[string]interface{}{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": (total + int64(pageSize) - 1) / int64(pageSize),
		},
	}

	h.writeJSONResponse(w, response, http.StatusOK)
}

func (h *Handlers) handleCreateExamFormAPI(w http.ResponseWriter, r *http.Request) {
	var req models.CreateExamFormRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.deps.Logger.Error("Failed to decode exam form request", "error", err)
		h.writeJSONError(w, fmt.Errorf("invalid JSON"), http.StatusBadRequest)
		return
	}

	examForm, err := h.deps.Services.ExamForm().CreateExamForm(r.Context(), &req)
	if err != nil {
		h.deps.Logger.Error("Failed to create exam form via API", "error", err)
		h.writeJSONError(w, err, http.StatusBadRequest)
		return
	}

	h.writeJSONResponse(w, examForm, http.StatusCreated)
}

func (h *Handlers) handleStudentSearch(w http.ResponseWriter, r *http.Request, user string) {
	searchType := r.FormValue("search_type")
	searchValue := r.FormValue("search_value")

	if searchType == "" || searchValue == "" {
		http.Redirect(w, r, "/update?error=Please+select+search+type+and+enter+value", http.StatusSeeOther)
		return
	}

	var student *models.Student
	var err error

	// Search for student based on type
	switch searchType {
	case "exam_roll":
		student, err = h.deps.Services.Student().GetStudentByEmail(r.Context(), searchValue) // Placeholder - need proper method
	case "abc_id":
		// Search by ABC ID - need to implement this method
		students, _, err := h.deps.Services.Student().GetAllStudents(r.Context(), 1, 100)
		if err == nil {
			for _, s := range students {
				if s.AbcID == searchValue {
					student = s
					break
				}
			}
			if student == nil {
				err = fmt.Errorf("student not found")
			}
		}
	case "aadhaar":
		// Search by Aadhaar - need to implement this method
		students, _, err := h.deps.Services.Student().GetAllStudents(r.Context(), 1, 100)
		if err == nil {
			for _, s := range students {
				if s.AadharNumber == searchValue {
					student = s
					break
				}
			}
			if student == nil {
				err = fmt.Errorf("student not found")
			}
		}
	default:
		http.Redirect(w, r, "/update?error=Invalid+search+type", http.StatusSeeOther)
		return
	}

	if err != nil {
		h.deps.Logger.Error("Failed to find student", "searchType", searchType, "searchValue", searchValue, "error", err)
		http.Redirect(w, r, "/update?error=Student+not+found", http.StatusSeeOther)
		return
	}

	// Render the form with found student data
	data := struct {
		Title           string
		IsAuthenticated bool
		User            string
		Student         *models.Student
		Error           string
		Success         string
	}{
		Title:           "Update Student Record",
		IsAuthenticated: true,
		User:            user,
		Student:         student,
		Error:           "",
		Success:         "",
	}
	h.renderTemplate(w, "update_form_simple.html", data)
}

func (h *Handlers) handleStudentUpdate(w http.ResponseWriter, r *http.Request, user string) {
	// TODO: Implement actual student update logic
	h.deps.Logger.Info("Student update requested", "user", user)
	http.Redirect(w, r, "/update?success=Student+updated+successfully", http.StatusSeeOther)
}
