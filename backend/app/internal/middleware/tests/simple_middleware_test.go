package middleware_test

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"app/internal/middleware"
	"app/tests/testutils"
)

// mockAuthService implements services.AuthService for testing
type simpleAuthService struct {
	validTokens map[string]string // token -> username
}

func newSimpleAuthService() *simpleAuthService {
	return &simpleAuthService{
		validTokens: make(map[string]string),
	}
}

func (m *simpleAuthService) Login(ctx context.Context, username, password string) (string, error) {
	return "", nil
}

func (m *simpleAuthService) ValidateSession(ctx context.Context, sessionToken string) (bool, error) {
	_, exists := m.validTokens[sessionToken]
	return exists, nil
}

func (m *simpleAuthService) Logout(ctx context.Context, sessionToken string) error {
	return nil
}

func (m *simpleAuthService) CreateSession(ctx context.Context, username string) (string, error) {
	return "", nil
}

func (m *simpleAuthService) RefreshSession(ctx context.Context, sessionToken string) (string, error) {
	return "", nil
}

func (m *simpleAuthService) GetSessionUser(ctx context.Context, sessionToken string) (string, error) {
	username, exists := m.validTokens[sessionToken]
	if !exists {
		return "", fmt.Errorf("session not found")
	}
	return username, nil
}

func (m *simpleAuthService) addValidToken(token, username string) {
	m.validTokens[token] = username
}

func TestAuthStatusMiddleware_Basic(t *testing.T) {
	mockAuth := newSimpleAuthService()
	mockAuth.addValidToken("valid_token", "test_user")

	middlewareFunc := middleware.AuthStatusMiddleware(mockAuth)

	t.Run("authenticated request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "valid_token",
		})

		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())
			
			testutils.AssertEqual(t, true, isAuth)
			testutils.AssertEqual(t, true, hasUser)
			testutils.AssertEqual(t, "test_user", user)
			
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())
			
			testutils.AssertEqual(t, false, isAuth)
			testutils.AssertEqual(t, false, hasUser)
			testutils.AssertEqual(t, "", user)
			
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})
}

func TestAuthMiddleware_Basic(t *testing.T) {
	mockAuth := newSimpleAuthService()
	mockAuth.addValidToken("valid_token", "test_user")

	middlewareFunc := middleware.AuthMiddleware(mockAuth)

	t.Run("authenticated request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "valid_token",
		})

		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())
			
			testutils.AssertEqual(t, true, isAuth)
			testutils.AssertEqual(t, true, hasUser)
			testutils.AssertEqual(t, "test_user", user)
			
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request redirects", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Fatal("Handler should not be called for unauthenticated request")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusSeeOther, rr.Code)
		testutils.AssertEqual(t, "/login", rr.Header().Get("Location"))
	})
}

func TestLoggingMiddleware_Basic(t *testing.T) {
	logger := testutils.TestLogger()
	middlewareFunc := middleware.LoggingMiddleware(logger)

	t.Run("logs request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("skips health endpoint", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})
}

func TestCORSMiddleware_Basic(t *testing.T) {
	middlewareFunc := middleware.CORSMiddleware()

	t.Run("sets CORS headers", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.Header.Set("Origin", "https://example.com")
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		
		testutils.AssertEqual(t, "https://example.com", rr.Header().Get("Access-Control-Allow-Origin"))
		testutils.AssertEqual(t, "GET, POST, PUT, DELETE, OPTIONS", rr.Header().Get("Access-Control-Allow-Methods"))
		testutils.AssertEqual(t, "Content-Type, Authorization, X-Requested-With", rr.Header().Get("Access-Control-Allow-Headers"))
		testutils.AssertEqual(t, "true", rr.Header().Get("Access-Control-Allow-Credentials"))
	})

	t.Run("handles preflight request", func(t *testing.T) {
		req := httptest.NewRequest("OPTIONS", "/", nil)
		req.Header.Set("Origin", "https://example.com")
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Fatal("Handler should not be called for OPTIONS request")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusNoContent, rr.Code)
	})
}

func TestRecoveryMiddleware_Basic(t *testing.T) {
	logger := testutils.TestLogger()
	middlewareFunc := middleware.RecoveryMiddleware(logger)

	t.Run("recovers from panic", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			panic("test panic")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusInternalServerError, rr.Code)
		testutils.AssertContains(t, rr.Body.String(), "Internal Server Error")
	})

	t.Run("normal request continues", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()
		
		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("OK"))
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
		testutils.AssertEqual(t, "OK", rr.Body.String())
	})
}
