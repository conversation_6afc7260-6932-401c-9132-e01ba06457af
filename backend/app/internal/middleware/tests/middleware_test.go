package middleware_test

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"app/internal/middleware"
	"app/tests/testutils"
)

// mockAuthService implements services.AuthService for testing
type mockAuthService struct {
	validTokens map[string]string // token -> username
}

func newMockAuthService() *mockAuthService {
	return &mockAuthService{
		validTokens: make(map[string]string),
	}
}

func (m *mockAuthService) Login(ctx context.Context, username, password string) (string, error) {
	return "", nil // Not used in middleware tests
}

func (m *mockAuthService) ValidateSession(ctx context.Context, sessionToken string) (bool, error) {
	_, exists := m.validTokens[sessionToken]
	return exists, nil
}

func (m *mockAuthService) Logout(ctx context.Context, sessionToken string) error {
	return nil // Not used in middleware tests
}

func (m *mockAuthService) CreateSession(ctx context.Context, username string) (string, error) {
	return "", nil // Not used in middleware tests
}

func (m *mockAuthService) RefreshSession(ctx context.Context, sessionToken string) (string, error) {
	return "", nil // Not used in middleware tests
}

func (m *mockAuthService) GetSessionUser(ctx context.Context, sessionToken string) (string, error) {
	username, exists := m.validTokens[sessionToken]
	if !exists {
		return "", fmt.Errorf("session not found")
	}
	return username, nil
}

func (m *mockAuthService) addValidToken(token, username string) {
	m.validTokens[token] = username
}

func TestAuthStatusMiddleware(t *testing.T) {
	mockAuth := newMockAuthService()
	mockAuth.addValidToken("valid_token", "test_user")

	middlewareFunc := middleware.AuthStatusMiddleware(mockAuth)

	t.Run("authenticated request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "valid_token",
		})

		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())

			testutils.AssertEqual(t, true, isAuth)
			testutils.AssertEqual(t, true, hasUser)
			testutils.AssertEqual(t, "test_user", user)

			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request - no cookie", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())

			testutils.AssertEqual(t, false, isAuth)
			testutils.AssertEqual(t, false, hasUser)
			testutils.AssertEqual(t, "", user)

			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request - invalid token", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "invalid_token",
		})

		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())

			testutils.AssertEqual(t, false, isAuth)
			testutils.AssertEqual(t, false, hasUser)
			testutils.AssertEqual(t, "", user)

			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request - empty token", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "",
		})

		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			testutils.AssertEqual(t, false, isAuth)
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})
}

func TestAuthMiddleware(t *testing.T) {
	mockAuth := newMockAuthService()
	mockAuth.addValidToken("valid_token", "test_user")

	middlewareFunc := middleware.AuthMiddleware(mockAuth)

	t.Run("authenticated request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "valid_token",
		})

		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			isAuth := middleware.IsAuthenticated(r.Context())
			user, hasUser := middleware.GetAuthenticatedUser(r.Context())

			testutils.AssertEqual(t, true, isAuth)
			testutils.AssertEqual(t, true, hasUser)
			testutils.AssertEqual(t, "test_user", user)

			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("unauthenticated request - no cookie", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Fatal("Handler should not be called for unauthenticated request")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusSeeOther, rr.Code)
		testutils.AssertEqual(t, "/login", rr.Header().Get("Location"))
	})

	t.Run("unauthenticated request - invalid token", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		req.AddCookie(&http.Cookie{
			Name:  "session",
			Value: "invalid_token",
		})

		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Fatal("Handler should not be called for unauthenticated request")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusSeeOther, rr.Code)
		testutils.AssertEqual(t, "/login", rr.Header().Get("Location"))

		// Check that invalid cookie is cleared
		cookies := rr.Result().Cookies()
		found := false
		for _, cookie := range cookies {
			if cookie.Name == "session" {
				found = true
				testutils.AssertEqual(t, "", cookie.Value)
				testutils.AssertEqual(t, -1, cookie.MaxAge)
			}
		}
		testutils.AssertEqual(t, true, found)
	})
}

func TestLoggingMiddleware(t *testing.T) {
	logger := testutils.TestLogger()
	middlewareFunc := middleware.LoggingMiddleware(logger)

	t.Run("logs request", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/test", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
	})

	t.Run("skips excluded paths", func(t *testing.T) {
		excludedPaths := []string{"/assets/", "/static/", "/favicon.ico", "/health"}

		for _, path := range excludedPaths {
			req := httptest.NewRequest("GET", path+"test", nil)
			rr := httptest.NewRecorder()

			handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				w.WriteHeader(http.StatusOK)
			}))

			handler.ServeHTTP(rr, req)
			testutils.AssertEqual(t, http.StatusOK, rr.Code)
		}
	})
}

func TestCORSMiddleware(t *testing.T) {
	middlewareFunc := middleware.CORSMiddleware()

	t.Run("sets CORS headers", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.Header.Set("Origin", "https://example.com")
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)

		testutils.AssertEqual(t, "https://example.com", rr.Header().Get("Access-Control-Allow-Origin"))
		testutils.AssertEqual(t, "GET, POST, PUT, DELETE, OPTIONS", rr.Header().Get("Access-Control-Allow-Methods"))
		testutils.AssertEqual(t, "Content-Type, Authorization, X-Requested-With", rr.Header().Get("Access-Control-Allow-Headers"))
		testutils.AssertEqual(t, "true", rr.Header().Get("Access-Control-Allow-Credentials"))
	})

	t.Run("handles preflight request", func(t *testing.T) {
		req := httptest.NewRequest("OPTIONS", "/", nil)
		req.Header.Set("Origin", "https://example.com")
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			t.Fatal("Handler should not be called for OPTIONS request")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusNoContent, rr.Code)
	})

	t.Run("sets wildcard origin when no origin header", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, "*", rr.Header().Get("Access-Control-Allow-Origin"))
	})
}

func TestRecoveryMiddleware(t *testing.T) {
	logger := testutils.TestLogger()
	middlewareFunc := middleware.RecoveryMiddleware(logger)

	t.Run("recovers from panic", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			panic("test panic")
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusInternalServerError, rr.Code)
		testutils.AssertContains(t, rr.Body.String(), "Internal Server Error")
	})

	t.Run("normal request continues", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
			w.Write([]byte("OK"))
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, http.StatusOK, rr.Code)
		testutils.AssertEqual(t, "OK", rr.Body.String())
	})
}

func TestSecurityMiddleware(t *testing.T) {
	middlewareFunc := middleware.SecurityMiddleware()

	t.Run("sets security headers", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)

		testutils.AssertEqual(t, "nosniff", rr.Header().Get("X-Content-Type-Options"))
		testutils.AssertEqual(t, "DENY", rr.Header().Get("X-Frame-Options"))
		testutils.AssertEqual(t, "1; mode=block", rr.Header().Get("X-XSS-Protection"))
		testutils.AssertEqual(t, "strict-origin-when-cross-origin", rr.Header().Get("Referrer-Policy"))
	})

	t.Run("sets HSTS for HTTPS", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/", nil)
		req.TLS = &tls.ConnectionState{} // Simulate HTTPS
		rr := httptest.NewRecorder()

		handler := middlewareFunc(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusOK)
		}))

		handler.ServeHTTP(rr, req)
		testutils.AssertEqual(t, "max-age=31536000; includeSubDomains", rr.Header().Get("Strict-Transport-Security"))
	})
}
