package errors_test

import (
	"fmt"
	"net/http"
	"testing"

	"app/internal/errors"
	"app/tests/testutils"
)

func TestAppError_Error(t *testing.T) {
	t.Run("error with underlying error", func(t *testing.T) {
		underlyingErr := fmt.Errorf("database connection failed")
		appErr := errors.Wrap(underlyingErr, errors.ErrCodeDatabase, "Failed to connect to database")
		
		expected := "DATABASE_ERROR: Failed to connect to database (database connection failed)"
		testutils.AssertEqual(t, expected, appErr.Error())
	})
	
	t.Run("error without underlying error", func(t *testing.T) {
		appErr := errors.New(errors.ErrCodeNotFound, "Student not found")
		
		expected := "NOT_FOUND: Student not found"
		testutils.AssertEqual(t, expected, appErr.Error())
	})
}

func TestAppError_Unwrap(t *testing.T) {
	underlyingErr := fmt.Errorf("original error")
	appErr := errors.Wrap(underlyingErr, errors.ErrCodeInternal, "Wrapped error")
	
	unwrapped := appErr.Unwrap()
	testutils.AssertEqual(t, underlyingErr, unwrapped)
}

func TestAppError_WithDetails(t *testing.T) {
	appErr := errors.New(errors.ErrCodeValidation, "Validation failed")
	appErr = appErr.WithDetails("Name field is required")
	
	testutils.AssertEqual(t, "Name field is required", appErr.Details)
}

func TestAppError_WithStatusCode(t *testing.T) {
	appErr := errors.New(errors.ErrCodeInternal, "Internal error")
	appErr = appErr.WithStatusCode(http.StatusTeapot)
	
	testutils.AssertEqual(t, http.StatusTeapot, appErr.StatusCode)
}

func TestErrorConstructors(t *testing.T) {
	tests := []struct {
		name           string
		constructor    func() *errors.AppError
		expectedCode   errors.ErrorCode
		expectedStatus int
	}{
		{
			name:           "Internal",
			constructor:    func() *errors.AppError { return errors.Internal("Internal error", fmt.Errorf("underlying")) },
			expectedCode:   errors.ErrCodeInternal,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "NotFound",
			constructor:    func() *errors.AppError { return errors.NotFound("Not found") },
			expectedCode:   errors.ErrCodeNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "BadRequest",
			constructor:    func() *errors.AppError { return errors.BadRequest("Bad request") },
			expectedCode:   errors.ErrCodeBadRequest,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Unauthorized",
			constructor:    func() *errors.AppError { return errors.Unauthorized("Unauthorized") },
			expectedCode:   errors.ErrCodeUnauthorized,
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "Forbidden",
			constructor:    func() *errors.AppError { return errors.Forbidden("Forbidden") },
			expectedCode:   errors.ErrCodeForbidden,
			expectedStatus: http.StatusForbidden,
		},
		{
			name:           "Conflict",
			constructor:    func() *errors.AppError { return errors.Conflict("Conflict") },
			expectedCode:   errors.ErrCodeConflict,
			expectedStatus: http.StatusConflict,
		},
		{
			name:           "Validation",
			constructor:    func() *errors.AppError { return errors.Validation("Validation error") },
			expectedCode:   errors.ErrCodeValidation,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "Database",
			constructor:    func() *errors.AppError { return errors.Database("Database error", fmt.Errorf("db error")) },
			expectedCode:   errors.ErrCodeDatabase,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:           "StudentExists",
			constructor:    func() *errors.AppError { return errors.StudentExists("Student exists") },
			expectedCode:   errors.ErrCodeStudentExists,
			expectedStatus: http.StatusConflict,
		},
		{
			name:           "StudentNotFound",
			constructor:    func() *errors.AppError { return errors.StudentNotFound("Student not found") },
			expectedCode:   errors.ErrCodeStudentNotFound,
			expectedStatus: http.StatusNotFound,
		},
		{
			name:           "FormExists",
			constructor:    func() *errors.AppError { return errors.FormExists("Form exists") },
			expectedCode:   errors.ErrCodeFormExists,
			expectedStatus: http.StatusConflict,
		},
		{
			name:           "FormNotFound",
			constructor:    func() *errors.AppError { return errors.FormNotFound("Form not found") },
			expectedCode:   errors.ErrCodeFormNotFound,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.constructor()
			testutils.AssertEqual(t, tt.expectedCode, err.Code)
			testutils.AssertEqual(t, tt.expectedStatus, err.StatusCode)
		})
	}
}

func TestStatusCodeMapping(t *testing.T) {
	tests := []struct {
		code           errors.ErrorCode
		expectedStatus int
	}{
		{errors.ErrCodeNotFound, http.StatusNotFound},
		{errors.ErrCodeStudentNotFound, http.StatusNotFound},
		{errors.ErrCodeFormNotFound, http.StatusNotFound},
		{errors.ErrCodeBadRequest, http.StatusBadRequest},
		{errors.ErrCodeValidation, http.StatusBadRequest},
		{errors.ErrCodeRequired, http.StatusBadRequest},
		{errors.ErrCodeInvalid, http.StatusBadRequest},
		{errors.ErrCodeUnauthorized, http.StatusUnauthorized},
		{errors.ErrCodeInvalidCredentials, http.StatusUnauthorized},
		{errors.ErrCodeSessionExpired, http.StatusUnauthorized},
		{errors.ErrCodeInvalidToken, http.StatusUnauthorized},
		{errors.ErrCodeForbidden, http.StatusForbidden},
		{errors.ErrCodeConflict, http.StatusConflict},
		{errors.ErrCodeDuplicate, http.StatusConflict},
		{errors.ErrCodeStudentExists, http.StatusConflict},
		{errors.ErrCodeFormExists, http.StatusConflict},
		{errors.ErrCodeInternal, http.StatusInternalServerError},
		{errors.ErrCodeDatabase, http.StatusInternalServerError},
		{errors.ErrCodeConstraint, http.StatusInternalServerError},
	}

	for _, tt := range tests {
		t.Run(string(tt.code), func(t *testing.T) {
			err := errors.New(tt.code, "Test message")
			testutils.AssertEqual(t, tt.expectedStatus, err.StatusCode)
		})
	}
}

func TestIsAppError(t *testing.T) {
	t.Run("is app error", func(t *testing.T) {
		appErr := errors.New(errors.ErrCodeNotFound, "Not found")
		testutils.AssertEqual(t, true, errors.IsAppError(appErr))
	})
	
	t.Run("is not app error", func(t *testing.T) {
		regularErr := fmt.Errorf("regular error")
		testutils.AssertEqual(t, false, errors.IsAppError(regularErr))
	})
}

func TestGetAppError(t *testing.T) {
	t.Run("from app error", func(t *testing.T) {
		originalErr := errors.New(errors.ErrCodeNotFound, "Not found")
		retrievedErr := errors.GetAppError(originalErr)
		
		testutils.AssertEqual(t, originalErr, retrievedErr)
	})
	
	t.Run("from regular error", func(t *testing.T) {
		regularErr := fmt.Errorf("regular error")
		appErr := errors.GetAppError(regularErr)
		
		testutils.AssertEqual(t, errors.ErrCodeInternal, appErr.Code)
		testutils.AssertEqual(t, "An unexpected error occurred", appErr.Message)
		testutils.AssertEqual(t, regularErr, appErr.Err)
	})
}

func TestErrorChaining(t *testing.T) {
	// Create a chain of errors
	originalErr := fmt.Errorf("database connection timeout")
	dbErr := errors.Database("Database operation failed", originalErr)
	serviceErr := errors.Internal("Service unavailable", dbErr)
	
	// Test error messages
	testutils.AssertContains(t, serviceErr.Error(), "Service unavailable")
	testutils.AssertContains(t, serviceErr.Error(), "Database operation failed")
	
	// Test unwrapping
	unwrapped := serviceErr.Unwrap()
	testutils.AssertEqual(t, dbErr, unwrapped)
	
	// Test that it's still an AppError
	testutils.AssertEqual(t, true, errors.IsAppError(serviceErr))
}

func TestErrorDetails(t *testing.T) {
	err := errors.Validation("Validation failed").
		WithDetails("Name is required, Email must be valid").
		WithStatusCode(http.StatusUnprocessableEntity)
	
	testutils.AssertEqual(t, errors.ErrCodeValidation, err.Code)
	testutils.AssertEqual(t, "Validation failed", err.Message)
	testutils.AssertEqual(t, "Name is required, Email must be valid", err.Details)
	testutils.AssertEqual(t, http.StatusUnprocessableEntity, err.StatusCode)
}
