package errors

import (
	"fmt"
	"net/http"
)

// ErrorCode represents different types of application errors
type ErrorCode string

const (
	// General errors
	ErrCodeInternal     ErrorCode = "INTERNAL_ERROR"
	ErrCodeNotFound     ErrorCode = "NOT_FOUND"
	ErrCodeBadRequest   ErrorCode = "BAD_REQUEST"
	ErrCodeUnauthorized ErrorCode = "UNAUTHORIZED"
	ErrCodeForbidden    ErrorCode = "FORBIDDEN"
	ErrCodeConflict     ErrorCode = "CONFLICT"

	// Validation errors
	ErrCodeValidation ErrorCode = "VALIDATION_ERROR"
	ErrCodeRequired   ErrorCode = "REQUIRED_FIELD"
	ErrCodeInvalid    ErrorCode = "INVALID_FORMAT"

	// Database errors
	ErrCodeDatabase     ErrorCode = "DATABASE_ERROR"
	ErrCodeDuplicate    ErrorCode = "DUPLICATE_ENTRY"
	ErrCodeConstraint   ErrorCode = "CONSTRAINT_VIOLATION"

	// Authentication errors
	ErrCodeInvalidCredentials ErrorCode = "INVALID_CREDENTIALS"
	ErrCodeSessionExpired     ErrorCode = "SESSION_EXPIRED"
	ErrCodeInvalidToken       ErrorCode = "INVALID_TOKEN"

	// Business logic errors
	ErrCodeStudentExists    ErrorCode = "STUDENT_EXISTS"
	ErrCodeStudentNotFound  ErrorCode = "STUDENT_NOT_FOUND"
	ErrCodeFormExists       ErrorCode = "FORM_EXISTS"
	ErrCodeFormNotFound     ErrorCode = "FORM_NOT_FOUND"
)

// AppError represents an application error with context
type AppError struct {
	Code       ErrorCode `json:"code"`
	Message    string    `json:"message"`
	Details    string    `json:"details,omitempty"`
	StatusCode int       `json:"-"`
	Err        error     `json:"-"`
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s (%v)", e.Code, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Code, e.Message)
}

// Unwrap returns the underlying error
func (e *AppError) Unwrap() error {
	return e.Err
}

// New creates a new AppError
func New(code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		StatusCode: getStatusCode(code),
	}
}

// Wrap wraps an existing error with additional context
func Wrap(err error, code ErrorCode, message string) *AppError {
	return &AppError{
		Code:       code,
		Message:    message,
		StatusCode: getStatusCode(code),
		Err:        err,
	}
}

// WithDetails adds details to an existing AppError
func (e *AppError) WithDetails(details string) *AppError {
	e.Details = details
	return e
}

// WithStatusCode sets a custom status code
func (e *AppError) WithStatusCode(statusCode int) *AppError {
	e.StatusCode = statusCode
	return e
}

// getStatusCode maps error codes to HTTP status codes
func getStatusCode(code ErrorCode) int {
	switch code {
	case ErrCodeNotFound, ErrCodeStudentNotFound, ErrCodeFormNotFound:
		return http.StatusNotFound
	case ErrCodeBadRequest, ErrCodeValidation, ErrCodeRequired, ErrCodeInvalid:
		return http.StatusBadRequest
	case ErrCodeUnauthorized, ErrCodeInvalidCredentials, ErrCodeSessionExpired, ErrCodeInvalidToken:
		return http.StatusUnauthorized
	case ErrCodeForbidden:
		return http.StatusForbidden
	case ErrCodeConflict, ErrCodeDuplicate, ErrCodeStudentExists, ErrCodeFormExists:
		return http.StatusConflict
	case ErrCodeInternal, ErrCodeDatabase, ErrCodeConstraint:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// Common error constructors
func Internal(message string, err error) *AppError {
	return Wrap(err, ErrCodeInternal, message)
}

func NotFound(message string) *AppError {
	return New(ErrCodeNotFound, message)
}

func BadRequest(message string) *AppError {
	return New(ErrCodeBadRequest, message)
}

func Unauthorized(message string) *AppError {
	return New(ErrCodeUnauthorized, message)
}

func Forbidden(message string) *AppError {
	return New(ErrCodeForbidden, message)
}

func Conflict(message string) *AppError {
	return New(ErrCodeConflict, message)
}

func Validation(message string) *AppError {
	return New(ErrCodeValidation, message)
}

func Database(message string, err error) *AppError {
	return Wrap(err, ErrCodeDatabase, message)
}

func StudentExists(message string) *AppError {
	return New(ErrCodeStudentExists, message)
}

func StudentNotFound(message string) *AppError {
	return New(ErrCodeStudentNotFound, message)
}

func FormExists(message string) *AppError {
	return New(ErrCodeFormExists, message)
}

func FormNotFound(message string) *AppError {
	return New(ErrCodeFormNotFound, message)
}

// IsAppError checks if an error is an AppError
func IsAppError(err error) bool {
	_, ok := err.(*AppError)
	return ok
}

// GetAppError extracts AppError from error, or creates a generic one
func GetAppError(err error) *AppError {
	if appErr, ok := err.(*AppError); ok {
		return appErr
	}
	return Internal("An unexpected error occurred", err)
}
