package config_test

import (
	"os"
	"testing"
	"time"

	"app/internal/config"
	"app/tests/testutils"
)

func TestLoad(t *testing.T) {
	// Save original environment
	originalEnv := make(map[string]string)
	envVars := []string{
		"DB_PASSWORD", "JWT_SECRET", "APP_USERNAME", "APP_PASSWORD",
		"SERVER_PORT", "DB_HOST", "DB_PORT", "DB_USER", "DB_NAME",
		"SESSION_TIMEOUT", "LOG_LEVEL", "APP_ENV",
	}
	
	for _, env := range envVars {
		originalEnv[env] = os.Getenv(env)
	}
	
	// Clean environment
	defer func() {
		for _, env := range envVars {
			if val, exists := originalEnv[env]; exists {
				os.Setenv(env, val)
			} else {
				os.Unsetenv(env)
			}
		}
	}()

	t.Run("valid configuration", func(t *testing.T) {
		// Set required environment variables
		os.Setenv("DB_PASSWORD", "test_password")
		os.Setenv("JWT_SECRET", "test_jwt_secret")
		os.Setenv("APP_USERNAME", "test_admin")
		os.Setenv("APP_PASSWORD", "test_password")
		
		cfg, err := config.Load()
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, nil, cfg)
		
		// Verify required fields are set
		testutils.AssertEqual(t, "test_password", cfg.Database.Password)
		testutils.AssertEqual(t, "test_jwt_secret", cfg.Auth.JWTSecret)
		testutils.AssertEqual(t, "test_admin", cfg.Auth.AdminUsername)
		testutils.AssertEqual(t, "test_password", cfg.Auth.AdminPassword)
	})

	t.Run("missing required DB_PASSWORD", func(t *testing.T) {
		os.Unsetenv("DB_PASSWORD")
		os.Setenv("JWT_SECRET", "test_jwt_secret")
		os.Setenv("APP_USERNAME", "test_admin")
		os.Setenv("APP_PASSWORD", "test_password")
		
		_, err := config.Load()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "DB_PASSWORD is required")
	})

	t.Run("missing required JWT_SECRET", func(t *testing.T) {
		os.Setenv("DB_PASSWORD", "test_password")
		os.Unsetenv("JWT_SECRET")
		os.Setenv("APP_USERNAME", "test_admin")
		os.Setenv("APP_PASSWORD", "test_password")
		
		_, err := config.Load()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "JWT_SECRET is required")
	})

	t.Run("missing required APP_USERNAME", func(t *testing.T) {
		os.Setenv("DB_PASSWORD", "test_password")
		os.Setenv("JWT_SECRET", "test_jwt_secret")
		os.Unsetenv("APP_USERNAME")
		os.Setenv("APP_PASSWORD", "test_password")
		
		_, err := config.Load()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "APP_USERNAME is required")
	})

	t.Run("missing required APP_PASSWORD", func(t *testing.T) {
		os.Setenv("DB_PASSWORD", "test_password")
		os.Setenv("JWT_SECRET", "test_jwt_secret")
		os.Setenv("APP_USERNAME", "test_admin")
		os.Unsetenv("APP_PASSWORD")
		
		_, err := config.Load()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "APP_PASSWORD is required")
	})
}

func TestConfigDefaults(t *testing.T) {
	// Set only required variables
	os.Setenv("DB_PASSWORD", "test_password")
	os.Setenv("JWT_SECRET", "test_jwt_secret")
	os.Setenv("APP_USERNAME", "test_admin")
	os.Setenv("APP_PASSWORD", "test_password")
	
	// Clear optional variables to test defaults
	os.Unsetenv("SERVER_HOST")
	os.Unsetenv("SERVER_PORT")
	os.Unsetenv("DB_HOST")
	os.Unsetenv("DB_PORT")
	os.Unsetenv("DB_USER")
	os.Unsetenv("DB_NAME")
	os.Unsetenv("SESSION_TIMEOUT")
	os.Unsetenv("LOG_LEVEL")
	os.Unsetenv("APP_ENV")
	
	defer func() {
		os.Unsetenv("DB_PASSWORD")
		os.Unsetenv("JWT_SECRET")
		os.Unsetenv("APP_USERNAME")
		os.Unsetenv("APP_PASSWORD")
	}()
	
	cfg, err := config.Load()
	testutils.AssertNoError(t, err)
	
	// Test server defaults
	testutils.AssertEqual(t, "0.0.0.0", cfg.Server.Host)
	testutils.AssertEqual(t, 8080, cfg.Server.Port)
	testutils.AssertEqual(t, 30*time.Second, cfg.Server.ReadTimeout)
	testutils.AssertEqual(t, 30*time.Second, cfg.Server.WriteTimeout)
	testutils.AssertEqual(t, 120*time.Second, cfg.Server.IdleTimeout)
	
	// Test database defaults
	testutils.AssertEqual(t, "localhost", cfg.Database.Host)
	testutils.AssertEqual(t, 5432, cfg.Database.Port)
	testutils.AssertEqual(t, "balena", cfg.Database.User)
	testutils.AssertEqual(t, "balena", cfg.Database.Name)
	testutils.AssertEqual(t, "disable", cfg.Database.SSLMode)
	
	// Test auth defaults
	testutils.AssertEqual(t, 24*time.Hour, cfg.Auth.SessionTimeout)
	testutils.AssertEqual(t, "session", cfg.Auth.CookieName)
	testutils.AssertEqual(t, false, cfg.Auth.CookieSecure)
	testutils.AssertEqual(t, true, cfg.Auth.CookieHTTPOnly)
	
	// Test app defaults
	testutils.AssertEqual(t, "development", cfg.App.Environment)
	testutils.AssertEqual(t, "info", cfg.App.LogLevel)
	testutils.AssertEqual(t, "templates", cfg.App.TemplateDir)
	testutils.AssertEqual(t, "assets", cfg.App.StaticDir)
	testutils.AssertEqual(t, "uploads", cfg.App.UploadDir)
	testutils.AssertEqual(t, int64(10*1024*1024), cfg.App.MaxUploadSize)
}

func TestConfigCustomValues(t *testing.T) {
	// Set custom values
	os.Setenv("DB_PASSWORD", "custom_password")
	os.Setenv("JWT_SECRET", "custom_jwt_secret")
	os.Setenv("APP_USERNAME", "custom_admin")
	os.Setenv("APP_PASSWORD", "custom_password")
	os.Setenv("SERVER_HOST", "127.0.0.1")
	os.Setenv("SERVER_PORT", "9000")
	os.Setenv("DB_HOST", "db.example.com")
	os.Setenv("DB_PORT", "5433")
	os.Setenv("DB_USER", "custom_user")
	os.Setenv("DB_NAME", "custom_db")
	os.Setenv("SESSION_TIMEOUT", "2h")
	os.Setenv("LOG_LEVEL", "debug")
	os.Setenv("APP_ENV", "production")
	
	defer func() {
		envVars := []string{
			"DB_PASSWORD", "JWT_SECRET", "APP_USERNAME", "APP_PASSWORD",
			"SERVER_HOST", "SERVER_PORT", "DB_HOST", "DB_PORT", "DB_USER", "DB_NAME",
			"SESSION_TIMEOUT", "LOG_LEVEL", "APP_ENV",
		}
		for _, env := range envVars {
			os.Unsetenv(env)
		}
	}()
	
	cfg, err := config.Load()
	testutils.AssertNoError(t, err)
	
	// Test custom values
	testutils.AssertEqual(t, "127.0.0.1", cfg.Server.Host)
	testutils.AssertEqual(t, 9000, cfg.Server.Port)
	testutils.AssertEqual(t, "db.example.com", cfg.Database.Host)
	testutils.AssertEqual(t, 5433, cfg.Database.Port)
	testutils.AssertEqual(t, "custom_user", cfg.Database.User)
	testutils.AssertEqual(t, "custom_db", cfg.Database.Name)
	testutils.AssertEqual(t, 2*time.Hour, cfg.Auth.SessionTimeout)
	testutils.AssertEqual(t, "debug", cfg.App.LogLevel)
	testutils.AssertEqual(t, "production", cfg.App.Environment)
}

func TestConfigHelperMethods(t *testing.T) {
	cfg := testutils.TestConfig()
	
	t.Run("GetDatabaseURL", func(t *testing.T) {
		url := cfg.GetDatabaseURL()
		expected := "postgres://balena:test_password@localhost:5432/balena_test?sslmode=disable"
		testutils.AssertEqual(t, expected, url)
	})
	
	t.Run("GetServerAddress", func(t *testing.T) {
		addr := cfg.GetServerAddress()
		expected := "localhost:8080"
		testutils.AssertEqual(t, expected, addr)
	})
	
	t.Run("IsProduction", func(t *testing.T) {
		// Test environment should not be production
		testutils.AssertEqual(t, false, cfg.IsProduction())
		
		// Test production environment
		cfg.App.Environment = "production"
		testutils.AssertEqual(t, true, cfg.IsProduction())
	})
}

func TestConfigValidation(t *testing.T) {
	t.Run("valid config", func(t *testing.T) {
		cfg := testutils.TestConfig()
		err := cfg.Validate()
		testutils.AssertNoError(t, err)
	})
	
	t.Run("missing database password", func(t *testing.T) {
		cfg := testutils.TestConfig()
		cfg.Database.Password = ""
		err := cfg.Validate()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "DB_PASSWORD is required")
	})
	
	t.Run("missing JWT secret", func(t *testing.T) {
		cfg := testutils.TestConfig()
		cfg.Auth.JWTSecret = ""
		err := cfg.Validate()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "JWT_SECRET is required")
	})
	
	t.Run("missing admin username", func(t *testing.T) {
		cfg := testutils.TestConfig()
		cfg.Auth.AdminUsername = ""
		err := cfg.Validate()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "APP_USERNAME is required")
	})
	
	t.Run("missing admin password", func(t *testing.T) {
		cfg := testutils.TestConfig()
		cfg.Auth.AdminPassword = ""
		err := cfg.Validate()
		testutils.AssertError(t, err)
		testutils.AssertContains(t, err.Error(), "APP_PASSWORD is required")
	})
}
