package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	Server   ServerConfig
	Database DatabaseConfig
	Auth     AuthConfig
	App      AppConfig
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Host         string
	Port         int
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// DatabaseConfig holds database-related configuration
type DatabaseConfig struct {
	Host     string
	Port     int
	User     string
	Password string
	Name     string
	SSLMode  string
}

// AuthConfig holds authentication-related configuration
type AuthConfig struct {
	JWTSecret       string
	SessionTimeout  time.Duration
	AdminUsername   string
	AdminPassword   string
	CookieName      string
	CookieSecure    bool
	CookieHTTPOnly  bool
}

// AppConfig holds application-specific configuration
type AppConfig struct {
	Environment       string
	LogLevel          string
	CloudflareToken   string
	TemplateDir       string
	StaticDir         string
	UploadDir         string
	MaxUploadSize     int64
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists (ignore error in production)
	_ = godotenv.Load()

	config := &Config{
		Server: ServerConfig{
			Host:         getEnvString("SERVER_HOST", "0.0.0.0"),
			Port:         getEnvInt("SERVER_PORT", 8080),
			ReadTimeout:  getEnvDuration("SERVER_READ_TIMEOUT", 30*time.Second),
			WriteTimeout: getEnvDuration("SERVER_WRITE_TIMEOUT", 30*time.Second),
			IdleTimeout:  getEnvDuration("SERVER_IDLE_TIMEOUT", 120*time.Second),
		},
		Database: DatabaseConfig{
			Host:     getEnvString("DB_HOST", "localhost"),
			Port:     getEnvInt("DB_PORT", 5432),
			User:     getEnvString("DB_USER", "balena"),
			Password: getEnvString("DB_PASSWORD", ""),
			Name:     getEnvString("DB_NAME", "balena"),
			SSLMode:  getEnvString("DB_SSLMODE", "disable"),
		},
		Auth: AuthConfig{
			JWTSecret:       getEnvString("JWT_SECRET", ""),
			SessionTimeout:  getEnvDuration("SESSION_TIMEOUT", 24*time.Hour),
			AdminUsername:   getEnvString("APP_USERNAME", ""),
			AdminPassword:   getEnvString("APP_PASSWORD", ""),
			CookieName:      getEnvString("COOKIE_NAME", "session"),
			CookieSecure:    getEnvBool("COOKIE_SECURE", false),
			CookieHTTPOnly:  getEnvBool("COOKIE_HTTP_ONLY", true),
		},
		App: AppConfig{
			Environment:     getEnvString("APP_ENV", "development"),
			LogLevel:        getEnvString("LOG_LEVEL", "info"),
			CloudflareToken: getEnvString("CLOUDFLARE_TUNNEL_TOKEN", ""),
			TemplateDir:     getEnvString("TEMPLATE_DIR", "templates"),
			StaticDir:       getEnvString("STATIC_DIR", "assets"),
			UploadDir:       getEnvString("UPLOAD_DIR", "uploads"),
			MaxUploadSize:   getEnvInt64("MAX_UPLOAD_SIZE", 10*1024*1024), // 10MB
		},
	}

	// Validate required configuration
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return config, nil
}

// Validate validates the configuration
func (c *Config) Validate() error {
	if c.Database.Password == "" {
		return fmt.Errorf("DB_PASSWORD is required")
	}
	if c.Auth.JWTSecret == "" {
		return fmt.Errorf("JWT_SECRET is required")
	}
	if c.Auth.AdminUsername == "" {
		return fmt.Errorf("APP_USERNAME is required")
	}
	if c.Auth.AdminPassword == "" {
		return fmt.Errorf("APP_PASSWORD is required")
	}
	return nil
}

// GetDatabaseURL returns the database connection URL
func (c *Config) GetDatabaseURL() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s",
		c.Database.User,
		c.Database.Password,
		c.Database.Host,
		c.Database.Port,
		c.Database.Name,
		c.Database.SSLMode,
	)
}

// GetServerAddress returns the server address
func (c *Config) GetServerAddress() string {
	return fmt.Sprintf("%s:%d", c.Server.Host, c.Server.Port)
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.App.Environment == "production"
}

// Helper functions for environment variable parsing
func getEnvString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}
