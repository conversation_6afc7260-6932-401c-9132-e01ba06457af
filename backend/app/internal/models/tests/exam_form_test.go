package models_test

import (
	"testing"

	"app/internal/models"
	"app/tests/testutils"
)

func TestCreateExamFormRequest_Validate(t *testing.T) {
	tests := []struct {
		name     string
		examForm *models.CreateExamFormRequest
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid exam form",
			examForm: testutils.CreateTestExamForm(),
			wantErr:  false,
		},
		{
			name: "missing roll number",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.RollNumber = ""
				return e
			}(),
			wantErr: true,
			errMsg:  "roll number is required",
		},
		{
			name: "roll number too short",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.RollNumber = "123"
				return e
			}(),
			wantErr: true,
			errMsg:  "roll number must be between 5 and 10 characters",
		},
		{
			name: "roll number too long",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.RollNumber = "12345678901"
				return e
			}(),
			wantErr: true,
			errMsg:  "roll number must be between 5 and 10 characters",
		},
		{
			name: "invalid category",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.Category = "INVALID"
				return e
			}(),
			wantErr: true,
			errMsg:  "invalid category",
		},
		{
			name: "invalid exam type",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.ExamType = "INVALID"
				return e
			}(),
			wantErr: true,
			errMsg:  "exam type must be Regular or Ex",
		},
		{
			name: "no papers selected",
			examForm: func() *models.CreateExamFormRequest {
				e := testutils.CreateTestExamForm()
				e.PaperI = false
				e.PaperII = false
				e.PaperIII = false
				e.PaperIV = false
				return e
			}(),
			wantErr: true,
			errMsg:  "at least one paper must be selected",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.examForm.Validate()
			
			if tt.wantErr {
				testutils.AssertError(t, err)
				if tt.errMsg != "" {
					testutils.AssertContains(t, err.Error(), tt.errMsg)
				}
			} else {
				testutils.AssertNoError(t, err)
			}
		})
	}
}

func TestCreateExamFormRequest_ToExamForm(t *testing.T) {
	req := testutils.CreateTestExamForm()
	
	examForm := req.ToExamForm()
	
	// Verify conversion
	testutils.AssertEqual(t, req.RollNumber, examForm.RollNumber)
	testutils.AssertEqual(t, req.Category, examForm.Category)
	testutils.AssertEqual(t, req.ExamType == "Regular", examForm.IsRegular)
	testutils.AssertEqual(t, req.PaperI, examForm.PaperI)
	testutils.AssertEqual(t, req.PaperII, examForm.PaperII)
	testutils.AssertEqual(t, req.PaperIII, examForm.PaperIII)
	testutils.AssertEqual(t, req.PaperIV, examForm.PaperIV)
	
	// Verify fee calculation
	testutils.AssertNotEqual(t, "", examForm.Fee)
}

func TestExamFormFeeCalculation(t *testing.T) {
	tests := []struct {
		name         string
		category     string
		examType     string
		papers       []bool // [PaperI, PaperII, PaperIII, PaperIV]
		expectedFee  string
	}{
		{
			name:        "UR Regular 2 papers",
			category:    "UR",
			examType:    "Regular",
			papers:      []bool{true, true, false, false},
			expectedFee: "2000.00",
		},
		{
			name:        "SC Regular 2 papers (50% discount)",
			category:    "SC",
			examType:    "Regular",
			papers:      []bool{true, true, false, false},
			expectedFee: "1000.00",
		},
		{
			name:        "ST Regular 2 papers (50% discount)",
			category:    "ST",
			examType:    "Regular",
			papers:      []bool{true, true, false, false},
			expectedFee: "1000.00",
		},
		{
			name:        "EBC Regular 2 papers (25% discount)",
			category:    "EBC",
			examType:    "Regular",
			papers:      []bool{true, true, false, false},
			expectedFee: "1500.00",
		},
		{
			name:        "BC Regular 2 papers (25% discount)",
			category:    "BC",
			examType:    "Regular",
			papers:      []bool{true, true, false, false},
			expectedFee: "1500.00",
		},
		{
			name:        "UR Ex 2 papers (additional 500)",
			category:    "UR",
			examType:    "Ex",
			papers:      []bool{true, true, false, false},
			expectedFee: "2500.00",
		},
		{
			name:        "UR Regular 4 papers",
			category:    "UR",
			examType:    "Regular",
			papers:      []bool{true, true, true, true},
			expectedFee: "4000.00",
		},
		{
			name:        "UR Regular 1 paper",
			category:    "UR",
			examType:    "Regular",
			papers:      []bool{true, false, false, false},
			expectedFee: "1000.00",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &models.CreateExamFormRequest{
				RollNumber: "2023001",
				Category:   tt.category,
				ExamType:   tt.examType,
				PaperI:     tt.papers[0],
				PaperII:    tt.papers[1],
				PaperIII:   tt.papers[2],
				PaperIV:    tt.papers[3],
			}
			
			examForm := req.ToExamForm()
			testutils.AssertEqual(t, tt.expectedFee, examForm.Fee)
		})
	}
}

func TestExamForm_GetSelectedPapers(t *testing.T) {
	examForm := &models.ExamForm{
		PaperI:   true,
		PaperII:  false,
		PaperIII: true,
		PaperIV:  false,
	}
	
	papers := examForm.GetSelectedPapers()
	expectedPapers := []string{"Paper I", "Paper III"}
	
	testutils.AssertEqual(t, len(expectedPapers), len(papers))
	for i, expected := range expectedPapers {
		testutils.AssertEqual(t, expected, papers[i])
	}
}

func TestExamForm_GetExamType(t *testing.T) {
	t.Run("regular exam", func(t *testing.T) {
		examForm := &models.ExamForm{IsRegular: true}
		testutils.AssertEqual(t, "Regular", examForm.GetExamType())
	})
	
	t.Run("ex exam", func(t *testing.T) {
		examForm := &models.ExamForm{IsRegular: false}
		testutils.AssertEqual(t, "Ex", examForm.GetExamType())
	})
}

func TestExamForm_GetPaperCount(t *testing.T) {
	tests := []struct {
		name          string
		papers        []bool // [PaperI, PaperII, PaperIII, PaperIV]
		expectedCount int
	}{
		{
			name:          "no papers",
			papers:        []bool{false, false, false, false},
			expectedCount: 0,
		},
		{
			name:          "one paper",
			papers:        []bool{true, false, false, false},
			expectedCount: 1,
		},
		{
			name:          "two papers",
			papers:        []bool{true, true, false, false},
			expectedCount: 2,
		},
		{
			name:          "all papers",
			papers:        []bool{true, true, true, true},
			expectedCount: 4,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			examForm := &models.ExamForm{
				PaperI:   tt.papers[0],
				PaperII:  tt.papers[1],
				PaperIII: tt.papers[2],
				PaperIV:  tt.papers[3],
			}
			
			count := examForm.GetPaperCount()
			testutils.AssertEqual(t, tt.expectedCount, count)
		})
	}
}
