package models_test

import (
	"testing"

	"app/internal/models"
	"app/tests/testutils"
)

func TestCreateStudentRequest_Validate(t *testing.T) {
	tests := []struct {
		name    string
		student *models.CreateStudentRequest
		wantErr bool
		errMsg  string
	}{
		{
			name:    "valid student",
			student: testutils.CreateTestStudent(),
			wantErr: false,
		},
		{
			name: "missing name",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Name = ""
				return s
			}(),
			wantErr: true,
			errMsg:  "name is required",
		},
		{
			name: "missing email",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Email = ""
				return s
			}(),
			wantErr: true,
			errMsg:  "email is required",
		},
		{
			name: "invalid email format",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Email = "invalid-email"
				return s
			}(),
			wantErr: true,
			errMsg:  "invalid email format",
		},
		{
			name: "invalid mobile number",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.MobileNumber = "123"
				return s
			}(),
			wantErr: true,
			errMsg:  "mobile number must be 10 digits",
		},
		{
			name: "invalid aadhar number",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.AadharNumber = "123"
				return s
			}(),
			wantErr: true,
			errMsg:  "aadhar number must be 12 digits",
		},
		{
			name: "invalid pincode",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Pincode = "123"
				return s
			}(),
			wantErr: true,
			errMsg:  "pincode must be 6 digits",
		},
		{
			name: "invalid date format",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.DateOfBirth = "invalid-date"
				return s
			}(),
			wantErr: true,
			errMsg:  "invalid date format",
		},
		{
			name: "invalid category",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Category = "INVALID"
				return s
			}(),
			wantErr: true,
			errMsg:  "invalid category",
		},
		{
			name: "invalid gender",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.Gender = "X"
				return s
			}(),
			wantErr: true,
			errMsg:  "gender must be M or F",
		},
		{
			name: "invalid state",
			student: func() *models.CreateStudentRequest {
				s := testutils.CreateTestStudent()
				s.State = "INVALID"
				return s
			}(),
			wantErr: true,
			errMsg:  "state must be BIHAR or OTHER",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.student.Validate()
			
			if tt.wantErr {
				testutils.AssertError(t, err)
				if tt.errMsg != "" {
					testutils.AssertContains(t, err.Error(), tt.errMsg)
				}
			} else {
				testutils.AssertNoError(t, err)
			}
		})
	}
}

func TestCreateStudentRequest_ToStudent(t *testing.T) {
	req := testutils.CreateTestStudent()
	
	student, err := req.ToStudent()
	testutils.AssertNoError(t, err)
	
	// Verify conversion
	testutils.AssertEqual(t, req.Name, student.Name)
	testutils.AssertEqual(t, req.Email, student.Email)
	testutils.AssertEqual(t, req.Gender, student.Gender)
	testutils.AssertEqual(t, req.Category, student.Category)
	testutils.AssertEqual(t, req.MobileNumber, student.MobileNumber)
	testutils.AssertEqual(t, req.Session, student.Session)
	testutils.AssertEqual(t, req.Subject, student.Subject)
	testutils.AssertEqual(t, req.ClassRollNumber, student.ClassRollNumber)
	testutils.AssertEqual(t, req.UniversityRollNumber, student.UniversityRollNumber)
	testutils.AssertEqual(t, req.RegistrationNumber, student.RegistrationNumber)
	testutils.AssertEqual(t, req.FatherName, student.FatherName)
	testutils.AssertEqual(t, req.MotherName, student.MotherName)
	testutils.AssertEqual(t, req.Pincode, student.Pincode)
	testutils.AssertEqual(t, req.State, student.State)
	testutils.AssertEqual(t, req.AadharNumber, student.AadharNumber)
	testutils.AssertEqual(t, req.AbcID, student.AbcID)
	testutils.AssertEqual(t, req.AadharMobile, student.AadharMobile)
}

func TestCreateStudentRequest_ToStudent_InvalidDate(t *testing.T) {
	req := testutils.CreateTestStudent()
	req.DateOfBirth = "invalid-date"
	
	_, err := req.ToStudent()
	testutils.AssertError(t, err)
	testutils.AssertContains(t, err.Error(), "invalid date format")
}

func TestValidationHelpers(t *testing.T) {
	t.Run("email validation", func(t *testing.T) {
		validEmails := []string{
			"<EMAIL>",
			"<EMAIL>",
			"<EMAIL>",
		}
		
		invalidEmails := []string{
			"invalid-email",
			"@domain.com",
			"test@",
			"test.domain.com",
		}
		
		for _, email := range validEmails {
			req := testutils.CreateTestStudent()
			req.Email = email
			err := req.Validate()
			if err != nil && containsEmailError(err.Error()) {
				t.Errorf("Expected %s to be valid email", email)
			}
		}
		
		for _, email := range invalidEmails {
			req := testutils.CreateTestStudent()
			req.Email = email
			err := req.Validate()
			if err == nil || !containsEmailError(err.Error()) {
				t.Errorf("Expected %s to be invalid email", email)
			}
		}
	})
	
	t.Run("mobile validation", func(t *testing.T) {
		validMobiles := []string{"9876543210", "1234567890"}
		invalidMobiles := []string{"123", "12345678901", "abcdefghij"}
		
		for _, mobile := range validMobiles {
			req := testutils.CreateTestStudent()
			req.MobileNumber = mobile
			err := req.Validate()
			if err != nil && containsMobileError(err.Error()) {
				t.Errorf("Expected %s to be valid mobile", mobile)
			}
		}
		
		for _, mobile := range invalidMobiles {
			req := testutils.CreateTestStudent()
			req.MobileNumber = mobile
			err := req.Validate()
			if err == nil || !containsMobileError(err.Error()) {
				t.Errorf("Expected %s to be invalid mobile", mobile)
			}
		}
	})
}

func containsEmailError(errMsg string) bool {
	return contains(errMsg, "email") && (contains(errMsg, "invalid") || contains(errMsg, "format"))
}

func containsMobileError(errMsg string) bool {
	return contains(errMsg, "mobile") && contains(errMsg, "10 digits")
}

func contains(str, substr string) bool {
	if len(substr) == 0 {
		return true
	}
	if len(str) < len(substr) {
		return false
	}
	for i := 0; i <= len(str)-len(substr); i++ {
		if str[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
