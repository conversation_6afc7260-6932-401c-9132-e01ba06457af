package models

import (
	"fmt"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// UserRole represents the role of a user
type UserRole string

const (
	RoleSuperAdmin      UserRole = "super_admin"
	RoleDepartmentAdmin UserRole = "department_admin"
)

// User represents a system user (admin or department admin)
type User struct {
	ID           int32       `json:"id"`
	Email        string      `json:"email"`
	PasswordHash string      `json:"-"` // Never expose password hash in JSON
	Role         UserRole    `json:"role"`
	DepartmentID *int32      `json:"department_id,omitempty"`
	Department   *Department `json:"department,omitempty"`
	IsActive     bool        `json:"is_active"`
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
}

// CreateUserRequest represents the request to create a user
type CreateUserRequest struct {
	Email        string   `json:"email" validate:"required,email"`
	Password     string   `json:"password" validate:"required,min=8"`
	Role         UserRole `json:"role" validate:"required,oneof=super_admin department_admin"`
	DepartmentID *int32   `json:"department_id,omitempty"`
}

// UpdateUserRequest represents the request to update a user
type UpdateUserRequest struct {
	Email        string   `json:"email" validate:"omitempty,email"`
	Password     string   `json:"password" validate:"omitempty,min=8"`
	Role         UserRole `json:"role" validate:"omitempty,oneof=super_admin department_admin"`
	DepartmentID *int32   `json:"department_id,omitempty"`
	IsActive     *bool    `json:"is_active,omitempty"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// Validate validates the create user request
func (u *CreateUserRequest) Validate() error {
	if u.Email == "" {
		return fmt.Errorf("email is required")
	}
	if u.Password == "" {
		return fmt.Errorf("password is required")
	}
	if len(u.Password) < 8 {
		return fmt.Errorf("password must be at least 8 characters")
	}
	if u.Role != RoleSuperAdmin && u.Role != RoleDepartmentAdmin {
		return fmt.Errorf("invalid role")
	}
	if u.Role == RoleDepartmentAdmin && u.DepartmentID == nil {
		return fmt.Errorf("department_id is required for department admin")
	}
	return nil
}

// ToUser converts the request to a domain model
func (u *CreateUserRequest) ToUser() (*User, error) {
	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(u.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	return &User{
		Email:        strings.ToLower(strings.TrimSpace(u.Email)),
		PasswordHash: string(hashedPassword),
		Role:         u.Role,
		DepartmentID: u.DepartmentID,
		IsActive:     true,
	}, nil
}

// CheckPassword verifies if the provided password matches the user's password
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.PasswordHash), []byte(password))
	return err == nil
}

// IsSuperAdmin returns true if the user is a super admin
func (u *User) IsSuperAdmin() bool {
	return u.Role == RoleSuperAdmin
}

// IsDepartmentAdmin returns true if the user is a department admin
func (u *User) IsDepartmentAdmin() bool {
	return u.Role == RoleDepartmentAdmin
}

// CanManageDepartment returns true if the user can manage the given department
func (u *User) CanManageDepartment(departmentID int32) bool {
	if u.IsSuperAdmin() {
		return true
	}
	if u.IsDepartmentAdmin() && u.DepartmentID != nil && *u.DepartmentID == departmentID {
		return true
	}
	return false
}

// GetDisplayName returns a display name for the user
func (u *User) GetDisplayName() string {
	if u.Department != nil {
		return fmt.Sprintf("%s (%s Admin)", u.Email, u.Department.Name)
	}
	return u.Email
}

// HashPassword hashes a password using bcrypt
func HashPassword(password string) (string, error) {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}
	return string(hashedPassword), nil
}
