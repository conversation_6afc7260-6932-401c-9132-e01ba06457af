package models

import (
	"fmt"
	"strings"
	"time"
)

// EnrollmentStatus represents the status of a student enrollment
type EnrollmentStatus string

const (
	EnrollmentStatusPending  EnrollmentStatus = "pending"
	EnrollmentStatusApproved EnrollmentStatus = "approved"
	EnrollmentStatusRejected EnrollmentStatus = "rejected"
)

// StudentEnrollment represents a student's enrollment in a department
type StudentEnrollment struct {
	ID                   int32            `json:"id"`
	StudentID            int32            `json:"student_id"`
	Student              *Student         `json:"student,omitempty"`
	DepartmentID         int32            `json:"department_id"`
	Department           *Department      `json:"department,omitempty"`
	Session              string           `json:"session"`
	Subject              string           `json:"subject"`
	ClassRollNumber      *string          `json:"class_roll_number,omitempty"`
	UniversityRollNumber *string          `json:"university_roll_number,omitempty"`
	RegistrationNumber   *string          `json:"registration_number,omitempty"`
	Status               EnrollmentStatus `json:"status"`
	ApprovedBy           *int32           `json:"approved_by,omitempty"`
	ApprovedByUser       *User            `json:"approved_by_user,omitempty"`
	ApprovedAt           *time.Time       `json:"approved_at,omitempty"`
	RejectionReason      *string          `json:"rejection_reason,omitempty"`
	CreatedAt            time.Time        `json:"created_at"`
	UpdatedAt            time.Time        `json:"updated_at"`
}

// CreateEnrollmentRequest represents the request to create an enrollment
type CreateEnrollmentRequest struct {
	StudentID            int32  `json:"student_id" validate:"required"`
	DepartmentID         int32  `json:"department_id" validate:"required"`
	Session              string `json:"session" validate:"required,max=10"`
	Subject              string `json:"subject" validate:"required,max=50"`
	ClassRollNumber      string `json:"class_roll_number" validate:"omitempty,max=4"`
	UniversityRollNumber string `json:"university_roll_number" validate:"omitempty,max=10"`
	RegistrationNumber   string `json:"registration_number" validate:"omitempty,max=20"`
}

// UpdateEnrollmentRequest represents the request to update an enrollment
type UpdateEnrollmentRequest struct {
	Session              string `json:"session" validate:"omitempty,max=10"`
	Subject              string `json:"subject" validate:"omitempty,max=50"`
	ClassRollNumber      string `json:"class_roll_number" validate:"omitempty,max=4"`
	UniversityRollNumber string `json:"university_roll_number" validate:"omitempty,max=10"`
	RegistrationNumber   string `json:"registration_number" validate:"omitempty,max=20"`
}

// ApproveEnrollmentRequest represents the request to approve an enrollment
type ApproveEnrollmentRequest struct {
	ClassRollNumber      string `json:"class_roll_number" validate:"required,max=4"`
	UniversityRollNumber string `json:"university_roll_number" validate:"required,max=10"`
	RegistrationNumber   string `json:"registration_number" validate:"required,max=20"`
}

// RejectEnrollmentRequest represents the request to reject an enrollment
type RejectEnrollmentRequest struct {
	Reason string `json:"reason" validate:"required,max=500"`
}

// Validate validates the create enrollment request
func (e *CreateEnrollmentRequest) Validate() error {
	if e.StudentID == 0 {
		return fmt.Errorf("student_id is required")
	}
	if e.DepartmentID == 0 {
		return fmt.Errorf("department_id is required")
	}
	if e.Session == "" {
		return fmt.Errorf("session is required")
	}
	if e.Subject == "" {
		return fmt.Errorf("subject is required")
	}
	return nil
}

// ToEnrollment converts the request to a domain model
func (e *CreateEnrollmentRequest) ToEnrollment() *StudentEnrollment {
	enrollment := &StudentEnrollment{
		StudentID:    e.StudentID,
		DepartmentID: e.DepartmentID,
		Session:      strings.TrimSpace(e.Session),
		Subject:      strings.TrimSpace(e.Subject),
		Status:       EnrollmentStatusPending,
	}

	// Set roll numbers if provided
	if e.ClassRollNumber != "" {
		classRoll := strings.TrimSpace(e.ClassRollNumber)
		enrollment.ClassRollNumber = &classRoll
	}
	if e.UniversityRollNumber != "" {
		uniRoll := strings.TrimSpace(e.UniversityRollNumber)
		enrollment.UniversityRollNumber = &uniRoll
	}
	if e.RegistrationNumber != "" {
		regNum := strings.TrimSpace(e.RegistrationNumber)
		enrollment.RegistrationNumber = &regNum
	}

	return enrollment
}

// IsPending returns true if the enrollment is pending approval
func (e *StudentEnrollment) IsPending() bool {
	return e.Status == EnrollmentStatusPending
}

// IsApproved returns true if the enrollment is approved
func (e *StudentEnrollment) IsApproved() bool {
	return e.Status == EnrollmentStatusApproved
}

// IsRejected returns true if the enrollment is rejected
func (e *StudentEnrollment) IsRejected() bool {
	return e.Status == EnrollmentStatusRejected
}

// HasRollNumbers returns true if all roll numbers are assigned
func (e *StudentEnrollment) HasRollNumbers() bool {
	return e.ClassRollNumber != nil && *e.ClassRollNumber != "" &&
		e.UniversityRollNumber != nil && *e.UniversityRollNumber != "" &&
		e.RegistrationNumber != nil && *e.RegistrationNumber != ""
}

// GetStatusDisplay returns a human-readable status
func (e *StudentEnrollment) GetStatusDisplay() string {
	switch e.Status {
	case EnrollmentStatusPending:
		return "Pending Approval"
	case EnrollmentStatusApproved:
		return "Approved"
	case EnrollmentStatusRejected:
		return "Rejected"
	default:
		return "Unknown"
	}
}
