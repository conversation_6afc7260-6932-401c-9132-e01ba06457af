package models

import (
	"fmt"
	"regexp"
	"strings"
	"time"
)

// StudentStatus represents the status of a student
type StudentStatus string

const (
	StudentStatusRegistered StudentStatus = "registered"
	StudentStatusEnrolled   StudentStatus = "enrolled"
	StudentStatusApproved   StudentStatus = "approved"
	StudentStatusRejected   StudentStatus = "rejected"
)

// Student represents a student in the system
type Student struct {
	ID           int32         `json:"id"`
	Name         string        `json:"name"`
	Email        string        `json:"email"`
	DateOfBirth  time.Time     `json:"date_of_birth"`
	Gender       string        `json:"gender"`
	Category     string        `json:"category"`
	MobileNumber string        `json:"mobile_number"`
	FatherName   string        `json:"father_name"`
	MotherName   string        `json:"mother_name"`
	Pincode      string        `json:"pincode"`
	State        string        `json:"state"`
	AadharNumber string        `json:"aadhar_number"`
	AbcID        string        `json:"abc_id"`
	AadharMobile string        `json:"aadhar_mobile"`
	Status       StudentStatus `json:"status"`
	CreatedAt    time.Time     `json:"created_at"`
	UpdatedAt    time.Time     `json:"updated_at"`
}

// CreateStudentRequest represents the request to create a student (initial registration)
type CreateStudentRequest struct {
	Name         string `json:"name" validate:"required,min=2,max=100"`
	Email        string `json:"email" validate:"required,email"`
	DateOfBirth  string `json:"date_of_birth" validate:"required"`
	Gender       string `json:"gender" validate:"required,oneof=M F"`
	Category     string `json:"category" validate:"required,oneof=UR EWS BC EBC SC ST"`
	MobileNumber string `json:"mobile_number" validate:"required,len=10,numeric"`
	FatherName   string `json:"father_name" validate:"required,max=100"`
	MotherName   string `json:"mother_name" validate:"required,max=100"`
	Pincode      string `json:"pincode" validate:"required,len=6,numeric"`
	State        string `json:"state" validate:"required,oneof=BIHAR OTHER"`
	AadharNumber string `json:"aadhar_number" validate:"required,len=12,numeric"`
	AbcID        string `json:"abc_id" validate:"required,len=12"`
	AadharMobile string `json:"aadhar_mobile" validate:"required,len=10,numeric"`
}

// UpdateStudentRequest represents the request to update a student
type UpdateStudentRequest struct {
	ID                   int32  `json:"id" validate:"required"`
	Name                 string `json:"name" validate:"omitempty,min=2,max=100"`
	Email                string `json:"email" validate:"omitempty,email"`
	DateOfBirth          string `json:"date_of_birth" validate:"omitempty"`
	Gender               string `json:"gender" validate:"omitempty,oneof=M F"`
	Category             string `json:"category" validate:"omitempty,oneof=UR EWS BC EBC SC ST"`
	MobileNumber         string `json:"mobile_number" validate:"omitempty,len=10,numeric"`
	College              string `json:"college" validate:"omitempty,max=100"`
	Session              string `json:"session" validate:"omitempty,max=10"`
	Subject              string `json:"subject" validate:"omitempty,max=50"`
	ClassRollNumber      string `json:"class_roll_number" validate:"omitempty,max=4"`
	UniversityRollNumber string `json:"university_roll_number" validate:"omitempty,max=10"`
	RegistrationNumber   string `json:"registration_number" validate:"omitempty,max=20"`
	FatherName           string `json:"father_name" validate:"omitempty,max=100"`
	MotherName           string `json:"mother_name" validate:"omitempty,max=100"`
	Pincode              string `json:"pincode" validate:"omitempty,len=6,numeric"`
	State                string `json:"state" validate:"omitempty,oneof=BIHAR OTHER"`
	AadharNumber         string `json:"aadhar_number" validate:"omitempty,len=12,numeric"`
	AbcID                string `json:"abc_id" validate:"omitempty,len=12"`
	AadharMobile         string `json:"aadhar_mobile" validate:"omitempty,len=10,numeric"`
}

// StudentSearchRequest represents search criteria for students
type StudentSearchRequest struct {
	SearchType  string `json:"search_type" validate:"required,oneof=email aadhar_number registration_number university_roll_number"`
	SearchValue string `json:"search_value" validate:"required"`
}

// Validate validates the student data
func (s *CreateStudentRequest) Validate() error {
	var errors []string

	// Basic validation
	if strings.TrimSpace(s.Name) == "" {
		errors = append(errors, "name is required")
	}
	if strings.TrimSpace(s.Email) == "" {
		errors = append(errors, "email is required")
	}
	if !isValidEmail(s.Email) {
		errors = append(errors, "invalid email format")
	}
	if !isValidMobile(s.MobileNumber) {
		errors = append(errors, "mobile number must be 10 digits")
	}
	if !isValidAadhar(s.AadharNumber) {
		errors = append(errors, "aadhar number must be 12 digits")
	}
	if !isValidPincode(s.Pincode) {
		errors = append(errors, "pincode must be 6 digits")
	}

	// Date validation
	if _, err := time.Parse("2006-01-02", s.DateOfBirth); err != nil {
		errors = append(errors, "invalid date format for date of birth")
	}

	// Category validation
	validCategories := []string{"UR", "EWS", "BC", "EBC", "SC", "ST"}
	if !contains(validCategories, s.Category) {
		errors = append(errors, "invalid category")
	}

	// Gender validation
	if s.Gender != "M" && s.Gender != "F" {
		errors = append(errors, "gender must be M or F")
	}

	// State validation
	if s.State != "BIHAR" && s.State != "OTHER" {
		errors = append(errors, "state must be BIHAR or OTHER")
	}

	if len(errors) > 0 {
		return fmt.Errorf("validation errors: %s", strings.Join(errors, ", "))
	}

	return nil
}

// ToStudent converts CreateStudentRequest to Student
func (s *CreateStudentRequest) ToStudent() (*Student, error) {
	dob, err := time.Parse("2006-01-02", s.DateOfBirth)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	return &Student{
		Name:         strings.TrimSpace(s.Name),
		Email:        strings.ToLower(strings.TrimSpace(s.Email)),
		DateOfBirth:  dob,
		Gender:       s.Gender,
		Category:     s.Category,
		MobileNumber: s.MobileNumber,
		FatherName:   strings.TrimSpace(s.FatherName),
		MotherName:   strings.TrimSpace(s.MotherName),
		Pincode:      s.Pincode,
		State:        s.State,
		AadharNumber: s.AadharNumber,
		AbcID:        s.AbcID,
		AadharMobile: s.AadharMobile,
		Status:       StudentStatusRegistered,
	}, nil
}

// IsRegistered returns true if the student is in registered status
func (s *Student) IsRegistered() bool {
	return s.Status == StudentStatusRegistered
}

// IsEnrolled returns true if the student has enrollments
func (s *Student) IsEnrolled() bool {
	return s.Status == StudentStatusEnrolled
}

// IsApproved returns true if the student has approved enrollments
func (s *Student) IsApproved() bool {
	return s.Status == StudentStatusApproved
}

// IsRejected returns true if the student has been rejected
func (s *Student) IsRejected() bool {
	return s.Status == StudentStatusRejected
}

// GetStatusDisplay returns a human-readable status
func (s *Student) GetStatusDisplay() string {
	switch s.Status {
	case StudentStatusRegistered:
		return "Registered"
	case StudentStatusEnrolled:
		return "Enrolled"
	case StudentStatusApproved:
		return "Approved"
	case StudentStatusRejected:
		return "Rejected"
	default:
		return "Unknown"
	}
}

// GetFullName returns the full name of the student
func (s *Student) GetFullName() string {
	return strings.TrimSpace(s.Name)
}

// Helper functions for validation
func isValidEmail(email string) bool {
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

func isValidMobile(mobile string) bool {
	mobileRegex := regexp.MustCompile(`^[0-9]{10}$`)
	return mobileRegex.MatchString(mobile)
}

func isValidAadhar(aadhar string) bool {
	aadharRegex := regexp.MustCompile(`^[0-9]{12}$`)
	return aadharRegex.MatchString(aadhar)
}

func isValidPincode(pincode string) bool {
	pincodeRegex := regexp.MustCompile(`^[0-9]{6}$`)
	return pincodeRegex.MatchString(pincode)
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
