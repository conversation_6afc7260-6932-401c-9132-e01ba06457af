package models

import (
	"fmt"
	"strings"
)

// ExamForm represents an exam form in the system
type ExamForm struct {
	ID         int32  `json:"id"`
	RollNumber string `json:"roll_number"`
	Category   string `json:"category"`
	IsRegular  bool   `json:"is_regular"`
	PaperI     bool   `json:"paper_i"`
	PaperII    bool   `json:"paper_ii"`
	PaperIII   bool   `json:"paper_iii"`
	PaperIV    bool   `json:"paper_iv"`
	Fee        string `json:"fee"`
}

// CreateExamFormRequest represents the request to create an exam form
type CreateExamFormRequest struct {
	RollNumber string `json:"roll_number" validate:"required,min=5,max=10"`
	Category   string `json:"category" validate:"required,oneof=UR EWS BC EBC SC ST"`
	ExamType   string `json:"exam_type" validate:"required,oneof=Regular Ex"`
	PaperI     bool   `json:"paper_i"`
	PaperII    bool   `json:"paper_ii"`
	PaperIII   bool   `json:"paper_iii"`
	PaperIV    bool   `json:"paper_iv"`
}

// UpdateExamFormRequest represents the request to update an exam form
type UpdateExamFormRequest struct {
	RollNumber string `json:"roll_number" validate:"required"`
	Category   string `json:"category" validate:"omitempty,oneof=UR EWS BC EBC SC ST"`
	ExamType   string `json:"exam_type" validate:"omitempty,oneof=Regular Ex"`
	PaperI     *bool  `json:"paper_i,omitempty"`
	PaperII    *bool  `json:"paper_ii,omitempty"`
	PaperIII   *bool  `json:"paper_iii,omitempty"`
	PaperIV    *bool  `json:"paper_iv,omitempty"`
}

// ExamFormSearchRequest represents search criteria for exam forms
type ExamFormSearchRequest struct {
	RollNumber string `json:"roll_number"`
	Category   string `json:"category"`
	IsRegular  *bool  `json:"is_regular,omitempty"`
}

// ExamFormWithStudent represents an exam form with associated student data
type ExamFormWithStudent struct {
	ExamForm *ExamForm `json:"exam_form"`
	Student  *Student  `json:"student"`
}

// Validate validates the exam form creation request
func (e *CreateExamFormRequest) Validate() error {
	var errors []string

	// Roll number validation
	if strings.TrimSpace(e.RollNumber) == "" {
		errors = append(errors, "roll number is required")
	}
	if len(e.RollNumber) < 5 || len(e.RollNumber) > 10 {
		errors = append(errors, "roll number must be between 5 and 10 characters")
	}

	// Category validation
	validCategories := []string{"UR", "EWS", "BC", "EBC", "SC", "ST"}
	if !contains(validCategories, e.Category) {
		errors = append(errors, "invalid category")
	}

	// Exam type validation
	if e.ExamType != "Regular" && e.ExamType != "Ex" {
		errors = append(errors, "exam type must be Regular or Ex")
	}

	// At least one paper must be selected
	if !e.PaperI && !e.PaperII && !e.PaperIII && !e.PaperIV {
		errors = append(errors, "at least one paper must be selected")
	}

	if len(errors) > 0 {
		return fmt.Errorf("validation errors: %s", strings.Join(errors, ", "))
	}

	return nil
}

// ToExamForm converts CreateExamFormRequest to ExamForm
func (e *CreateExamFormRequest) ToExamForm() *ExamForm {
	return &ExamForm{
		RollNumber: strings.TrimSpace(e.RollNumber),
		Category:   e.Category,
		IsRegular:  e.ExamType == "Regular",
		PaperI:     e.PaperI,
		PaperII:    e.PaperII,
		PaperIII:   e.PaperIII,
		PaperIV:    e.PaperIV,
		Fee:        e.calculateFee(),
	}
}

// calculateFee calculates the fee based on selected papers and category
func (e *CreateExamFormRequest) calculateFee() string {
	baseFee := 1000.0
	paperCount := 0

	if e.PaperI {
		paperCount++
	}
	if e.PaperII {
		paperCount++
	}
	if e.PaperIII {
		paperCount++
	}
	if e.PaperIV {
		paperCount++
	}

	// Calculate fee based on number of papers
	totalFee := baseFee * float64(paperCount)

	// Apply category-based discounts
	switch e.Category {
	case "SC", "ST":
		totalFee *= 0.5 // 50% discount
	case "EBC", "BC":
		totalFee *= 0.75 // 25% discount
	}

	// Ex-students pay additional fee
	if e.ExamType == "Ex" {
		totalFee += 500.0
	}

	return fmt.Sprintf("%.2f", totalFee)
}

// GetSelectedPapers returns a list of selected papers
func (e *ExamForm) GetSelectedPapers() []string {
	var papers []string
	if e.PaperI {
		papers = append(papers, "Paper I")
	}
	if e.PaperII {
		papers = append(papers, "Paper II")
	}
	if e.PaperIII {
		papers = append(papers, "Paper III")
	}
	if e.PaperIV {
		papers = append(papers, "Paper IV")
	}
	return papers
}

// GetExamType returns the exam type as string
func (e *ExamForm) GetExamType() string {
	if e.IsRegular {
		return "Regular"
	}
	return "Ex"
}

// GetPaperCount returns the number of selected papers
func (e *ExamForm) GetPaperCount() int {
	count := 0
	if e.PaperI {
		count++
	}
	if e.PaperII {
		count++
	}
	if e.PaperIII {
		count++
	}
	if e.PaperIV {
		count++
	}
	return count
}
