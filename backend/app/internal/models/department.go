package models

import (
	"fmt"
	"strings"
	"time"
)

// Department represents a university department
type Department struct {
	ID          int32     `json:"id"`
	Name        string    `json:"name"`
	Code        string    `json:"code"`
	Description string    `json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// CreateDepartmentRequest represents the request to create a department
type CreateDepartmentRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Code        string `json:"code" validate:"required,min=2,max=10,uppercase"`
	Description string `json:"description" validate:"max=500"`
}

// UpdateDepartmentRequest represents the request to update a department
type UpdateDepartmentRequest struct {
	Name        string `json:"name" validate:"omitempty,min=2,max=100"`
	Code        string `json:"code" validate:"omitempty,min=2,max=10,uppercase"`
	Description string `json:"description" validate:"omitempty,max=500"`
}

// Validate validates the create department request
func (d *CreateDepartmentRequest) Validate() error {
	// Basic validation - can be extended with more complex rules
	if d.Name == "" {
		return fmt.Errorf("department name is required")
	}
	if d.Code == "" {
		return fmt.Errorf("department code is required")
	}
	return nil
}

// ToDepartment converts the request to a domain model
func (d *CreateDepartmentRequest) ToDepartment() *Department {
	return &Department{
		Name:        strings.TrimSpace(d.Name),
		Code:        strings.ToUpper(strings.TrimSpace(d.Code)),
		Description: strings.TrimSpace(d.Description),
	}
}

// GetSubjects returns the subjects available in this department
func (d *Department) GetSubjects() []string {
	switch d.Code {
	case "SCI":
		return []string{"Physics", "Chemistry", "Mathematics", "Botany", "Zoology"}
	case "LANG":
		return []string{"Hindi", "English", "Urdu", "Sanskrit"}
	case "SOC":
		return []string{"History", "Political Science", "Economics", "Sociology", "Philosophy", "Psychology", "AIHC", "IRPM"}
	default:
		return []string{}
	}
}
