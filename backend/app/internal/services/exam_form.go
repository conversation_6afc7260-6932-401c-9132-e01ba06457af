package services

import (
	"context"
	"strings"

	"app/internal/errors"
	"app/internal/models"
)

// examFormService implements ExamFormService
type examFormService struct {
	deps *Dependencies
}

// NewExamFormService creates a new exam form service
func NewExamFormService(deps *Dependencies) ExamFormService {
	return &examFormService{
		deps: deps,
	}
}

// CreateExamForm creates a new exam form
func (e *examFormService) CreateExamForm(ctx context.Context, req *models.CreateExamFormRequest) (*models.ExamForm, error) {
	// Validate the request
	if err := req.Validate(); err != nil {
		e.deps.Logger.Warn("Exam form validation failed", "error", err)
		return nil, errors.Validation(err.Error())
	}

	// Check if exam form already exists for this roll number
	_, err := e.deps.Repository.ExamForm().GetByRollNumber(ctx, req.RollNumber)
	if err == nil {
		e.deps.Logger.Info("Exam form already exists", "rollNumber", req.RollNumber)
		return nil, errors.FormExists("Exam form for this roll number already exists")
	}

	// Verify that the error is "not found" and not some other error
	if !errors.IsAppError(err) || errors.GetAppError(err).Code != errors.ErrCodeFormNotFound {
		e.deps.Logger.Error("Failed to check exam form existence", "error", err)
		return nil, err
	}

	// Convert request to domain model
	examForm := req.ToExamForm()

	// Create the exam form
	createdForm, err := e.deps.Repository.ExamForm().Create(ctx, examForm)
	if err != nil {
		e.deps.Logger.Error("Failed to create exam form", "error", err)
		return nil, err
	}

	e.deps.Logger.Info("Exam form created successfully", 
		"id", createdForm.ID, 
		"rollNumber", createdForm.RollNumber,
		"category", createdForm.Category)

	return createdForm, nil
}

// GetExamFormByID retrieves an exam form by ID
func (e *examFormService) GetExamFormByID(ctx context.Context, id int32) (*models.ExamForm, error) {
	if id <= 0 {
		return nil, errors.BadRequest("Invalid exam form ID")
	}

	examForm, err := e.deps.Repository.ExamForm().GetByID(ctx, id)
	if err != nil {
		e.deps.Logger.Error("Failed to get exam form by ID", "id", id, "error", err)
		return nil, err
	}

	return examForm, nil
}

// GetExamFormByRollNumber retrieves an exam form by roll number
func (e *examFormService) GetExamFormByRollNumber(ctx context.Context, rollNumber string) (*models.ExamForm, error) {
	if strings.TrimSpace(rollNumber) == "" {
		return nil, errors.BadRequest("Roll number is required")
	}

	examForm, err := e.deps.Repository.ExamForm().GetByRollNumber(ctx, rollNumber)
	if err != nil {
		e.deps.Logger.Error("Failed to get exam form by roll number", "rollNumber", rollNumber, "error", err)
		return nil, err
	}

	return examForm, nil
}

// GetAllExamForms retrieves all exam forms with pagination
func (e *examFormService) GetAllExamForms(ctx context.Context, page, pageSize int) ([]*models.ExamForm, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20 // Default page size
	}

	offset := (page - 1) * pageSize

	// Get exam forms
	examForms, err := e.deps.Repository.ExamForm().GetAll(ctx, pageSize, offset)
	if err != nil {
		e.deps.Logger.Error("Failed to get all exam forms", "error", err)
		return nil, 0, err
	}

	// Get total count
	total, err := e.deps.Repository.ExamForm().Count(ctx)
	if err != nil {
		e.deps.Logger.Error("Failed to count exam forms", "error", err)
		return nil, 0, err
	}

	e.deps.Logger.Info("Retrieved exam forms", 
		"count", len(examForms), 
		"page", page, 
		"pageSize", pageSize,
		"total", total)

	return examForms, total, nil
}

// GetExamFormsByCategory retrieves exam forms by category
func (e *examFormService) GetExamFormsByCategory(ctx context.Context, category string) ([]*models.ExamForm, error) {
	if strings.TrimSpace(category) == "" {
		return nil, errors.BadRequest("Category is required")
	}

	examForms, err := e.deps.Repository.ExamForm().GetByCategory(ctx, category)
	if err != nil {
		e.deps.Logger.Error("Failed to get exam forms by category", "category", category, "error", err)
		return nil, err
	}

	return examForms, nil
}

// UpdateExamForm updates an exam form
func (e *examFormService) UpdateExamForm(ctx context.Context, req *models.UpdateExamFormRequest) (*models.ExamForm, error) {
	if strings.TrimSpace(req.RollNumber) == "" {
		return nil, errors.BadRequest("Roll number is required")
	}

	// Get existing exam form
	existingForm, err := e.deps.Repository.ExamForm().GetByRollNumber(ctx, req.RollNumber)
	if err != nil {
		e.deps.Logger.Error("Failed to get existing exam form", "rollNumber", req.RollNumber, "error", err)
		return nil, err
	}

	// Update only provided fields
	updatedForm := *existingForm
	
	if req.Category != "" {
		updatedForm.Category = req.Category
	}
	if req.ExamType != "" {
		updatedForm.IsRegular = req.ExamType == "Regular"
	}
	if req.PaperI != nil {
		updatedForm.PaperI = *req.PaperI
	}
	if req.PaperII != nil {
		updatedForm.PaperII = *req.PaperII
	}
	if req.PaperIII != nil {
		updatedForm.PaperIII = *req.PaperIII
	}
	if req.PaperIV != nil {
		updatedForm.PaperIV = *req.PaperIV
	}

	// Recalculate fee if papers or category changed
	createReq := &models.CreateExamFormRequest{
		RollNumber: updatedForm.RollNumber,
		Category:   updatedForm.Category,
		ExamType:   updatedForm.GetExamType(),
		PaperI:     updatedForm.PaperI,
		PaperII:    updatedForm.PaperII,
		PaperIII:   updatedForm.PaperIII,
		PaperIV:    updatedForm.PaperIV,
	}
	updatedForm.Fee = createReq.ToExamForm().Fee

	// Update the exam form
	result, err := e.deps.Repository.ExamForm().Update(ctx, &updatedForm)
	if err != nil {
		e.deps.Logger.Error("Failed to update exam form", "rollNumber", req.RollNumber, "error", err)
		return nil, err
	}

	e.deps.Logger.Info("Exam form updated successfully", "rollNumber", result.RollNumber)
	return result, nil
}

// DeleteExamForm deletes an exam form
func (e *examFormService) DeleteExamForm(ctx context.Context, id int32) error {
	if id <= 0 {
		return errors.BadRequest("Invalid exam form ID")
	}

	// Check if exam form exists
	_, err := e.deps.Repository.ExamForm().GetByID(ctx, id)
	if err != nil {
		return err
	}

	// Delete the exam form
	err = e.deps.Repository.ExamForm().Delete(ctx, id)
	if err != nil {
		e.deps.Logger.Error("Failed to delete exam form", "id", id, "error", err)
		return err
	}

	e.deps.Logger.Info("Exam form deleted successfully", "id", id)
	return nil
}

// DeleteExamFormByRollNumber deletes an exam form by roll number
func (e *examFormService) DeleteExamFormByRollNumber(ctx context.Context, rollNumber string) error {
	if strings.TrimSpace(rollNumber) == "" {
		return errors.BadRequest("Roll number is required")
	}

	// Check if exam form exists
	_, err := e.deps.Repository.ExamForm().GetByRollNumber(ctx, rollNumber)
	if err != nil {
		return err
	}

	// Delete the exam form
	err = e.deps.Repository.ExamForm().DeleteByRollNumber(ctx, rollNumber)
	if err != nil {
		e.deps.Logger.Error("Failed to delete exam form", "rollNumber", rollNumber, "error", err)
		return err
	}

	e.deps.Logger.Info("Exam form deleted successfully", "rollNumber", rollNumber)
	return nil
}

// SearchExamForms searches for exam forms
func (e *examFormService) SearchExamForms(ctx context.Context, req *models.ExamFormSearchRequest) ([]*models.ExamForm, error) {
	examForms, err := e.deps.Repository.ExamForm().Search(ctx, req)
	if err != nil {
		e.deps.Logger.Error("Failed to search exam forms", "error", err)
		return nil, err
	}

	return examForms, nil
}

// GenerateExamFormWithStudent generates an exam form with student data
func (e *examFormService) GenerateExamFormWithStudent(ctx context.Context, rollNumber string) (*models.ExamFormWithStudent, error) {
	if strings.TrimSpace(rollNumber) == "" {
		return nil, errors.BadRequest("Roll number is required")
	}

	// Get exam form
	examForm, err := e.deps.Repository.ExamForm().GetByRollNumber(ctx, rollNumber)
	if err != nil {
		return nil, err
	}

	// Get student by university roll number (assuming roll number matches)
	student, err := e.deps.Repository.Student().GetByUniversityRollNumber(ctx, rollNumber)
	if err != nil {
		e.deps.Logger.Error("Failed to get student for exam form", "rollNumber", rollNumber, "error", err)
		return nil, err
	}

	return &models.ExamFormWithStudent{
		ExamForm: examForm,
		Student:  student,
	}, nil
}

// ValidateExamFormEligibility validates if a student is eligible for exam form
func (e *examFormService) ValidateExamFormEligibility(ctx context.Context, rollNumber string) error {
	// Check if student exists
	_, err := e.deps.Repository.Student().GetByUniversityRollNumber(ctx, rollNumber)
	if err != nil {
		return errors.BadRequest("Student not found for this roll number")
	}

	// Check if exam form already exists
	_, err = e.deps.Repository.ExamForm().GetByRollNumber(ctx, rollNumber)
	if err == nil {
		return errors.FormExists("Exam form already exists for this roll number")
	}

	// Verify that the error is "not found" and not some other error
	if !errors.IsAppError(err) || errors.GetAppError(err).Code != errors.ErrCodeFormNotFound {
		return err
	}

	return nil
}

// CalculateExamFee calculates the exam fee
func (e *examFormService) CalculateExamFee(ctx context.Context, req *models.CreateExamFormRequest) (string, error) {
	if err := req.Validate(); err != nil {
		return "", errors.Validation(err.Error())
	}

	return req.ToExamForm().Fee, nil
}
