package services

import (
	"context"
	"strings"

	"app/internal/errors"
	"app/internal/models"
)

// studentService implements StudentService
type studentService struct {
	deps *Dependencies
}

// NewStudentService creates a new student service
func NewStudentService(deps *Dependencies) StudentService {
	return &studentService{
		deps: deps,
	}
}

// CreateStudent creates a new student
func (s *studentService) CreateStudent(ctx context.Context, req *models.CreateStudentRequest) (*models.Student, error) {
	// Validate the request
	if err := req.Validate(); err != nil {
		s.deps.Logger.Warn("Student validation failed", "error", err)
		return nil, errors.Validation(err.Error())
	}

	// Check if student already exists
	exists, err := s.ValidateStudentExists(ctx, "email", req.Email)
	if err != nil {
		s.deps.Logger.Error("Failed to check student existence", "error", err)
		return nil, err
	}
	if exists {
		s.deps.Logger.Info("Student already exists", "email", req.Email)
		return nil, errors.StudentExists("Student with this email already exists")
	}

	// Check Aadhar uniqueness
	exists, err = s.ValidateStudentExists(ctx, "aadhar_number", req.AadharNumber)
	if err != nil {
		s.deps.Logger.Error("Failed to check Aadhar existence", "error", err)
		return nil, err
	}
	if exists {
		s.deps.Logger.Info("Student with Aadhar already exists", "aadhar", req.AadharNumber)
		return nil, errors.StudentExists("Student with this Aadhar number already exists")
	}

	// Convert request to domain model
	student, err := req.ToStudent()
	if err != nil {
		s.deps.Logger.Error("Failed to convert request to student", "error", err)
		return nil, errors.BadRequest("Invalid student data")
	}

	// Note: College field removed in refactored schema
	// Students will be associated with departments through enrollments

	// Create the student
	createdStudent, err := s.deps.Repository.Student().Create(ctx, student)
	if err != nil {
		s.deps.Logger.Error("Failed to create student", "error", err)
		return nil, err
	}

	s.deps.Logger.Info("Student created successfully",
		"id", createdStudent.ID,
		"email", createdStudent.Email,
		"name", createdStudent.Name)

	return createdStudent, nil
}

// GetStudentByID retrieves a student by ID
func (s *studentService) GetStudentByID(ctx context.Context, id int32) (*models.Student, error) {
	if id <= 0 {
		return nil, errors.BadRequest("Invalid student ID")
	}

	student, err := s.deps.Repository.Student().GetByID(ctx, id)
	if err != nil {
		s.deps.Logger.Error("Failed to get student by ID", "id", id, "error", err)
		return nil, err
	}

	return student, nil
}

// GetStudentByEmail retrieves a student by email
func (s *studentService) GetStudentByEmail(ctx context.Context, email string) (*models.Student, error) {
	if strings.TrimSpace(email) == "" {
		return nil, errors.BadRequest("Email is required")
	}

	email = strings.ToLower(strings.TrimSpace(email))
	student, err := s.deps.Repository.Student().GetByEmail(ctx, email)
	if err != nil {
		s.deps.Logger.Error("Failed to get student by email", "email", email, "error", err)
		return nil, err
	}

	return student, nil
}

// GetAllStudents retrieves all students with pagination
func (s *studentService) GetAllStudents(ctx context.Context, page, pageSize int) ([]*models.Student, int64, error) {
	// Validate pagination parameters
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 20 // Default page size
	}

	offset := (page - 1) * pageSize

	// Get students
	students, err := s.deps.Repository.Student().GetAll(ctx, pageSize, offset)
	if err != nil {
		s.deps.Logger.Error("Failed to get all students", "error", err)
		return nil, 0, err
	}

	// Get total count
	total, err := s.deps.Repository.Student().Count(ctx)
	if err != nil {
		s.deps.Logger.Error("Failed to count students", "error", err)
		return nil, 0, err
	}

	s.deps.Logger.Info("Retrieved students",
		"count", len(students),
		"page", page,
		"pageSize", pageSize,
		"total", total)

	return students, total, nil
}

// UpdateStudent updates a student
func (s *studentService) UpdateStudent(ctx context.Context, req *models.UpdateStudentRequest) (*models.Student, error) {
	if req.ID <= 0 {
		return nil, errors.BadRequest("Invalid student ID")
	}

	// Get existing student
	existingStudent, err := s.deps.Repository.Student().GetByID(ctx, req.ID)
	if err != nil {
		s.deps.Logger.Error("Failed to get existing student", "id", req.ID, "error", err)
		return nil, err
	}

	// Update only provided fields
	updatedStudent := *existingStudent

	if req.Name != "" {
		updatedStudent.Name = strings.TrimSpace(req.Name)
	}
	if req.Email != "" {
		// Check if new email is already taken by another student
		if req.Email != existingStudent.Email {
			exists, err := s.ValidateStudentExists(ctx, "email", req.Email)
			if err != nil {
				return nil, err
			}
			if exists {
				return nil, errors.StudentExists("Student with this email already exists")
			}
		}
		updatedStudent.Email = strings.ToLower(strings.TrimSpace(req.Email))
	}
	if req.MobileNumber != "" {
		updatedStudent.MobileNumber = req.MobileNumber
	}
	if req.Category != "" {
		updatedStudent.Category = req.Category
	}
	// Note: Subject and Session fields removed in refactored schema
	// These will be handled through the enrollment system
	// Add other fields as needed...

	// Update the student
	result, err := s.deps.Repository.Student().Update(ctx, &updatedStudent)
	if err != nil {
		s.deps.Logger.Error("Failed to update student", "id", req.ID, "error", err)
		return nil, err
	}

	s.deps.Logger.Info("Student updated successfully", "id", result.ID)
	return result, nil
}

// DeleteStudent deletes a student
func (s *studentService) DeleteStudent(ctx context.Context, id int32) error {
	if id <= 0 {
		return errors.BadRequest("Invalid student ID")
	}

	// Check if student exists
	_, err := s.deps.Repository.Student().GetByID(ctx, id)
	if err != nil {
		return err
	}

	// Delete the student
	err = s.deps.Repository.Student().Delete(ctx, id)
	if err != nil {
		s.deps.Logger.Error("Failed to delete student", "id", id, "error", err)
		return err
	}

	s.deps.Logger.Info("Student deleted successfully", "id", id)
	return nil
}

// SearchStudent searches for a student
func (s *studentService) SearchStudent(ctx context.Context, req *models.StudentSearchRequest) (*models.Student, error) {
	if strings.TrimSpace(req.SearchValue) == "" {
		return nil, errors.BadRequest("Search value is required")
	}

	// Validate search type
	validSearchTypes := []string{"email", "aadhar_number", "registration_number", "university_roll_number"}
	isValid := false
	for _, validType := range validSearchTypes {
		if req.SearchType == validType {
			isValid = true
			break
		}
	}
	if !isValid {
		return nil, errors.BadRequest("Invalid search type")
	}

	student, err := s.deps.Repository.Student().Search(ctx, req.SearchType, req.SearchValue)
	if err != nil {
		s.deps.Logger.Error("Failed to search student",
			"searchType", req.SearchType,
			"searchValue", req.SearchValue,
			"error", err)
		return nil, err
	}

	return student, nil
}

// ValidateStudentExists checks if a student exists
func (s *studentService) ValidateStudentExists(ctx context.Context, searchType, searchValue string) (bool, error) {
	if strings.TrimSpace(searchValue) == "" {
		return false, errors.BadRequest("Search value is required")
	}

	_, err := s.deps.Repository.Student().Search(ctx, searchType, searchValue)
	if err != nil {
		if errors.IsAppError(err) {
			appErr := errors.GetAppError(err)
			if appErr.Code == errors.ErrCodeStudentNotFound {
				return false, nil
			}
		}
		return false, err
	}

	return true, nil
}

// RegisterStudent creates a new student registration (alias for CreateStudent)
func (s *studentService) RegisterStudent(ctx context.Context, req *models.CreateStudentRequest) (*models.Student, error) {
	// For now, this is the same as CreateStudent
	return s.CreateStudent(ctx, req)
}

// EnrollStudent creates an enrollment for a student
func (s *studentService) EnrollStudent(ctx context.Context, req *models.CreateEnrollmentRequest) (*models.StudentEnrollment, error) {
	// TODO: Implement when enrollment repository is available
	return nil, errors.BadRequest("enrollment functionality not yet implemented - database schema needs to be updated")
}

// GetStudentEnrollments returns enrollments for a student
func (s *studentService) GetStudentEnrollments(ctx context.Context, studentID int32) ([]*models.StudentEnrollment, error) {
	// TODO: Implement when enrollment repository is available
	return []*models.StudentEnrollment{}, nil
}

// GetStudentStats returns statistics about students
func (s *studentService) GetStudentStats(ctx context.Context) (map[string]interface{}, error) {
	// Get total count
	total, err := s.deps.Repository.Student().Count(ctx)
	if err != nil {
		s.deps.Logger.Error("Failed to count students", "error", err)
		return nil, err
	}

	// For now, return basic stats
	stats := map[string]interface{}{
		"total_students":      total,
		"registered_students": total, // All students are considered registered
		"enrolled_students":   0,     // TODO: Implement when enrollment is available
		"approved_students":   0,     // TODO: Implement when enrollment is available
	}

	return stats, nil
}
