package services

import (
	"context"

	"app/internal/auth"
)

// authService implements AuthService
type authService struct {
	deps       *Dependencies
	authEngine *auth.AuthService
}

// NewAuthService creates a new auth service
func NewAuthService(deps *Dependencies) AuthService {
	authEngine := auth.NewAuthService(deps.Config, deps.Logger)
	
	return &authService{
		deps:       deps,
		authEngine: authEngine,
	}
}

// <PERSON><PERSON> authenticates a user and creates a session
func (a *authService) Login(ctx context.Context, username, password string) (string, error) {
	token, err := a.authEngine.Login(ctx, username, password)
	if err != nil {
		a.deps.Logger.Error("Login failed", "username", username, "error", err)
		return "", err
	}

	a.deps.Logger.Info("User logged in successfully", "username", username)
	return token, nil
}

// ValidateSession validates a session token
func (a *authService) ValidateSession(ctx context.Context, sessionToken string) (bool, error) {
	return a.authEngine.ValidateSession(ctx, sessionToken)
}

// Logout removes a session
func (a *authService) Logout(ctx context.Context, sessionToken string) error {
	return a.authEngine.Logout(ctx, sessionToken)
}

// CreateSession creates a new session for a user
func (a *authService) CreateSession(ctx context.Context, username string) (string, error) {
	return a.authEngine.CreateSession(ctx, username)
}

// RefreshSession extends the expiration time of a session
func (a *authService) RefreshSession(ctx context.Context, sessionToken string) (string, error) {
	return a.authEngine.RefreshSession(ctx, sessionToken)
}

// GetSessionUser returns the username for a session
func (a *authService) GetSessionUser(ctx context.Context, sessionToken string) (string, error) {
	return a.authEngine.GetSessionUser(ctx, sessionToken)
}
