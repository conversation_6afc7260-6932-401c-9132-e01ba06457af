package services_test

import (
	"context"
	"fmt"
	"testing"

	"app/internal/errors"
	"app/internal/models"
	"app/tests/testutils"
)

func TestStudentService_CreateStudent(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("successful creation", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		
		student, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, int32(0), student.ID)
		testutils.AssertEqual(t, req.Name, student.Name)
		testutils.AssertEqual(t, req.Email, student.Email)
		testutils.AssertEqual(t, "SSV College", student.College) // Default college
	})

	t.Run("validation error", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		req.Email = "invalid-email" // Invalid email format
		
		_, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeValidation, appErr.Code)
	})

	t.Run("duplicate email", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		
		// Create first student
		_, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		// Try to create student with same email
		req2 := testutils.CreateTestStudent()
		req2.AadharNumber = "123456789013" // Different Aadhar
		req2.AbcID = "ABC123456790"        // Different ABC ID
		
		_, err = svc.Student().CreateStudent(ctx, req2)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentExists, appErr.Code)
	})

	t.Run("duplicate aadhar", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		req.Email = "<EMAIL>"
		
		// Create first student
		_, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		// Try to create student with same Aadhar
		req2 := testutils.CreateTestStudent()
		req2.Email = "<EMAIL>"
		req2.AbcID = "ABC123456791" // Different ABC ID
		
		_, err = svc.Student().CreateStudent(ctx, req2)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentExists, appErr.Code)
	})
}

func TestStudentService_GetStudentByID(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("existing student", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		created, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		retrieved, err := svc.Student().GetStudentByID(ctx, created.ID)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, retrieved.ID)
		testutils.AssertEqual(t, created.Name, retrieved.Name)
	})

	t.Run("invalid ID", func(t *testing.T) {
		_, err := svc.Student().GetStudentByID(ctx, 0)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeBadRequest, appErr.Code)
	})

	t.Run("non-existent student", func(t *testing.T) {
		_, err := svc.Student().GetStudentByID(ctx, 99999)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentService_GetStudentByEmail(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("existing email", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		created, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		retrieved, err := svc.Student().GetStudentByEmail(ctx, created.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, retrieved.ID)
		testutils.AssertEqual(t, created.Email, retrieved.Email)
	})

	t.Run("empty email", func(t *testing.T) {
		_, err := svc.Student().GetStudentByEmail(ctx, "")
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeBadRequest, appErr.Code)
	})

	t.Run("non-existent email", func(t *testing.T) {
		_, err := svc.Student().GetStudentByEmail(ctx, "<EMAIL>")
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentService_GetAllStudents(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	// Create multiple students
	for i := 0; i < 5; i++ {
		req := testutils.CreateTestStudent()
		req.Email = fmt.Sprintf("<EMAIL>", i)
		req.AadharNumber = fmt.Sprintf("12345678901%d", i)
		req.AbcID = fmt.Sprintf("ABC12345678%d", i)
		
		_, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)
	}

	t.Run("get all with pagination", func(t *testing.T) {
		students, total, err := svc.Student().GetAllStudents(ctx, 1, 3) // Page 1, 3 per page
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 3, len(students))
		testutils.AssertEqual(t, int64(5), total)

		students, total, err = svc.Student().GetAllStudents(ctx, 2, 3) // Page 2, 3 per page
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 2, len(students))
		testutils.AssertEqual(t, int64(5), total)
	})

	t.Run("invalid pagination parameters", func(t *testing.T) {
		students, total, err := svc.Student().GetAllStudents(ctx, 0, 0) // Invalid params
		testutils.AssertNoError(t, err) // Should not error, but use defaults
		testutils.AssertEqual(t, 5, len(students)) // Should return all students with default page size
		testutils.AssertEqual(t, int64(5), total)
	})
}

func TestStudentService_UpdateStudent(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("successful update", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		created, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		updateReq := &models.UpdateStudentRequest{
			ID:      created.ID,
			Name:    "Updated Name",
			Subject: "Updated Subject",
		}

		updated, err := svc.Student().UpdateStudent(ctx, updateReq)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, updated.ID)
		testutils.AssertEqual(t, "Updated Name", updated.Name)
		testutils.AssertEqual(t, "Updated Subject", updated.Subject)
		testutils.AssertEqual(t, created.Email, updated.Email) // Unchanged
	})

	t.Run("invalid ID", func(t *testing.T) {
		updateReq := &models.UpdateStudentRequest{
			ID:   0,
			Name: "Updated Name",
		}

		_, err := svc.Student().UpdateStudent(ctx, updateReq)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeBadRequest, appErr.Code)
	})

	t.Run("non-existent student", func(t *testing.T) {
		updateReq := &models.UpdateStudentRequest{
			ID:   99999,
			Name: "Updated Name",
		}

		_, err := svc.Student().UpdateStudent(ctx, updateReq)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})

	t.Run("update email to existing email", func(t *testing.T) {
		// Create two students
		req1 := testutils.CreateTestStudent()
		student1, err := svc.Student().CreateStudent(ctx, req1)
		testutils.AssertNoError(t, err)

		req2 := testutils.CreateTestStudent()
		req2.Email = "<EMAIL>"
		req2.AadharNumber = "123456789013"
		req2.AbcID = "ABC123456790"
		student2, err := svc.Student().CreateStudent(ctx, req2)
		testutils.AssertNoError(t, err)

		// Try to update student2's email to student1's email
		updateReq := &models.UpdateStudentRequest{
			ID:    student2.ID,
			Email: student1.Email,
		}

		_, err = svc.Student().UpdateStudent(ctx, updateReq)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentExists, appErr.Code)
	})
}

func TestStudentService_DeleteStudent(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("successful deletion", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		created, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)

		err = svc.Student().DeleteStudent(ctx, created.ID)
		testutils.AssertNoError(t, err)

		// Verify student is deleted
		_, err = svc.Student().GetStudentByID(ctx, created.ID)
		testutils.AssertError(t, err)
	})

	t.Run("invalid ID", func(t *testing.T) {
		err := svc.Student().DeleteStudent(ctx, 0)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeBadRequest, appErr.Code)
	})

	t.Run("non-existent student", func(t *testing.T) {
		err := svc.Student().DeleteStudent(ctx, 99999)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentService_SearchStudent(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	req := testutils.CreateTestStudent()
	created, err := svc.Student().CreateStudent(ctx, req)
	testutils.AssertNoError(t, err)

	tests := []struct {
		name        string
		searchType  string
		searchValue string
		shouldFind  bool
		expectedErr errors.ErrorCode
	}{
		{"search by email", "email", created.Email, true, ""},
		{"search by aadhar", "aadhar_number", created.AadharNumber, true, ""},
		{"search by registration", "registration_number", created.RegistrationNumber, true, ""},
		{"search by university roll", "university_roll_number", created.UniversityRollNumber, true, ""},
		{"invalid search type", "invalid_type", "value", false, errors.ErrCodeBadRequest},
		{"empty search value", "email", "", false, errors.ErrCodeBadRequest},
		{"non-existent email", "email", "<EMAIL>", false, errors.ErrCodeStudentNotFound},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			searchReq := &models.StudentSearchRequest{
				SearchType:  tt.searchType,
				SearchValue: tt.searchValue,
			}
			
			result, err := svc.Student().SearchStudent(ctx, searchReq)
			
			if tt.shouldFind {
				testutils.AssertNoError(t, err)
				testutils.AssertEqual(t, created.ID, result.ID)
			} else {
				testutils.AssertError(t, err)
				testutils.AssertEqual(t, true, errors.IsAppError(err))
				
				if tt.expectedErr != "" {
					appErr := errors.GetAppError(err)
					testutils.AssertEqual(t, tt.expectedErr, appErr.Code)
				}
			}
		})
	}
}

func TestStudentService_ValidateStudentExists(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	req := testutils.CreateTestStudent()
	created, err := svc.Student().CreateStudent(ctx, req)
	testutils.AssertNoError(t, err)

	t.Run("existing student", func(t *testing.T) {
		exists, err := svc.Student().ValidateStudentExists(ctx, "email", created.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, exists)
	})

	t.Run("non-existent student", func(t *testing.T) {
		exists, err := svc.Student().ValidateStudentExists(ctx, "email", "<EMAIL>")
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, exists)
	})

	t.Run("empty search value", func(t *testing.T) {
		_, err := svc.Student().ValidateStudentExists(ctx, "email", "")
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))
		
		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeBadRequest, appErr.Code)
	})
}
