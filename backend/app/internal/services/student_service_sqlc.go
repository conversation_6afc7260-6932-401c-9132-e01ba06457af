package services

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"app/data"
	"app/internal/models"
)

// StudentServiceSQLC provides student-related operations using SQLC
type StudentServiceSQLC struct {
	queries *data.Queries
	db      *sql.DB
}

// NewStudentServiceSQLC creates a new student service with SQLC
func NewStudentServiceSQLC(db *sql.DB) *StudentServiceSQLC {
	return &StudentServiceSQLC{
		queries: data.New(db),
		db:      db,
	}
}

// RegisterStudent creates a new student registration using SQLC
func (s *StudentServiceSQLC) RegisterStudent(ctx context.Context, req *models.StudentRegistrationRequest) (*models.Student, error) {
	// Parse date of birth
	dob, err := time.Parse("2006-01-02", req.DateOfBirth)
	if err != nil {
		return nil, fmt.Errorf("invalid date format: %w", err)
	}

	// Create student using SQLC
	params := data.CreateStudentRegistrationParams{
		Name:         req.Name,
		Email:        req.Email,
		DateOfBirth:  dob,
		Gender:       req.Gender,
		Category:     req.Category,
		MobileNumber: req.MobileNumber,
		FatherName:   req.FatherName,
		MotherName:   req.MotherName,
		Pincode:      req.Pincode,
		State:        req.State,
		AadharNumber: req.AadharNumber,
		AbcID:        req.AbcID,
		AadharMobile: req.AadharMobile,
	}

	student, err := s.queries.CreateStudentRegistration(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create student: %w", err)
	}

	// Convert to models.Student
	return &models.Student{
		ID:           student.ID,
		Name:         student.Name,
		Email:        student.Email,
		DateOfBirth:  student.DateOfBirth.Format("2006-01-02"),
		Gender:       student.Gender,
		Category:     student.Category,
		MobileNumber: student.MobileNumber,
		FatherName:   student.FatherName,
		MotherName:   student.MotherName,
		Pincode:      student.Pincode,
		State:        student.State,
		AadharNumber: student.AadharNumber,
		AbcID:        student.AbcID,
		AadharMobile: student.AadharMobile,
		Status:       student.Status,
		CreatedAt:    student.CreatedAt,
		UpdatedAt:    student.UpdatedAt,
	}, nil
}

// GetStudentByEmail retrieves a student by email using SQLC
func (s *StudentServiceSQLC) GetStudentByEmail(ctx context.Context, email string) (*models.Student, error) {
	student, err := s.queries.GetStudentByEmail(ctx, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	return &models.Student{
		ID:           student.ID,
		Name:         student.Name,
		Email:        student.Email,
		DateOfBirth:  student.DateOfBirth.Format("2006-01-02"),
		Gender:       student.Gender,
		Category:     student.Category,
		MobileNumber: student.MobileNumber,
		FatherName:   student.FatherName,
		MotherName:   student.MotherName,
		Pincode:      student.Pincode,
		State:        student.State,
		AadharNumber: student.AadharNumber,
		AbcID:        student.AbcID,
		AadharMobile: student.AadharMobile,
		Status:       student.Status,
		CreatedAt:    student.CreatedAt,
		UpdatedAt:    student.UpdatedAt,
	}, nil
}

// EnrollStudent creates a new enrollment using SQLC
func (s *StudentServiceSQLC) EnrollStudent(ctx context.Context, req *models.EnrollmentRequest) (*models.Enrollment, error) {
	// First, get the student by email
	student, err := s.queries.GetStudentByEmail(ctx, req.StudentEmail)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("student not found. Please register first")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	// Create enrollment using SQLC
	params := data.CreateStudentEnrollmentParams{
		StudentID:    student.ID,
		DepartmentID: req.DepartmentID,
		Session:      req.Session,
		Subject:      req.Subject,
	}

	enrollment, err := s.queries.CreateStudentEnrollment(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to create enrollment: %w", err)
	}

	// Update student status to enrolled
	_, err = s.queries.UpdateStudentStatus(ctx, data.UpdateStudentStatusParams{
		ID:     student.ID,
		Status: "enrolled",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to update student status: %w", err)
	}

	// Get department info
	department, err := s.queries.GetDepartmentByID(ctx, req.DepartmentID)
	if err != nil {
		return nil, fmt.Errorf("failed to get department: %w", err)
	}

	// Convert to models.Enrollment
	return &models.Enrollment{
		ID:           enrollment.ID,
		StudentID:    enrollment.StudentID,
		DepartmentID: enrollment.DepartmentID,
		Session:      enrollment.Session,
		Subject:      enrollment.Subject,
		Status:       enrollment.Status,
		Student: &models.Student{
			ID:           student.ID,
			Name:         student.Name,
			Email:        student.Email,
			DateOfBirth:  student.DateOfBirth.Format("2006-01-02"),
			Gender:       student.Gender,
			Category:     student.Category,
			MobileNumber: student.MobileNumber,
			Status:       "enrolled", // Updated status
			CreatedAt:    student.CreatedAt,
			UpdatedAt:    student.UpdatedAt,
		},
		Department: &models.Department{
			ID:          department.ID,
			Name:        department.Name,
			Code:        department.Code,
			Description: department.Description.String,
		},
		CreatedAt: enrollment.CreatedAt,
		UpdatedAt: enrollment.UpdatedAt,
	}, nil
}

// GetStudentEnrollments retrieves all enrollments for a student using SQLC
func (s *StudentServiceSQLC) GetStudentEnrollments(ctx context.Context, email string) (*models.StudentStatusResponse, error) {
	// Get student by email
	student, err := s.queries.GetStudentByEmail(ctx, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	// Get enrollments for the student
	enrollments, err := s.queries.GetEnrollmentsByStudent(ctx, student.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enrollments: %w", err)
	}

	// Convert to response format
	var enrollmentList []models.Enrollment
	for _, e := range enrollments {
		// Get department info
		department, err := s.queries.GetDepartmentByID(ctx, e.DepartmentID)
		if err != nil {
			continue // Skip if department not found
		}

		enrollment := models.Enrollment{
			ID:           e.ID,
			StudentID:    e.StudentID,
			DepartmentID: e.DepartmentID,
			Session:      e.Session,
			Subject:      e.Subject,
			Status:       e.Status,
			Department: &models.Department{
				ID:          department.ID,
				Name:        department.Name,
				Code:        department.Code,
				Description: department.Description.String,
			},
			CreatedAt: e.CreatedAt,
			UpdatedAt: e.UpdatedAt,
		}

		// Add roll numbers if available
		if e.ClassRollNumber.Valid {
			enrollment.ClassRollNumber = e.ClassRollNumber.String
		}
		if e.UniversityRollNumber.Valid {
			enrollment.UniversityRollNumber = e.UniversityRollNumber.String
		}
		if e.RegistrationNumber.Valid {
			enrollment.RegistrationNumber = e.RegistrationNumber.String
		}

		enrollmentList = append(enrollmentList, enrollment)
	}

	return &models.StudentStatusResponse{
		Student: &models.Student{
			ID:           student.ID,
			Name:         student.Name,
			Email:        student.Email,
			DateOfBirth:  student.DateOfBirth.Format("2006-01-02"),
			Gender:       student.Gender,
			Category:     student.Category,
			MobileNumber: student.MobileNumber,
			Status:       student.Status,
			CreatedAt:    student.CreatedAt,
			UpdatedAt:    student.UpdatedAt,
		},
		Enrollments: enrollmentList,
	}, nil
}

// GetAllDepartments retrieves all departments using SQLC
func (s *StudentServiceSQLC) GetAllDepartments(ctx context.Context) ([]models.Department, error) {
	departments, err := s.queries.GetAllDepartments(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get departments: %w", err)
	}

	var result []models.Department
	for _, d := range departments {
		dept := models.Department{
			ID:   d.ID,
			Name: d.Name,
			Code: d.Code,
		}
		if d.Description.Valid {
			dept.Description = d.Description.String
		}
		result = append(result, dept)
	}

	return result, nil
}
