package services

import (
	"context"
	"log/slog"

	"app/internal/config"
	"app/internal/models"
	"app/internal/repository"
)

// StudentService defines the interface for student business logic
type StudentService interface {
	// Create operations
	CreateStudent(ctx context.Context, req *models.CreateStudentRequest) (*models.Student, error)

	// Read operations
	GetStudentByID(ctx context.Context, id int32) (*models.Student, error)
	GetStudentByEmail(ctx context.Context, email string) (*models.Student, error)
	GetAllStudents(ctx context.Context, page, pageSize int) ([]*models.Student, int64, error)

	// Update operations
	UpdateStudent(ctx context.Context, req *models.UpdateStudentRequest) (*models.Student, error)

	// Delete operations
	DeleteStudent(ctx context.Context, id int32) error

	// Search operations
	SearchStudent(ctx context.Context, req *models.StudentSearchRequest) (*models.Student, error)

	// Validation operations
	ValidateStudentExists(ctx context.Context, searchType, searchValue string) (bool, error)

	// New enrollment operations (temporary placeholders)
	RegisterStudent(ctx context.Context, req *models.CreateStudentRequest) (*models.Student, error)
	EnrollStudent(ctx context.Context, req *models.CreateEnrollmentRequest) (*models.StudentEnrollment, error)
	GetStudentEnrollments(ctx context.Context, studentID int32) ([]*models.StudentEnrollment, error)
	GetStudentStats(ctx context.Context) (map[string]interface{}, error)
}

// ExamFormService defines the interface for exam form business logic
type ExamFormService interface {
	// Create operations
	CreateExamForm(ctx context.Context, req *models.CreateExamFormRequest) (*models.ExamForm, error)

	// Read operations
	GetExamFormByID(ctx context.Context, id int32) (*models.ExamForm, error)
	GetExamFormByRollNumber(ctx context.Context, rollNumber string) (*models.ExamForm, error)
	GetAllExamForms(ctx context.Context, page, pageSize int) ([]*models.ExamForm, int64, error)
	GetExamFormsByCategory(ctx context.Context, category string) ([]*models.ExamForm, error)

	// Update operations
	UpdateExamForm(ctx context.Context, req *models.UpdateExamFormRequest) (*models.ExamForm, error)

	// Delete operations
	DeleteExamForm(ctx context.Context, id int32) error
	DeleteExamFormByRollNumber(ctx context.Context, rollNumber string) error

	// Search operations
	SearchExamForms(ctx context.Context, req *models.ExamFormSearchRequest) ([]*models.ExamForm, error)

	// Business logic operations
	GenerateExamFormWithStudent(ctx context.Context, rollNumber string) (*models.ExamFormWithStudent, error)
	ValidateExamFormEligibility(ctx context.Context, rollNumber string) error
	CalculateExamFee(ctx context.Context, req *models.CreateExamFormRequest) (string, error)
}

// AuthService defines the interface for authentication business logic
type AuthService interface {
	// Authentication operations
	Login(ctx context.Context, username, password string) (string, error)
	ValidateSession(ctx context.Context, sessionToken string) (bool, error)
	Logout(ctx context.Context, sessionToken string) error

	// Session management
	CreateSession(ctx context.Context, username string) (string, error)
	RefreshSession(ctx context.Context, sessionToken string) (string, error)
	GetSessionUser(ctx context.Context, sessionToken string) (string, error)
}

// PDFService defines the interface for PDF generation
type PDFService interface {
	// PDF generation operations
	GenerateExamForm(ctx context.Context, examForm *models.ExamFormWithStudent) ([]byte, error)
	GenerateAdmitCard(ctx context.Context, examForm *models.ExamFormWithStudent) ([]byte, error)
	GenerateBulkForms(ctx context.Context, examForms []*models.ExamFormWithStudent) ([]byte, error)
}

// UserService defines the interface for user management
type UserService interface {
	// Create operations
	CreateUser(ctx context.Context, req *models.CreateUserRequest, createdByUserID int32) (*models.User, error)

	// Read operations
	GetUserByID(ctx context.Context, id int32) (*models.User, error)
	GetUserByEmail(ctx context.Context, email string) (*models.User, error)
	GetAllUsers(ctx context.Context) ([]*models.User, error)
	GetUsersByDepartment(ctx context.Context, departmentID int32) ([]*models.User, error)

	// Update operations
	UpdateUser(ctx context.Context, user *models.User, updatedByUserID int32) error
	ToggleUserStatus(ctx context.Context, userID int32, toggledByUserID int32) error
	ChangeUserPassword(ctx context.Context, userID int32, newPassword string, changedByUserID int32) error

	// Delete operations
	DeleteUser(ctx context.Context, userID int32, deletedByUserID int32) error

	// Statistics
	GetUserStats(ctx context.Context) (map[string]interface{}, error)
}

// DepartmentService defines the interface for department management
type DepartmentService interface {
	// Read operations
	GetAllDepartments(ctx context.Context) ([]*models.Department, error)
	GetDepartmentByID(ctx context.Context, id int32) (*models.Department, error)
	GetDepartmentByCode(ctx context.Context, code string) (*models.Department, error)
	GetDepartmentSubjects(ctx context.Context, departmentID int32) ([]string, error)
	GetDepartmentSubjectsByCode(ctx context.Context, code string) ([]string, error)

	// Create operations
	CreateDepartment(ctx context.Context, req *models.CreateDepartmentRequest, createdByUserID int32) (*models.Department, error)

	// Update operations
	UpdateDepartment(ctx context.Context, department *models.Department, updatedByUserID int32) error

	// Delete operations
	DeleteDepartment(ctx context.Context, departmentID int32, deletedByUserID int32) error

	// Validation
	ValidateSubjectForDepartment(ctx context.Context, departmentID int32, subject string) (bool, error)

	// Statistics
	GetDepartmentStats(ctx context.Context) (map[string]interface{}, error)
	GetDepartmentsWithUserCounts(ctx context.Context) ([]map[string]interface{}, error)
}

// EnrollmentService defines the interface for enrollment management
type EnrollmentService interface {
	// Read operations
	GetAllEnrollments(ctx context.Context) ([]*models.StudentEnrollment, error)
	GetPendingEnrollments(ctx context.Context) ([]*models.StudentEnrollment, error)
	GetEnrollmentByID(ctx context.Context, id int32) (*models.StudentEnrollment, error)
	GetEnrollmentsByDepartment(ctx context.Context, departmentID int32) ([]*models.StudentEnrollment, error)
	GetPendingEnrollmentsByDepartment(ctx context.Context, departmentID int32) ([]*models.StudentEnrollment, error)

	// Update operations
	ApproveEnrollment(ctx context.Context, enrollmentID int32, req *models.ApproveEnrollmentRequest, approvedByUserID int32) error
	RejectEnrollment(ctx context.Context, enrollmentID int32, req *models.RejectEnrollmentRequest, rejectedByUserID int32) error

	// Delete operations
	DeleteEnrollment(ctx context.Context, id int32) error

	// Permissions
	CanUserManageEnrollment(ctx context.Context, userID, enrollmentID int32) (bool, error)

	// Statistics
	GetEnrollmentStats(ctx context.Context) (map[string]interface{}, error)
}

// Services aggregates all service interfaces
type Services struct {
	Student    StudentService
	ExamForm   ExamFormService
	Auth       AuthService
	PDF        PDFService
	User       UserService
	Department DepartmentService
	Enrollment EnrollmentService
}

// ServiceManager manages service instances and dependencies
type ServiceManager interface {
	// Service access
	Student() StudentService
	ExamForm() ExamFormService
	Auth() AuthService
	PDF() PDFService
	User() UserService
	Department() DepartmentService
	Enrollment() EnrollmentService

	// Health check
	HealthCheck(ctx context.Context) error
}

// Dependencies holds all dependencies needed by services
type Dependencies struct {
	Config     *config.Config
	Repository repository.RepositoryManager
	Logger     *slog.Logger
}

// NewServiceManager creates a new service manager
func NewServiceManager(deps *Dependencies) ServiceManager {
	return &serviceManager{
		config:     deps.Config,
		repository: deps.Repository,
		logger:     deps.Logger,
		student:    NewStudentService(deps),
		examForm:   NewExamFormService(deps),
		auth:       NewAuthService(deps),
		pdf:        NewPDFService(deps),
		user:       NewUserServiceImpl(deps),
		department: NewDepartmentServiceImpl(deps),
		enrollment: NewEnrollmentServiceImpl(deps),
	}
}

// serviceManager implements ServiceManager
type serviceManager struct {
	config     *config.Config
	repository repository.RepositoryManager
	logger     *slog.Logger
	student    StudentService
	examForm   ExamFormService
	auth       AuthService
	pdf        PDFService
	user       UserService
	department DepartmentService
	enrollment EnrollmentService
}

// Student returns the student service
func (s *serviceManager) Student() StudentService {
	return s.student
}

// ExamForm returns the exam form service
func (s *serviceManager) ExamForm() ExamFormService {
	return s.examForm
}

// Auth returns the auth service
func (s *serviceManager) Auth() AuthService {
	return s.auth
}

// PDF returns the PDF service
func (s *serviceManager) PDF() PDFService {
	return s.pdf
}

// User returns the user service
func (s *serviceManager) User() UserService {
	return s.user
}

// Department returns the department service
func (s *serviceManager) Department() DepartmentService {
	return s.department
}

// Enrollment returns the enrollment service
func (s *serviceManager) Enrollment() EnrollmentService {
	return s.enrollment
}

// HealthCheck performs a health check on all services
func (s *serviceManager) HealthCheck(ctx context.Context) error {
	// Check database connection
	if err := s.repository.Ping(ctx); err != nil {
		s.logger.Error("Database health check failed", "error", err)
		return err
	}

	s.logger.Info("Health check passed")
	return nil
}
