package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"app/data"
	"app/internal/errors"
	"app/internal/models"

	"github.com/jackc/pgx/v5/pgconn"
)

// studentRepository implements StudentRepository
type studentRepository struct {
	db      *sql.DB
	queries *data.Queries
}

// NewStudentRepository creates a new student repository
func NewStudentRepository(db *sql.DB) StudentRepository {
	return &studentRepository{
		db:      db,
		queries: data.New(db),
	}
}

// Create creates a new student
func (r *studentRepository) Create(ctx context.Context, student *models.Student) (*models.Student, error) {
	params := data.CreateStudentParams{
		Name:                 student.Name,
		Email:                student.Email,
		DateOfBirth:          student.DateOfBirth,
		Gender:               student.Gender,
		Category:             student.Category,
		MobileNumber:         student.MobileNumber,
		Session:              student.Session,
		Subject:              student.Subject,
		ClassRollNumber:      student.ClassRollNumber,
		UniversityRollNumber: student.UniversityRollNumber,
		RegistrationNumber:   student.RegistrationNumber,
		FatherName:           student.FatherName,
		MotherName:           student.MotherName,
		Pincode:              student.Pincode,
		State:                student.State,
		AadharNumber:         student.AadharNumber,
		AbcID:                student.AbcID,
		AadharMobile:         student.AadharMobile,
	}

	result, err := r.queries.CreateStudent(ctx, params)
	if err != nil {
		return nil, r.handleDatabaseError(err, "create student")
	}

	return r.convertToModel(&result), nil
}

// GetByID retrieves a student by ID
func (r *studentRepository) GetByID(ctx context.Context, id int32) (*models.Student, error) {
	result, err := r.queries.GetStudentByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, errors.Database("failed to get student by ID", err)
	}

	return r.convertToModel(&result), nil
}

// GetByEmail retrieves a student by email
func (r *studentRepository) GetByEmail(ctx context.Context, email string) (*models.Student, error) {
	result, err := r.queries.GetStudentByEmail(ctx, email)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, errors.Database("failed to get student by email", err)
	}

	return r.convertToModel(&result), nil
}

// GetByAadhar retrieves a student by Aadhar number
func (r *studentRepository) GetByAadhar(ctx context.Context, aadhar string) (*models.Student, error) {
	result, err := r.queries.GetStudentByAadhar(ctx, aadhar)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, errors.Database("failed to get student by Aadhar", err)
	}

	return r.convertToModel(&result), nil
}

// GetByRegistrationNumber retrieves a student by registration number
func (r *studentRepository) GetByRegistrationNumber(ctx context.Context, regNumber string) (*models.Student, error) {
	result, err := r.queries.GetStudentByRegistrationNumber(ctx, regNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, errors.Database("failed to get student by registration number", err)
	}

	return r.convertToModel(&result), nil
}

// GetByUniversityRollNumber retrieves a student by university roll number
func (r *studentRepository) GetByUniversityRollNumber(ctx context.Context, rollNumber string) (*models.Student, error) {
	result, err := r.queries.GetStudentByUniversityRollNumber(ctx, rollNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, errors.Database("failed to get student by university roll number", err)
	}

	return r.convertToModel(&result), nil
}

// GetAll retrieves all students with pagination
func (r *studentRepository) GetAll(ctx context.Context, limit, offset int) ([]*models.Student, error) {
	params := data.GetAllStudentsParams{
		Limit:  int32(limit),
		Offset: int32(offset),
	}

	results, err := r.queries.GetAllStudents(ctx, params)
	if err != nil {
		return nil, errors.Database("failed to get all students", err)
	}

	students := make([]*models.Student, len(results))
	for i, result := range results {
		students[i] = r.convertToModel(&result)
	}

	return students, nil
}

// Update updates a student
func (r *studentRepository) Update(ctx context.Context, student *models.Student) (*models.Student, error) {
	params := data.UpdateStudentParams{
		ID:                   student.ID,
		Name:                 student.Name,
		Email:                student.Email,
		DateOfBirth:          student.DateOfBirth,
		Gender:               student.Gender,
		Category:             student.Category,
		MobileNumber:         student.MobileNumber,
		Session:              student.Session,
		Subject:              student.Subject,
		ClassRollNumber:      student.ClassRollNumber,
		UniversityRollNumber: student.UniversityRollNumber,
		RegistrationNumber:   student.RegistrationNumber,
		FatherName:           student.FatherName,
		MotherName:           student.MotherName,
		Pincode:              student.Pincode,
		State:                student.State,
		AadharNumber:         student.AadharNumber,
		AbcID:                student.AbcID,
		AadharMobile:         student.AadharMobile,
	}

	result, err := r.queries.UpdateStudent(ctx, params)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.StudentNotFound("student not found")
		}
		return nil, r.handleDatabaseError(err, "update student")
	}

	return r.convertToModel(&result), nil
}

// Delete deletes a student by ID
func (r *studentRepository) Delete(ctx context.Context, id int32) error {
	err := r.queries.DeleteStudent(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.StudentNotFound("student not found")
		}
		return errors.Database("failed to delete student", err)
	}

	return nil
}

// Search searches for a student based on search type and value
func (r *studentRepository) Search(ctx context.Context, searchType, searchValue string) (*models.Student, error) {
	switch searchType {
	case "email":
		return r.GetByEmail(ctx, searchValue)
	case "aadhar_number":
		return r.GetByAadhar(ctx, searchValue)
	case "registration_number":
		return r.GetByRegistrationNumber(ctx, searchValue)
	case "university_roll_number":
		return r.GetByUniversityRollNumber(ctx, searchValue)
	default:
		return nil, errors.BadRequest("invalid search type")
	}
}

// Count returns the total number of students
func (r *studentRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.queries.CountStudents(ctx)
	if err != nil {
		return 0, errors.Database("failed to count students", err)
	}

	return count, nil
}

// convertToModel converts data.Student to models.Student
func (r *studentRepository) convertToModel(student *data.Student) *models.Student {
	return &models.Student{
		ID:                   student.ID,
		Name:                 student.Name,
		Email:                student.Email,
		DateOfBirth:          student.DateOfBirth,
		Gender:               student.Gender,
		Category:             student.Category,
		MobileNumber:         student.MobileNumber,
		College:              student.College,
		Session:              student.Session,
		Subject:              student.Subject,
		ClassRollNumber:      student.ClassRollNumber,
		UniversityRollNumber: student.UniversityRollNumber,
		RegistrationNumber:   student.RegistrationNumber,
		FatherName:           student.FatherName,
		MotherName:           student.MotherName,
		Pincode:              student.Pincode,
		State:                student.State,
		AadharNumber:         student.AadharNumber,
		AbcID:                student.AbcID,
		AadharMobile:         student.AadharMobile,
		CreatedAt:            student.CreatedAt,
		UpdatedAt:            student.UpdatedAt,
	}
}

// handleDatabaseError handles database-specific errors
func (r *studentRepository) handleDatabaseError(err error, operation string) error {
	if pgErr, ok := err.(*pgconn.PgError); ok {
		switch pgErr.Code {
		case "23505": // Unique violation
			if strings.Contains(pgErr.Detail, "email") {
				return errors.StudentExists("student with this email already exists")
			}
			if strings.Contains(pgErr.Detail, "aadhar_number") {
				return errors.StudentExists("student with this Aadhar number already exists")
			}
			if strings.Contains(pgErr.Detail, "registration_number") {
				return errors.StudentExists("student with this registration number already exists")
			}
			return errors.StudentExists("student already exists")
		case "23503": // Foreign key violation
			return errors.BadRequest("invalid reference data")
		case "23514": // Check constraint violation
			return errors.BadRequest("invalid data format")
		default:
			return errors.Database(fmt.Sprintf("database error during %s", operation), err)
		}
	}

	return errors.Database(fmt.Sprintf("failed to %s", operation), err)
}
