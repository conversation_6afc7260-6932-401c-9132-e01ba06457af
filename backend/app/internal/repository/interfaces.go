package repository

import (
	"context"
	"database/sql"

	"app/internal/models"
)

// StudentRepository defines the interface for student data operations
type StudentRepository interface {
	// Create operations
	Create(ctx context.Context, student *models.Student) (*models.Student, error)
	
	// Read operations
	GetByID(ctx context.Context, id int32) (*models.Student, error)
	GetByEmail(ctx context.Context, email string) (*models.Student, error)
	GetByAadhar(ctx context.Context, aadhar string) (*models.Student, error)
	GetByRegistrationNumber(ctx context.Context, regNumber string) (*models.Student, error)
	GetByUniversityRollNumber(ctx context.Context, rollNumber string) (*models.Student, error)
	GetAll(ctx context.Context, limit, offset int) ([]*models.Student, error)
	
	// Update operations
	Update(ctx context.Context, student *models.Student) (*models.Student, error)
	
	// Delete operations
	Delete(ctx context.Context, id int32) error
	
	// Search operations
	Search(ctx context.Context, searchType, searchValue string) (*models.Student, error)
	
	// Count operations
	Count(ctx context.Context) (int64, error)
}

// ExamFormRepository defines the interface for exam form data operations
type ExamFormRepository interface {
	// Create operations
	Create(ctx context.Context, examForm *models.ExamForm) (*models.ExamForm, error)
	
	// Read operations
	GetByID(ctx context.Context, id int32) (*models.ExamForm, error)
	GetByRollNumber(ctx context.Context, rollNumber string) (*models.ExamForm, error)
	GetAll(ctx context.Context, limit, offset int) ([]*models.ExamForm, error)
	GetByCategory(ctx context.Context, category string) ([]*models.ExamForm, error)
	GetByExamType(ctx context.Context, isRegular bool) ([]*models.ExamForm, error)
	
	// Update operations
	Update(ctx context.Context, examForm *models.ExamForm) (*models.ExamForm, error)
	
	// Delete operations
	Delete(ctx context.Context, id int32) error
	DeleteByRollNumber(ctx context.Context, rollNumber string) error
	
	// Search operations
	Search(ctx context.Context, criteria *models.ExamFormSearchRequest) ([]*models.ExamForm, error)
	
	// Count operations
	Count(ctx context.Context) (int64, error)
	CountByCategory(ctx context.Context, category string) (int64, error)
}

// Repository aggregates all repository interfaces
type Repository struct {
	Student  StudentRepository
	ExamForm ExamFormRepository
}

// Transactor defines the interface for transaction operations
type Transactor interface {
	// WithTx executes a function within a database transaction
	WithTx(ctx context.Context, fn func(ctx context.Context, tx *sql.Tx) error) error
}

// RepositoryManager manages repository instances and transactions
type RepositoryManager interface {
	// Repository access
	Student() StudentRepository
	ExamForm() ExamFormRepository
	
	// Transaction management
	Transactor
	
	// Health check
	Ping(ctx context.Context) error
	
	// Close closes the database connection
	Close() error
}

// NewRepository creates a new repository instance
func NewRepository(db *sql.DB) RepositoryManager {
	return &repositoryManager{
		db:       db,
		student:  NewStudentRepository(db),
		examForm: NewExamFormRepository(db),
	}
}

// repositoryManager implements RepositoryManager
type repositoryManager struct {
	db       *sql.DB
	student  StudentRepository
	examForm ExamFormRepository
}

// Student returns the student repository
func (r *repositoryManager) Student() StudentRepository {
	return r.student
}

// ExamForm returns the exam form repository
func (r *repositoryManager) ExamForm() ExamFormRepository {
	return r.examForm
}

// WithTx executes a function within a database transaction
func (r *repositoryManager) WithTx(ctx context.Context, fn func(ctx context.Context, tx *sql.Tx) error) error {
	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return err
	}
	
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()
	
	err = fn(ctx, tx)
	return err
}

// Ping checks the database connection
func (r *repositoryManager) Ping(ctx context.Context) error {
	return r.db.PingContext(ctx)
}

// Close closes the database connection
func (r *repositoryManager) Close() error {
	return r.db.Close()
}
