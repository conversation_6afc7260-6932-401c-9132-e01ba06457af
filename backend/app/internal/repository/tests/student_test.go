package repository_test

import (
	"context"
	"fmt"
	"testing"

	"app/internal/errors"
	"app/internal/repository"
	"app/tests/testutils"
)

func TestStudentRepository_Create(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("successful creation", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, int32(0), created.ID)
		testutils.AssertEqual(t, student.Name, created.Name)
		testutils.AssertEqual(t, student.Email, created.Email)
	})

	t.Run("duplicate email", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		// Create first student
		_, err = repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		// Try to create student with same email
		student2 := *student
		student2.AadharNumber = "123456789013" // Different Aadhar
		student2.AbcID = "ABC123456790"        // Different ABC ID
		_, err = repo.Create(ctx, &student2)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentExists, appErr.Code)
	})

	t.Run("duplicate aadhar", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		req.Email = "<EMAIL>"
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		// Create first student
		_, err = repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		// Try to create student with same Aadhar
		student2 := *student
		student2.Email = "<EMAIL>"
		student2.AbcID = "ABC123456791" // Different ABC ID
		_, err = repo.Create(ctx, &student2)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentExists, appErr.Code)
	})
}

func TestStudentRepository_GetByID(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("existing student", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		retrieved, err := repo.GetByID(ctx, created.ID)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, retrieved.ID)
		testutils.AssertEqual(t, created.Name, retrieved.Name)
		testutils.AssertEqual(t, created.Email, retrieved.Email)
	})

	t.Run("non-existent student", func(t *testing.T) {
		_, err := repo.GetByID(ctx, 99999)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentRepository_GetByEmail(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("existing email", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		retrieved, err := repo.GetByEmail(ctx, created.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, retrieved.ID)
		testutils.AssertEqual(t, created.Email, retrieved.Email)
	})

	t.Run("non-existent email", func(t *testing.T) {
		_, err := repo.GetByEmail(ctx, "<EMAIL>")
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentRepository_GetByAadhar(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("existing aadhar", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		retrieved, err := repo.GetByAadhar(ctx, created.AadharNumber)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, retrieved.ID)
		testutils.AssertEqual(t, created.AadharNumber, retrieved.AadharNumber)
	})

	t.Run("non-existent aadhar", func(t *testing.T) {
		_, err := repo.GetByAadhar(ctx, "************")
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentRepository_Update(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("successful update", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		// Update student
		created.Name = "Updated Name"
		created.Subject = "Updated Subject"

		updated, err := repo.Update(ctx, created)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, created.ID, updated.ID)
		testutils.AssertEqual(t, "Updated Name", updated.Name)
		testutils.AssertEqual(t, "Updated Subject", updated.Subject)
	})

	t.Run("update non-existent student", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)
		student.ID = 99999

		_, err = repo.Update(ctx, student)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentRepository_Delete(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	t.Run("successful deletion", func(t *testing.T) {
		req := testutils.CreateTestStudent()
		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		created, err := repo.Create(ctx, student)
		testutils.AssertNoError(t, err)

		err = repo.Delete(ctx, created.ID)
		testutils.AssertNoError(t, err)

		// Verify student is deleted
		_, err = repo.GetByID(ctx, created.ID)
		testutils.AssertError(t, err)
	})

	t.Run("delete non-existent student", func(t *testing.T) {
		err := repo.Delete(ctx, 99999)
		testutils.AssertError(t, err)
		testutils.AssertEqual(t, true, errors.IsAppError(err))

		appErr := errors.GetAppError(err)
		testutils.AssertEqual(t, errors.ErrCodeStudentNotFound, appErr.Code)
	})
}

func TestStudentRepository_GetAll(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	// Create multiple students
	for i := 0; i < 5; i++ {
		req := testutils.CreateTestStudent()
		req.Email = fmt.Sprintf("<EMAIL>", i)
		req.AadharNumber = fmt.Sprintf("12345678901%d", i)
		req.AbcID = fmt.Sprintf("ABC12345678%d", i)

		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		_, err = repo.Create(ctx, student)
		testutils.AssertNoError(t, err)
	}

	t.Run("get all with pagination", func(t *testing.T) {
		students, err := repo.GetAll(ctx, 3, 0) // Get first 3
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 3, len(students))

		students, err = repo.GetAll(ctx, 3, 3) // Get next 2
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 2, len(students))
	})
}

func TestStudentRepository_Search(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	req := testutils.CreateTestStudent()
	student, err := req.ToStudent()
	testutils.AssertNoError(t, err)

	created, err := repo.Create(ctx, student)
	testutils.AssertNoError(t, err)

	tests := []struct {
		name        string
		searchType  string
		searchValue string
		shouldFind  bool
	}{
		{"search by email", "email", created.Email, true},
		{"search by aadhar", "aadhar_number", created.AadharNumber, true},
		{"search by registration", "registration_number", created.RegistrationNumber, true},
		{"search by university roll", "university_roll_number", created.UniversityRollNumber, true},
		{"invalid search type", "invalid_type", "value", false},
		{"non-existent email", "email", "<EMAIL>", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := repo.Search(ctx, tt.searchType, tt.searchValue)

			if tt.shouldFind {
				if tt.searchType == "invalid_type" {
					testutils.AssertError(t, err)
				} else {
					testutils.AssertNoError(t, err)
					testutils.AssertEqual(t, created.ID, result.ID)
				}
			} else {
				testutils.AssertError(t, err)
			}
		})
	}
}

func TestStudentRepository_Count(t *testing.T) {
	db := testutils.SetupTestDB(t)
	defer testutils.TeardownTestDB(t, db)

	repo := repository.NewStudentRepository(db)
	ctx := context.Background()

	// Initially should be 0
	count, err := repo.Count(ctx)
	testutils.AssertNoError(t, err)
	testutils.AssertEqual(t, int64(0), count)

	// Create students
	for i := 0; i < 3; i++ {
		req := testutils.CreateTestStudent()
		req.Email = fmt.Sprintf("<EMAIL>", i)
		req.AadharNumber = fmt.Sprintf("12345678901%d", i)
		req.AbcID = fmt.Sprintf("ABC12345678%d", i)

		student, err := req.ToStudent()
		testutils.AssertNoError(t, err)

		_, err = repo.Create(ctx, student)
		testutils.AssertNoError(t, err)
	}

	// Should now be 3
	count, err = repo.Count(ctx)
	testutils.AssertNoError(t, err)
	testutils.AssertEqual(t, int64(3), count)
}
