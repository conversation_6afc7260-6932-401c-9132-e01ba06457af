package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"app/data"
	"app/internal/errors"
	"app/internal/models"

	"github.com/jackc/pgx/v5/pgconn"
)

// examFormRepository implements ExamFormRepository
type examFormRepository struct {
	db      *sql.DB
	queries *data.Queries
}

// NewExamFormRepository creates a new exam form repository
func NewExamFormRepository(db *sql.DB) ExamFormRepository {
	return &examFormRepository{
		db:      db,
		queries: data.New(db),
	}
}

// Create creates a new exam form
func (r *examFormRepository) Create(ctx context.Context, examForm *models.ExamForm) (*models.ExamForm, error) {
	params := data.CreateExamFormParams{
		RollNumber: examForm.RollNumber,
		Category:   examForm.Category,
		IsRegular:  examForm.IsRegular,
		PaperI:     examForm.PaperI,
		PaperIi:    examForm.PaperII,
		PaperIii:   examForm.PaperIII,
		PaperIv:    examForm.PaperIV,
		Fee:        examForm.Fee,
	}

	result, err := r.queries.CreateExamForm(ctx, params)
	if err != nil {
		return nil, r.handleDatabaseError(err, "create exam form")
	}

	return r.convertToModel(&result), nil
}

// GetByID retrieves an exam form by ID
func (r *examFormRepository) GetByID(ctx context.Context, id int32) (*models.ExamForm, error) {
	result, err := r.queries.GetExamFormByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.FormNotFound("exam form not found")
		}
		return nil, errors.Database("failed to get exam form by ID", err)
	}

	return r.convertToModel(&result), nil
}

// GetByRollNumber retrieves an exam form by roll number
func (r *examFormRepository) GetByRollNumber(ctx context.Context, rollNumber string) (*models.ExamForm, error) {
	result, err := r.queries.GetExamFormByRoll(ctx, rollNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.FormNotFound("exam form not found")
		}
		return nil, errors.Database("failed to get exam form by roll number", err)
	}

	return r.convertToModel(&result), nil
}

// GetAll retrieves all exam forms with pagination
func (r *examFormRepository) GetAll(ctx context.Context, limit, offset int) ([]*models.ExamForm, error) {
	results, err := r.queries.GetAllExamForms(ctx)
	if err != nil {
		return nil, errors.Database("failed to get all exam forms", err)
	}

	// Apply pagination manually since SQLC doesn't support it in this query
	start := offset
	end := offset + limit
	if start > len(results) {
		return []*models.ExamForm{}, nil
	}
	if end > len(results) {
		end = len(results)
	}

	paginatedResults := results[start:end]
	examForms := make([]*models.ExamForm, len(paginatedResults))
	for i, result := range paginatedResults {
		examForms[i] = r.convertToModel(&result)
	}

	return examForms, nil
}

// GetByCategory retrieves exam forms by category
func (r *examFormRepository) GetByCategory(ctx context.Context, category string) ([]*models.ExamForm, error) {
	results, err := r.queries.GetExamFormsByCategory(ctx, category)
	if err != nil {
		return nil, errors.Database("failed to get exam forms by category", err)
	}

	examForms := make([]*models.ExamForm, len(results))
	for i, result := range results {
		examForms[i] = r.convertToModel(&result)
	}

	return examForms, nil
}

// GetByExamType retrieves exam forms by exam type (regular/ex)
func (r *examFormRepository) GetByExamType(ctx context.Context, isRegular bool) ([]*models.ExamForm, error) {
	results, err := r.queries.GetExamFormsByType(ctx, isRegular)
	if err != nil {
		return nil, errors.Database("failed to get exam forms by type", err)
	}

	examForms := make([]*models.ExamForm, len(results))
	for i, result := range results {
		examForms[i] = r.convertToModel(&result)
	}

	return examForms, nil
}

// Update updates an exam form
func (r *examFormRepository) Update(ctx context.Context, examForm *models.ExamForm) (*models.ExamForm, error) {
	params := data.UpdateExamFormByRollParams{
		RollNumber: examForm.RollNumber,
		Category:   examForm.Category,
		IsRegular:  examForm.IsRegular,
		PaperI:     examForm.PaperI,
		PaperIi:    examForm.PaperII,
		PaperIii:   examForm.PaperIII,
		PaperIv:    examForm.PaperIV,
		Fee:        examForm.Fee,
	}

	err := r.queries.UpdateExamFormByRoll(ctx, params)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, errors.FormNotFound("exam form not found")
		}
		return nil, r.handleDatabaseError(err, "update exam form")
	}

	// Return the updated exam form
	return r.GetByRollNumber(ctx, examForm.RollNumber)
}

// Delete deletes an exam form by ID
func (r *examFormRepository) Delete(ctx context.Context, id int32) error {
	err := r.queries.DeleteExamFormByID(ctx, id)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.FormNotFound("exam form not found")
		}
		return errors.Database("failed to delete exam form", err)
	}

	return nil
}

// DeleteByRollNumber deletes an exam form by roll number
func (r *examFormRepository) DeleteByRollNumber(ctx context.Context, rollNumber string) error {
	err := r.queries.DeleteExamFormByRoll(ctx, rollNumber)
	if err != nil {
		if err == sql.ErrNoRows {
			return errors.FormNotFound("exam form not found")
		}
		return errors.Database("failed to delete exam form", err)
	}

	return nil
}

// Search searches for exam forms based on criteria
func (r *examFormRepository) Search(ctx context.Context, criteria *models.ExamFormSearchRequest) ([]*models.ExamForm, error) {
	var results []data.ExamFormDegreeIii
	var err error

	// If roll number is specified, search by roll number
	if criteria.RollNumber != "" {
		result, err := r.queries.GetExamFormByRoll(ctx, criteria.RollNumber)
		if err != nil {
			if err == sql.ErrNoRows {
				return []*models.ExamForm{}, nil
			}
			return nil, errors.Database("failed to search exam forms", err)
		}
		results = []data.ExamFormDegreeIii{result}
	} else if criteria.Category != "" {
		results, err = r.queries.GetExamFormsByCategory(ctx, criteria.Category)
		if err != nil {
			return nil, errors.Database("failed to search exam forms by category", err)
		}
	} else if criteria.IsRegular != nil {
		results, err = r.queries.GetExamFormsByType(ctx, *criteria.IsRegular)
		if err != nil {
			return nil, errors.Database("failed to search exam forms by type", err)
		}
	} else {
		// No specific criteria, return all
		results, err = r.queries.GetAllExamForms(ctx)
		if err != nil {
			return nil, errors.Database("failed to get all exam forms", err)
		}
	}

	examForms := make([]*models.ExamForm, len(results))
	for i, result := range results {
		examForms[i] = r.convertToModel(&result)
	}

	return examForms, nil
}

// Count returns the total number of exam forms
func (r *examFormRepository) Count(ctx context.Context) (int64, error) {
	count, err := r.queries.CountExamForms(ctx)
	if err != nil {
		return 0, errors.Database("failed to count exam forms", err)
	}

	return count, nil
}

// CountByCategory returns the number of exam forms by category
func (r *examFormRepository) CountByCategory(ctx context.Context, category string) (int64, error) {
	count, err := r.queries.CountExamFormsByCategory(ctx, category)
	if err != nil {
		return 0, errors.Database("failed to count exam forms by category", err)
	}

	return count, nil
}

// convertToModel converts data.ExamFormDegreeIii to models.ExamForm
func (r *examFormRepository) convertToModel(examForm *data.ExamFormDegreeIii) *models.ExamForm {
	return &models.ExamForm{
		ID:         examForm.ID,
		RollNumber: examForm.RollNumber,
		Category:   examForm.Category,
		IsRegular:  examForm.IsRegular,
		PaperI:     examForm.PaperI,
		PaperII:    examForm.PaperIi,
		PaperIII:   examForm.PaperIii,
		PaperIV:    examForm.PaperIv,
		Fee:        examForm.Fee,
	}
}

// handleDatabaseError handles database-specific errors
func (r *examFormRepository) handleDatabaseError(err error, operation string) error {
	if pgErr, ok := err.(*pgconn.PgError); ok {
		switch pgErr.Code {
		case "23505": // Unique violation
			if strings.Contains(pgErr.Detail, "roll_number") {
				return errors.FormExists("exam form with this roll number already exists")
			}
			return errors.FormExists("exam form already exists")
		case "23503": // Foreign key violation
			return errors.BadRequest("invalid reference data")
		case "23514": // Check constraint violation
			return errors.BadRequest("invalid data format")
		default:
			return errors.Database(fmt.Sprintf("database error during %s", operation), err)
		}
	}

	return errors.Database(fmt.Sprintf("failed to %s", operation), err)
}
