{{define "content"}}
<div class="max-w-2xl mx-auto p-6 bg-white shadow-md rounded-md">
    <h2 class="text-2xl font-bold mb-4 text-center">Search Student Record</h2>

    <form id="searchForm" method="POST" action="/update" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Search Type -->
            <div>
                <label for="search_type" class="block text-gray-700"
                    >Search By:</label
                >
                <select
                    name="search_type"
                    id="search_type"
                    required
                    class="w-full p-2 border border-gray-300 rounded-md"
                >
                    <option value="">-- Select --</option>
                    <option value="exam_roll">Exam Roll Number</option>
                    <option value="abc_id">ABC ID</option>
                    <option value="aadhaar">Aadhaar Number</option>
                </select>
            </div>

            <!-- Search Value -->
            <div>
                <label for="search_value" class="block text-gray-700"
                    >Enter Value:</label
                >
                <input
                    type="text"
                    name="search_value"
                    id="search_value"
                    required
                    class="w-full p-2 border border-gray-300 rounded-md"
                />
            </div>
        </div>

        <!-- Submit Button -->
        <button
            type="submit"
            class="w-full bg-blue-600 text-white py-2 rounded-md hover:bg-blue-700 transition"
        >
            Search
        </button>
    </form>
</div>

<!-- jQuery for Form Validation -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        // Form Validation
        $("#searchForm").on("submit", function (e) {
            let valid = true;

            if ($("#search_type").val().trim() === "") {
                $("#search_type").addClass("border-red-500");
                valid = false;
            } else {
                $("#search_type").removeClass("border-red-500");
            }

            if ($("#search_value").val().trim() === "") {
                $("#search_value").addClass("border-red-500");
                valid = false;
            } else {
                $("#search_value").removeClass("border-red-500");
            }

            if (!valid) {
                alert("Please select a search type and enter a value.");
                e.preventDefault();
            }
        });
    });
</script>
{{end}}
