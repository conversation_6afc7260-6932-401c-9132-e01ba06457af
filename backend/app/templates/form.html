{{ define "content" }} {{ if .Success }}
<div
    class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"
    role="alert"
>
    <span class="block sm:inline">{{ .Success }}</span>
</div>
{{ else if .Error }}
<div
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
    role="alert"
>
    <span class="block sm:inline">{{ .Error }}</span>
</div>
{{ else }}
<h1 class="text-3xl font-bold mb-4">फॉर्म भरें</h1>

<!-- Success/Error Messages -->
<div
    class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4 hidden"
    id="successMessage"
>
    <h4 class="font-bold">Success!</h4>
    <p>All form entries are valid</p>
</div>

<div
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4 hidden"
    id="errorMessage"
>
    <h4 class="font-bold">मान्यता त्रुटि!</h4>
    <p>
        कृपया फ़ॉर्म को ध्यान से देखें और यह सुनिश्चित करें कि सभी आवश्यक फ़ील्ड
        सही तरीके से भरी गई हैं। यदि कोई जानकारी गलत या अधूरी है, तो उसे ठीक
        करें और फिर से सबमिट करें।
    </p>
</div>

<noscript>
    <div
        class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
    >
        <p class="font-bold">JavaScript Required</p>
        <p>This form requires JavaScript to be enabled in your browser.</p>
    </div>
    <style>
        #submitButton {
            display: none;
        }
    </style>
</noscript>

<form id="myForm" action="/form" method="POST" class="space-y-6">
    <div>
        <label for="name" class="block text-lg font-medium"
            >नाम (Capital Letters)</label
        >
        <input
            type="text"
            id="name"
            name="name"
            class="w-full p-2 border rounded"
            pattern="[A-Z\s]+"
            oninput="this.value = this.value.toUpperCase()"
            required
        />
        <p class="text-red-500 text-sm" id="nameError"></p>
    </div>

    <div>
        <label for="email" class="block text-lg font-medium">ईमेल आईडी</label>
        <input
            type="email"
            id="email"
            name="email"
            class="w-full p-2 border rounded"
            required
        />
        <p class="text-red-500 text-sm" id="emailError"></p>
    </div>

    <div>
        <label for="mobile_number" class="block text-lg font-medium"
            >मोबाइल नंबर</label
        >
        <input
            type="text"
            id="mobile_number"
            name="mobile_number"
            class="w-full p-2 border rounded"
            required
        />
        <p class="text-red-500 text-sm" id="mobileError"></p>
    </div>

    <div>
        <label class="block text-lg font-medium">लिंग</label>
        <div class="flex space-x-4">
            <label>
                <input type="radio" name="gender" value="M" required /> Male
            </label>
            <label>
                <input type="radio" name="gender" value="F" /> Female
            </label>
        </div>
        <p class="text-red-500 text-sm" id="genderError"></p>
    </div>

    <div>
        <label class="block text-lg font-medium">श्रेणी</label>
        <select name="category" class="w-full p-2 border rounded" required>
            <option value="">Select Category</option>
            <option value="UR">UR</option>
            <option value="EWS">EWS</option>
            <option value="BC">BC</option>
            <option value="EBC">EBC</option>
            <option value="SC">SC</option>
            <option value="ST">ST</option>
        </select>
        <p class="text-red-500 text-sm" id="categoryError"></p>
    </div>

    <div>
        <label for="date_of_birth" class="block text-lg font-medium"
            >जन्मतिथि</label
        >
        <input
            type="date"
            id="date_of_birth"
            name="date_of_birth"
            class="w-full p-2 border rounded"
            required
        />
        <p class="text-red-500 text-sm" id="dobError"></p>
    </div>

    <div>
        <label for="college" class="block text-lg font-medium"
            >महाविद्यालय</label
        >
        <input
            type="text"
            id="college"
            name="college"
            value="SSV College"
            class="w-full p-2 border rounded bg-gray-100"
            readonly
            required
        />
    </div>

    <div>
        <label for="session" class="block text-lg font-medium">सत्र</label>
        <select
            id="session"
            name="session"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select Session</option>
            <option value="2022-25">2022-25</option>
            <option value="2023-27">2023-27</option>
            <option value="2024-28">2024-28</option>
        </select>
        <p class="text-red-500 text-sm" id="sessionError"></p>
    </div>

    <div>
        <label for="subject" class="block text-lg font-medium"
            >विषय (Hounours or MJC)</label
        >
        <select
            id="subject"
            name="subject"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select Subject</option>
            <option value="Physics">Physics</option>
            <option value="Chemistry">Chemistry</option>
            <option value="Botany">Botany</option>
            <option value="Zoology">Zoology</option>
            <option value="Mathematics">Mathematics</option>
            <option value="Hindi">Hindi</option>
            <option value="English">English</option>
            <option value="Urdu">Urdu</option>
            <option value="Sanskrit">Sanskrit</option>
            <option value="History">History</option>
            <option value="Political Science">Political Science</option>
            <option value="Economics">Economics</option>
            <option value="AIHC">AIHC</option>
            <option value="IRPM">IRPM</option>
            <option value="Sociology">Sociology</option>
            <option value="Philosophy">Philosophy</option>
            <option value="Psychology">Psychology</option>
        </select>
        <p class="text-red-500 text-sm" id="subjectError"></p>
    </div>

    <div>
        <label for="class_roll_number" class="block text-lg font-medium">
            कक्षा रोल नंबर
            <span class="text-gray-600 text-sm">
                (यह वह रोल नंबर है जिसका उपयोग कक्षा में उपस्थिति दर्ज करने के
                लिए किया जाता है।)
            </span>
        </label>
        <input
            type="text"
            id="class_roll_number"
            name="class_roll_number"
            class="w-full p-2 border rounded"
            pattern="\d{1,4}"
            maxlength="4"
            required
        />
        <p class="text-red-500 text-sm" id="classRollError"></p>
    </div>

    <div>
        <label for="university_roll_number" class="block text-lg font-medium">
            विश्वविद्यालय/परीक्षा रोल नंबर
            <span class="text-gray-600 text-sm">
                (यह वह रोल नंबर है जिसका उपयोग छात्र परीक्षा देने के लिए करते
                हैं। कृपया सही रोल नंबर दर्ज करें।)
            </span>
        </label>
        <input
            type="text"
            id="university_roll_number"
            name="university_roll_number"
            class="w-full p-2 border rounded"
            pattern="\d{5,10}"
            minlength="5"
            maxlength="10"
            required
        />
        <p class="text-red-500 text-sm" id="universityRollError"></p>
    </div>

    <div>
        <label for="registration_number" class="block text-lg font-medium">
            पंजीकरण संख्या
            <span class="text-gray-600 text-sm">
                (पंजीकरण संख्या में वह वर्ष भी शामिल होता है जिसमें छात्र ने
                विश्वविद्यालय में प्रवेश लिया था। उदाहरण: 1480500012/2014 या
                TM148012/2014)
            </span>
        </label>
        <input
            type="text"
            id="registration_number"
            name="registration_number"
            class="w-full p-2 border rounded"
            pattern="^(TM)?[0-9]+\/[0-9]{4}$"
            required
        />
        <p class="text-red-500 text-sm" id="registrationNumberError"></p>
    </div>

    <div>
        <label for="father_name" class="block text-lg font-medium"
            >पिता का नाम (Capital Letters only)</label
        >
        <input
            type="text"
            id="father_name"
            name="father_name"
            class="w-full p-2 border rounded"
            pattern="[A-Z\s]+"
            oninput="this.value = this.value.toUpperCase()"
            required
        />
        <p class="text-red-500 text-sm" id="fatherNameError"></p>
    </div>

    <div>
        <label for="mother_name" class="block text-lg font-medium"
            >माता का नाम (Capital Letters only)</label
        >
        <input
            type="text"
            id="mother_name"
            name="mother_name"
            class="w-full p-2 border rounded"
            pattern="[A-Z\s]+"
            oninput="this.value = this.value.toUpperCase()"
            required
        />
        <p class="text-red-500 text-sm" id="motherNameError"></p>
    </div>

    <div>
        <label for="state" class="block text-lg font-medium">राज्य</label>
        <select
            id="state"
            name="state"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select State</option>
            <option value="BIHAR">BIHAR</option>
            <option value="OTHER">OTHER</option>
        </select>
        <p class="text-red-500 text-sm" id="stateError"></p>
    </div>

    <div>
        <label for="pincode" class="block text-lg font-medium">पिनकोड</label>
        <input
            type="text"
            id="pincode"
            name="pincode"
            class="w-full p-2 border rounded"
            required
        />
        <p class="text-red-500 text-sm" id="pincodeError"></p>
    </div>

    <div>
        <label for="aadhar_number" class="block text-lg font-medium"
            >आधार नंबर (12 digits)</label
        >
        <input
            type="text"
            id="aadhar_number"
            name="aadhar_number"
            class="w-full p-2 border rounded"
            pattern="\d{12}"
            maxlength="12"
            required
        />
        <p class="text-red-500 text-sm" id="aadharError"></p>
    </div>

    <div>
        <label for="abc_id" class="block text-lg font-medium"
            >ABC ID (12 digits)</label
        >
        <input
            type="text"
            id="abc_id"
            name="abc_id"
            class="w-full p-2 border rounded"
            pattern="\d{12}"
            maxlength="12"
            required
        />
        <p class="text-red-500 text-sm" id="abcIdError"></p>
    </div>

    <div>
        <label for="aadhar_mobile" class="block text-lg font-medium"
            >आधार लिंक मोबाइल नंबर</label
        >
        <input
            type="text"
            id="aadhar_mobile"
            name="aadhar_mobile"
            class="w-full p-2 border rounded"
            pattern="\d{10}"
            maxlength="10"
            required
        />
        <p class="text-red-500 text-sm" id="aadharMobileError"></p>
    </div>

    <button
        type="submit"
        id="submitButton"
        class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
    >
        सबमिट करें
    </button>
</form>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.5/dist/jquery.validate.min.js"></script>
<script>
    // Check if jQuery and validation plugin loaded
    if (
        typeof jQuery != "undefined" &&
        typeof jQuery.validator != "undefined"
    ) {
        $(document).ready(function () {
            $.validator.addMethod(
                "uppercase",
                function (value, element) {
                    return (
                        this.optional(element) || value === value.toUpperCase()
                    );
                },
                "Please enter text in uppercase only",
            );

            $.validator.addMethod(
                "lettersonly",
                function (value, element) {
                    return this.optional(element) || /^[A-Z\s]+$/.test(value);
                },
                "Please enter letters only",
            );

            $.validator.addMethod(
                "regNumber",
                function (value, element) {
                    return (
                        this.optional(element) ||
                        /^(TM)?[0-9]+\/[0-9]{4}$/.test(value)
                    );
                },
                "Please enter valid registration number format (e.g. 1480500012/2014 or TM148012/2014)",
            );

            $("#myForm").validate({
                rules: {
                    name: {
                        required: true,
                        uppercase: true,
                        lettersonly: true,
                    },
                    email: {
                        required: true,
                        email: true,
                    },
                    mobile_number: {
                        required: true,
                        digits: true,
                        minlength: 10,
                        maxlength: 10,
                    },
                    gender: {
                        required: true,
                    },
                    category: {
                        required: true,
                    },
                    date_of_birth: {
                        required: true,
                        date: true,
                    },
                    session: {
                        required: true,
                    },
                    subject: {
                        required: true,
                    },
                    class_roll_number: {
                        required: true,
                        digits: true,
                        maxlength: 4,
                    },
                    university_roll_number: {
                        required: true,
                        digits: true,
                        minlength: 5,
                        maxlength: 10,
                    },
                    registration_number: {
                        required: true,
                        regNumber: true,
                    },
                    father_name: {
                        required: true,
                        uppercase: true,
                        lettersonly: true,
                    },
                    mother_name: {
                        required: true,
                        uppercase: true,
                        lettersonly: true,
                    },
                    state: {
                        required: true,
                    },
                    pincode: {
                        required: true,
                        digits: true,
                        minlength: 6,
                        maxlength: 6,
                    },
                    aadhar_number: {
                        required: true,
                        digits: true,
                        minlength: 12,
                        maxlength: 12,
                    },
                    abc_id: {
                        required: true,
                        digits: true,
                        minlength: 12,
                        maxlength: 12,
                    },
                    aadhar_mobile: {
                        required: true,
                        digits: true,
                        minlength: 10,
                        maxlength: 10,
                    },
                },
                messages: {
                    name: {
                        required: "Please enter your name",
                        uppercase: "Name must be in uppercase letters",
                        lettersonly: "Name can only contain letters",
                    },
                    email: {
                        required: "Please enter your email",
                        email: "Please enter a valid email address",
                    },
                    mobile_number: {
                        required: "Please enter mobile number",
                        digits: "Please enter only digits",
                        minlength: "Mobile number must be 10 digits",
                        maxlength: "Mobile number must be 10 digits",
                    },
                    gender: {
                        required: "Please select gender",
                    },
                    category: {
                        required: "Please select category",
                    },
                    date_of_birth: {
                        required: "Please enter date of birth",
                        date: "Please enter a valid date",
                    },
                    session: {
                        required: "Please select session",
                    },
                    subject: {
                        required: "Please select subject",
                    },
                    class_roll_number: {
                        required: "Please enter class roll number",
                        digits: "Please enter only digits",
                        maxlength: "Class roll of college/department.",
                    },
                    university_roll_number: {
                        required: "Please enter university roll number",
                        digits: "Please enter only digits",
                        minlength: "University roll from admit card",
                        maxlength: "University roll from admit card",
                    },
                    registration_number: {
                        required: "Please enter registration number",
                        regNumber:
                            "Please enter valid registration number format (e.g. 1480500012/2014 or TM148012/2014)",
                    },
                    father_name: {
                        required: "Please enter father's name",
                        uppercase: "Name must be in uppercase letters",
                        lettersonly: "Name can only contain letters",
                    },
                    mother_name: {
                        required: "Please enter mother's name",
                        uppercase: "Name must be in uppercase letters",
                        lettersonly: "Name can only contain letters",
                    },
                    state: {
                        required: "Please select state",
                    },
                    pincode: {
                        required: "Please enter pincode",
                        digits: "Please enter only digits",
                        minlength: "Pincode must be 6 digits",
                        maxlength: "Pincode must be 6 digits",
                    },
                    aadhar_number: {
                        required: "Please enter Aadhar number",
                        digits: "Please enter only digits",
                        minlength: "Aadhar must be 12 digits",
                        maxlength: "Aadhar must be 12 digits",
                    },
                    abc_id: {
                        required: "Please enter ABC ID",
                        digits: "Please enter only digits",
                        minlength: "ABC ID must be 12 digits",
                        maxlength: "ABC ID must be 12 digits",
                    },
                    aadhar_mobile: {
                        required: "Please enter Aadhar linked mobile number",
                        digits: "Please enter only digits",
                        minlength: "Mobile number must be 10 digits",
                        maxlength: "Mobile number must be 10 digits",
                    },
                },
                errorClass: "text-red-500 text-sm",
                errorElement: "p",
                highlight: function (element) {
                    $(element).addClass("border-red-500");
                },
                unhighlight: function (element) {
                    $(element).removeClass("border-red-500");
                },
                submitHandler: function (form) {
                    $("#successMessage").removeClass("hidden");
                    setTimeout(function () {
                        $("#successMessage").addClass("hidden");
                        form.submit();
                    }, 2000);
                },
                invalidHandler: function () {
                    $("#errorMessage").removeClass("hidden");
                    setTimeout(function () {
                        $("#errorMessage").addClass("hidden");
                    }, 5000);
                },
            });
        });
    } else {
        // Hide submit button if jQuery or validation plugin not loaded
        document.getElementById("submitButton").style.display = "none";
    }
</script>
{{ end }} {{ end }}
