{{define "content"}}
<div class="max-w-4xl mx-auto p-6 bg-white shadow-md rounded-md">
    <h2 class="text-2xl font-bold mb-4 text-center">Update Student Record</h2>
    
    {{if .Success}}
    <div class="bg-green-100 text-green-700 px-4 py-2 rounded-md mb-4 text-center">
        {{.Success}}
    </div>
    {{end}}
    
    {{if .Error}}
    <div class="bg-red-100 text-red-700 px-4 py-2 rounded-md mb-4 text-center">
        {{.Error}}
    </div>
    {{end}}

    <!-- Search Form -->
    <form method="POST" action="/update" class="bg-gray-100 p-4 rounded-md mb-4">
        <h3 class="text-lg font-semibold mb-2">Search for Student</h3>
        <div class="flex gap-2">
            <select name="search_type" required class="p-2 border border-gray-300 rounded-md">
                <option value="">-- Select Search Type --</option>
                <option value="abc_id">ABC ID</option>
                <option value="aadhaar">Aadhaar Number</option>
            </select>
            <input type="text" name="search_value" required placeholder="Enter value" class="p-2 border border-gray-300 rounded-md flex-1">
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                Search
            </button>
        </div>
    </form>

    <!-- Student Form -->
    {{if .Student}}
    <form method="POST" action="/update" class="space-y-4">
        <input type="hidden" name="id" value="{{.Student.ID}}">
        <input type="hidden" name="update" value="true">
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label class="block text-gray-700">Name:</label>
                <input type="text" name="name" value="{{.Student.Name}}" required class="w-full p-2 border border-gray-300 rounded-md">
            </div>
            <div>
                <label class="block text-gray-700">Email:</label>
                <input type="email" name="email" value="{{.Student.Email}}" required class="w-full p-2 border border-gray-300 rounded-md">
            </div>
        </div>
        
        <button type="submit" class="w-full bg-green-600 text-white py-2 rounded-md hover:bg-green-700">
            Update Student
        </button>
    </form>
    {{else}}
    <div class="text-center text-gray-600 mt-8">
        <p>Please search for a student using the form above to update their record.</p>
    </div>
    {{end}}
</div>
{{end}}
