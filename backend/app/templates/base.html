<!doctype html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{{.Title}}</title>
        <link rel="stylesheet" href="/assets/css/output.css" />
        <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
        <script
            src="https://cdn.jsdelivr.net/npm/alpinejs@3.x/dist/cdn.min.js"
            defer
        ></script>
        <script
            src="https://unpkg.com/htmx.org@2.0.3"
            integrity="sha384-0895/pl2MU10Hqc6jd4RvrthNlDiE9U1tWmX7WRESftEDRosgxNsQG/Ze9YMRzHq"
            crossorigin="anonymous"
        ></script>
        <script
            src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"
            defer
        ></script>
        <!-- Added MathJax -->
        <script>
            MathJax = {
                tex: {
                    packages: { "[+]": ["ams", "base"] }, // Added amsmath support only
                    inlineMath: [
                        ["$", "$"],
                        ["\\(", "\\)"],
                    ],
                    displayMath: [
                        ["$$", "$$"],
                        ["\\[", "\\]"],
                    ],
                    processEnvironments: true, // Process environments like align
                    processRefs: true, // Process references
                },
                svg: {
                    fontCache: "global",
                },
            };
        </script>
        <style>
            @media print {
                @page {
                    size: A4;
                    margin: 0;
                }
            }
        </style>
    </head>

    <body class="bg-gray-100">
        <!-- Modular Navigation -->
        {{ template "navbar" . }}

        <!-- Page Content -->
        <div class="container mx-auto px-4 py-8">
            {{ block "content" . }}{{ end }}
        </div>
        <script src="/static/dist/js/bundle.js"></script>
    </body>
</html>
