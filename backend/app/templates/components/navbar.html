{{ define "navbar" }}
<!-- Modular Navigation Bar -->
<nav x-data="{ open: false }" class="bg-gray-800 shadow-lg print:hidden">
    <div class="max-w-7xl mx-auto px-4">
        <div class="flex justify-between h-16">
            <!-- <PERSON><PERSON> and Brand -->
            <div class="flex items-center">
                <a href="/" class="flex-shrink-0 flex items-center">
                    <i class="fas fa-university text-white text-xl mr-2"></i>
                    <span class="font-bold text-xl text-white">UMIS</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Common Links -->
                <a href="/" class="nav-link {{ if eq .CurrentPage "home" }}active{{ end }}">
                    <i class="fas fa-home mr-1"></i>होम
                </a>

                <!-- Student Links (always visible) -->
                {{ if not .IsAuthenticated }}
                <a href="/student/register" class="nav-link {{ if eq .CurrentPage "register" }}active{{ end }}">
                    <i class="fas fa-user-plus mr-1"></i>पंजीकरण
                </a>
                <a href="/student/enrollment" class="nav-link {{ if eq .CurrentPage "enrollment" }}active{{ end }}">
                    <i class="fas fa-graduation-cap mr-1"></i>नामांकन
                </a>
                <a href="/student/status" class="nav-link {{ if eq .CurrentPage "status" }}active{{ end }}">
                    <i class="fas fa-search mr-1"></i>स्थिति
                </a>
                {{ end }}

                <!-- Authenticated User Links -->
                {{ if .IsAuthenticated }}
                    {{ if eq .UserRole "admin" }}
                    <!-- Super Admin Links -->
                    <a href="/admin/dashboard" class="nav-link {{ if eq .CurrentPage "admin-dashboard" }}active{{ end }}">
                        <i class="fas fa-tachometer-alt mr-1"></i>डैशबोर्ड
                    </a>
                    <a href="/admin/departments" class="nav-link {{ if eq .CurrentPage "admin-departments" }}active{{ end }}">
                        <i class="fas fa-building mr-1"></i>विभाग
                    </a>
                    <a href="/admin/users" class="nav-link {{ if eq .CurrentPage "admin-users" }}active{{ end }}">
                        <i class="fas fa-users-cog mr-1"></i>उपयोगकर्ता
                    </a>
                    {{ else if eq .UserRole "department_admin" }}
                    <!-- Department Admin Links -->
                    <a href="/dept/dashboard" class="nav-link {{ if eq .CurrentPage "dept-dashboard" }}active{{ end }}">
                        <i class="fas fa-chart-line mr-1"></i>विभाग डैशबोर्ड
                    </a>
                    <a href="/dept/enrollments" class="nav-link {{ if eq .CurrentPage "dept-enrollments" }}active{{ end }}">
                        <i class="fas fa-clipboard-list mr-1"></i>नामांकन अनुमोदन
                    </a>
                    <a href="/dept/students" class="nav-link {{ if eq .CurrentPage "dept-students" }}active{{ end }}">
                        <i class="fas fa-user-graduate mr-1"></i>छात्र सूची
                    </a>
                    {{ else }}
                    <!-- Regular Student Links -->
                    <a href="/student/profile" class="nav-link {{ if eq .CurrentPage "student-profile" }}active{{ end }}">
                        <i class="fas fa-user mr-1"></i>प्रोफाइल
                    </a>
                    <a href="/student/enrollments" class="nav-link {{ if eq .CurrentPage "student-enrollments" }}active{{ end }}">
                        <i class="fas fa-list mr-1"></i>मेरे नामांकन
                    </a>
                    {{ end }}

                    <!-- User Info and Logout -->
                    <div class="flex items-center space-x-2 ml-4 pl-4 border-l border-gray-600">
                        <span class="text-gray-300 text-sm">
                            <i class="fas fa-user-circle mr-1"></i>
                            {{ .UserName }}
                            {{ if .UserRole }}
                                <span class="text-xs text-gray-400">({{ .UserRole }})</span>
                            {{ end }}
                        </span>
                        <a href="/logout" class="nav-link text-red-400 hover:text-red-300">
                            <i class="fas fa-sign-out-alt mr-1"></i>लॉगआउट
                        </a>
                    </div>
                {{ else }}
                <!-- Login Link for Non-authenticated Users -->
                <a href="/admin/login" class="nav-link {{ if eq .CurrentPage "login" }}active{{ end }}">
                    <i class="fas fa-sign-in-alt mr-1"></i>लॉगिन
                </a>
                {{ end }}
            </div>

            <!-- Mobile menu button -->
            <div class="md:hidden flex items-center">
                <button @click="open = !open" class="text-gray-300 hover:text-white focus:outline-none">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile menu -->
    <div x-show="open" x-transition class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 bg-gray-700">
            <!-- Common Mobile Links -->
            <a href="/" class="mobile-nav-link {{ if eq .CurrentPage "home" }}active{{ end }}">
                <i class="fas fa-home mr-2"></i>होम
            </a>

            <!-- Student Mobile Links (always visible when not authenticated) -->
            {{ if not .IsAuthenticated }}
            <a href="/student/register" class="mobile-nav-link {{ if eq .CurrentPage "register" }}active{{ end }}">
                <i class="fas fa-user-plus mr-2"></i>पंजीकरण
            </a>
            <a href="/student/enrollment" class="mobile-nav-link {{ if eq .CurrentPage "enrollment" }}active{{ end }}">
                <i class="fas fa-graduation-cap mr-2"></i>नामांकन
            </a>
            <a href="/student/status" class="mobile-nav-link {{ if eq .CurrentPage "status" }}active{{ end }}">
                <i class="fas fa-search mr-2"></i>स्थिति
            </a>
            {{ end }}

            <!-- Authenticated Mobile Links -->
            {{ if .IsAuthenticated }}
                {{ if eq .UserRole "admin" }}
                <!-- Super Admin Mobile Links -->
                <a href="/admin/dashboard" class="mobile-nav-link {{ if eq .CurrentPage "admin-dashboard" }}active{{ end }}">
                    <i class="fas fa-tachometer-alt mr-2"></i>डैशबोर्ड
                </a>
                <a href="/admin/departments" class="mobile-nav-link {{ if eq .CurrentPage "admin-departments" }}active{{ end }}">
                    <i class="fas fa-building mr-2"></i>विभाग
                </a>
                <a href="/admin/users" class="mobile-nav-link {{ if eq .CurrentPage "admin-users" }}active{{ end }}">
                    <i class="fas fa-users-cog mr-2"></i>उपयोगकर्ता
                </a>
                {{ else if eq .UserRole "department_admin" }}
                <!-- Department Admin Mobile Links -->
                <a href="/dept/dashboard" class="mobile-nav-link {{ if eq .CurrentPage "dept-dashboard" }}active{{ end }}">
                    <i class="fas fa-chart-line mr-2"></i>विभाग डैशबोर्ड
                </a>
                <a href="/dept/enrollments" class="mobile-nav-link {{ if eq .CurrentPage "dept-enrollments" }}active{{ end }}">
                    <i class="fas fa-clipboard-list mr-2"></i>नामांकन अनुमोदन
                </a>
                <a href="/dept/students" class="mobile-nav-link {{ if eq .CurrentPage "dept-students" }}active{{ end }}">
                    <i class="fas fa-user-graduate mr-2"></i>छात्र सूची
                </a>
                {{ else }}
                <!-- Regular Student Mobile Links -->
                <a href="/student/profile" class="mobile-nav-link {{ if eq .CurrentPage "student-profile" }}active{{ end }}">
                    <i class="fas fa-user mr-2"></i>प्रोफाइल
                </a>
                <a href="/student/enrollments" class="mobile-nav-link {{ if eq .CurrentPage "student-enrollments" }}active{{ end }}">
                    <i class="fas fa-list mr-2"></i>मेरे नामांकन
                </a>
                {{ end }}

                <!-- User Info Mobile -->
                <div class="px-3 py-2 text-gray-300 text-sm border-t border-gray-600 mt-2">
                    <i class="fas fa-user-circle mr-1"></i>
                    {{ .UserName }}
                    {{ if .UserRole }}
                        <span class="text-xs text-gray-400 block">({{ .UserRole }})</span>
                    {{ end }}
                </div>
                <a href="/logout" class="mobile-nav-link text-red-400">
                    <i class="fas fa-sign-out-alt mr-2"></i>लॉगआउट
                </a>
            {{ else }}
            <!-- Login Mobile Link -->
            <a href="/admin/login" class="mobile-nav-link {{ if eq .CurrentPage "login" }}active{{ end }}">
                <i class="fas fa-sign-in-alt mr-2"></i>लॉगिन
            </a>
            {{ end }}
        </div>
    </div>
</nav>

<!-- CSS Styles for Navigation -->
<style>
.nav-link {
    @apply text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200;
}

.nav-link.active {
    @apply bg-blue-600 text-white;
}

.mobile-nav-link {
    @apply text-gray-300 hover:text-white block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200;
}

.mobile-nav-link.active {
    @apply bg-blue-600 text-white;
}
</style>
{{ end }}
