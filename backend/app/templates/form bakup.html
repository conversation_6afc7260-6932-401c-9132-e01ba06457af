{{ define "content" }} {{ if .Success }}
<div
    class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4"
    role="alert"
>
    <span class="block sm:inline">{{ .Success }}</span>
</div>
{{ end }} {{ if .Error }}
<div
    class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
    role="alert"
>
    <span class="block sm:inline">{{ .Error }}</span>
</div>
{{ end }}
<h1 class="text-3xl font-bold mb-4">फॉर्म भरें</h1>
<form id="myForm" action="/form" method="POST" class="space-y-6">
    <!-- Name -->
    <div>
        <label for="name" class="block text-lg font-medium"
            >नाम (Capital Letters)</label
        >
        <input
            type="text"
            id="name"
            name="name"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="nameError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Email -->
    <div>
        <label for="email" class="block text-lg font-medium">ईमेल आईडी</label>
        <input
            type="email"
            id="email"
            name="email"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="emailError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Date of Birth -->
    <div>
        <label for="date_of_birth" class="block text-lg font-medium"
            >जन्मतिथि</label
        >
        <input
            type="date"
            id="date_of_birth"
            name="date_of_birth"
            class="w-full p-2 border rounded"
            required
        />
        <p id="dobError" class="text-red-500 text-sm" style="display: none"></p>
    </div>

    <!-- Gender -->
    <div>
        <label class="block text-lg font-medium">लिंग</label>
        <div class="flex space-x-4">
            <label>
                <input type="radio" name="gender" value="M" required /> Male
            </label>
            <label>
                <input type="radio" name="gender" value="F" required /> Female
            </label>
        </div>
        <p
            id="genderError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Category -->
    <div>
        <label class="block text-lg font-medium">श्रेणी</label>
        <select name="category" class="w-full p-2 border rounded" required>
            <option value="">Select Category</option>
            <option value="UR">UR</option>
            <option value="EWS">EWS</option>
            <option value="BC">BC</option>
            <option value="EBC">EBC</option>
            <option value="SC">SC</option>
            <option value="ST">ST</option>
        </select>
        <p
            id="categoryError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Mobile Number -->
    <div>
        <label for="mobile_number" class="block text-lg font-medium"
            >मोबाइल नंबर</label
        >
        <input
            type="text"
            id="mobile_number"
            name="mobile_number"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="mobileError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- College -->
    <div>
        <label for="college" class="block text-lg font-medium"
            >महाविद्यालय</label
        >
        <input
            type="text"
            id="college"
            name="college"
            value="SSV College"
            class="w-full p-2 border rounded bg-gray-100"
            readonly
            required
        />
    </div>

    <!-- Session -->
    <div>
        <label for="session" class="block text-lg font-medium">सत्र</label>
        <select
            id="session"
            name="session"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select Session</option>
            <option value="2022-25">2022-25</option>
            <option value="2023-27">2023-27</option>
            <option value="2024-28">2024-28</option>
        </select>
        <p
            id="sessionError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Subjects -->
    <div>
        <label for="subject" class="block text-lg font-medium"
            >विषय (Hounours or MJC)</label
        >
        <select
            id="subject"
            name="subject"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select Subject</option>
            <option value="Physics">Physics</option>
            <option value="Chemistry">Chemistry</option>
            <option value="Botany">Botany</option>
            <option value="Zoology">Zoology</option>
            <option value="Mathematics">Mathematics</option>
            <option value="Hindi">Hindi</option>
            <option value="English">English</option>
            <option value="Urdu">Urdu</option>
            <option value="Sanskrit">Sanskrit</option>
            <option value="History">History</option>
            <option value="Political Science">Political Science</option>
            <option value="Economics">Economics</option>
            <option value="AIHC">AIHC</option>
            <option value="IRPM">IRPM</option>
            <option value="Sociology">Sociology</option>
            <option value="Philosophy">Philosophy</option>
            <option value="Psychology">Psychology</option>
        </select>
        <p
            id="subjectError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Class Roll No -->
    <div>
        <label for="class_roll_number" class="block text-lg font-medium"
            >कक्षा रोल नंबर</label
        >
        <input
            type="text"
            id="class_roll_number"
            name="class_roll_number"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="classRollError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- University Roll No -->
    <div>
        <label for="university_roll_number" class="block text-lg font-medium"
            >विश्वविद्यालय रोल नंबर</label
        >
        <input
            type="text"
            id="university_roll_number"
            name="university_roll_number"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="universityRollError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Registration Number -->
    <div>
        <label for="registration_number" class="block text-lg font-medium"
            >पंजीकरण संख्या (1480500012/2014 or TM148012/2014 As per
            registration slip.)</label
        >
        <input
            type="text"
            id="registration_number"
            name="registration_number"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="registrationNumberError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Father's Name -->
    <div>
        <label for="father_name" class="block text-lg font-medium"
            >पिता का नाम (Capital Letters only)</label
        >
        <input
            type="text"
            id="father_name"
            name="father_name"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="fatherNameError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Mother's Name -->
    <div>
        <label for="mother_name" class="block text-lg font-medium"
            >माता का नाम (Capital Letters only)</label
        >
        <input
            type="text"
            id="mother_name"
            name="mother_name"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="motherNameError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Pincode -->
    <div>
        <label for="pincode" class="block text-lg font-medium">पिनकोड</label>
        <input
            type="text"
            id="pincode"
            name="pincode"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="pincodeError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- State -->
    <div>
        <label for="state" class="block text-lg font-medium">राज्य</label>
        <select
            id="state"
            name="state"
            class="w-full p-2 border rounded"
            required
        >
            <option value="">Select State</option>
            <option value="BIHAR">BIHAR</option>
            <option value="OTHER">OTHER</option>
        </select>
        <p
            id="stateError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Aadhar Number -->
    <div>
        <label for="aadhar_number" class="block text-lg font-medium"
            >आधार नंबर</label
        >
        <input
            type="text"
            id="aadhar_number"
            name="aadhar_number"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="aadharError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- ABC ID -->
    <div>
        <label for="abc_id" class="block text-lg font-medium">ABC ID</label>
        <input
            type="text"
            id="abc_id"
            name="abc_id"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="abcIdError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Aadhar Linked Mobile -->
    <div>
        <label for="aadhar_mobile" class="block text-lg font-medium"
            >आधार लिंक मोबाइल नंबर</label
        >
        <input
            type="text"
            id="aadhar_mobile"
            name="aadhar_mobile"
            class="w-full p-2 border rounded"
            required
        />
        <p
            id="aadharMobileError"
            class="text-red-500 text-sm"
            style="display: none"
        ></p>
    </div>

    <!-- Submit Button -->
    <button
        type="submit"
        class="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
    >
        सबमिट करें
    </button>
</form>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        function validateField(field, value) {
            let error = "";
            switch (field) {
                case "name":
                    if (value !== value.toUpperCase()) {
                        error = "Name must be in capital letters";
                    }
                    break;

                case "email":
                    const emailPattern =
                        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                    if (!emailPattern.test(value)) {
                        error = "Invalid email address";
                    }
                    break;

                case "mobile":
                case "aadharMobile":
                    const mobilePattern = /^\d{10}$/;
                    if (!mobilePattern.test(value)) {
                        error = "Use valid mobile number.";
                    }
                    break;

                case "classRoll":
                    const classRollPattern = /^\d{1,4}$/;
                    if (!classRollPattern.test(value)) {
                        error = "Roll number of class in college.";
                    }
                    break;

                case "universityRoll":
                    const uniRollPattern = /^\d{5,10}$/;
                    if (!uniRollPattern.test(value)) {
                        error = "Roll number used in examination.";
                    }
                    break;

                case "registrationNumber":
                    const regPattern = /^(TM)?\d+\/\d{4}$/;
                    if (!regPattern.test(value)) {
                        error =
                            "Invalid registration number format (e.g., 1480500012/2014 or TM148012/2014)";
                    }
                    break;

                case "fatherName":
                case "motherName":
                    if (value !== value.toUpperCase()) {
                        error = "Name must be in capital letters";
                    }
                    break;

                case "pincode":
                    const pincodePattern = /^\d{6}$/;
                    if (!pincodePattern.test(value)) {
                        error = "Enter valid pincode";
                    }
                    break;

                case "aadhar":
                    const aadharPattern = /^\d{12}$/;
                    if (!aadharPattern.test(value)) {
                        error = "Enter valid AADHAAR ID";
                    }
                    break;

                case "abcId":
                    const abcPattern = /^\d{12}$/;
                    if (!abcPattern.test(value)) {
                        error = "Enter valid ABC ID";
                    }
                    break;
            }
            return error;
        }

        function validateForm() {
            let isValid = true;
            const fields = [
                "name",
                "email",
                "mobile",
                "classRoll",
                "universityRoll",
                "registrationNumber",
                "fatherName",
                "motherName",
                "pincode",
                "aadhar",
                "abcId",
                "aadharMobile",
            ];

            fields.forEach((field) => {
                const value = $("#" + field).val();
                const error = validateField(field, value);
                if (error) {
                    $("#" + field + "Error")
                        .text(error)
                        .show();
                    isValid = false;
                } else {
                    $("#" + field + "Error").hide();
                }
            });

            // Check required fields
            if (!$("#dob").val()) {
                $("#dobError").text("Date of birth is required").show();
                isValid = false;
            } else {
                $("#dobError").hide();
            }

            if (!$("input[name='gender']:checked").val()) {
                $("#genderError").text("Gender is required").show();
                isValid = false;
            } else {
                $("#genderError").hide();
            }

            if (!$("select[name='category']").val()) {
                $("#categoryError").text("Category is required").show();
                isValid = false;
            } else {
                $("#categoryError").hide();
            }

            if (!$("select[name='session']").val()) {
                $("#sessionError").text("Session is required").show();
                isValid = false;
            } else {
                $("#sessionError").hide();
            }

            if (!$("select[name='subject']").val()) {
                $("#subjectError").text("Subject is required").show();
                isValid = false;
            } else {
                $("#subjectError").hide();
            }

            return isValid;
        }

        $("#myForm").on("submit", function (event) {
            if (!validateForm()) {
                event.preventDefault();
            }
        });

        // Validate fields on input
        $("input, select").on("input change", function () {
            const field = $(this).attr("id");
            const value = $(this).val();
            const error = validateField(field, value);
            if (error) {
                $("#" + field + "Error")
                    .text(error)
                    .show();
            } else {
                $("#" + field + "Error").hide();
            }
        });
    });
</script>
{{ end }}
