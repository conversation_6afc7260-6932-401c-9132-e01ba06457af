-- New Workflow Queries for University Information Management System

-- Department Queries
-- name: GetAllDepartments :many
SELECT id, name, code, description, created_at, updated_at FROM departments
ORDER BY name;

-- name: GetDepartmentByID :one
SELECT id, name, code, description, created_at, updated_at FROM departments
WHERE id = $1;

-- name: GetDepartmentByCode :one
SELECT id, name, code, description, created_at, updated_at FROM departments
WHERE code = $1;

-- name: CreateDepartment :one
INSERT INTO departments (name, code, description)
VALUES ($1, $2, $3)
RETURNING id, name, code, description, created_at, updated_at;

-- name: UpdateDepartment :one
UPDATE departments 
SET name = $2, code = $3, description = $4, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, code, description, created_at, updated_at;

-- name: DeleteDepartment :exec
DELETE FROM departments WHERE id = $1;

-- User Queries (Admin and Department Admin)
-- name: GetUserByEmail :one
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE email = $1;

-- name: GetUserByID :one
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE id = $1;

-- name: CreateUser :one
INSERT INTO users (email, password_hash, role, department_id, is_active)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, email, password_hash, role, department_id, is_active, created_at, updated_at;

-- name: UpdateUser :one
UPDATE users 
SET email = $2, password_hash = $3, role = $4, department_id = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, email, password_hash, role, department_id, is_active, created_at, updated_at;

-- name: GetUsersByRole :many
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE role = $1 ORDER BY created_at DESC;

-- name: GetUsersByDepartment :many
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE department_id = $1 ORDER BY created_at DESC;

-- Student Queries (New Workflow)
-- name: CreateStudentRegistration :one
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at;

-- name: GetStudentByEmail :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE email = $1;

-- name: GetStudentByID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE id = $1;

-- name: GetStudentByAadhar :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE aadhar_number = $1;

-- name: GetStudentByABCID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE abc_id = $1;

-- name: UpdateStudentStatus :one
UPDATE students 
SET status = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at;

-- name: GetAllStudents :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2;

-- name: CountStudents :one
SELECT COUNT(*) FROM students;

-- name: GetStudentsByStatus :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE status = $1
ORDER BY created_at DESC;

-- Student Enrollment Queries
-- name: CreateStudentEnrollment :one
INSERT INTO student_enrollments (
    student_id, department_id, session, subject
) VALUES (
    $1, $2, $3, $4
) RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at;

-- name: GetEnrollmentByID :one
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE id = $1;

-- name: GetEnrollmentsByStudent :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE student_id = $1
ORDER BY created_at DESC;

-- name: GetEnrollmentsByDepartment :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE department_id = $1
ORDER BY created_at DESC;

-- name: GetEnrollmentsByStatus :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE status = $1
ORDER BY created_at DESC;

-- name: GetPendingEnrollmentsByDepartment :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments
WHERE department_id = $1 AND status = 'pending'
ORDER BY created_at ASC;

-- name: ApproveEnrollment :one
UPDATE student_enrollments
SET status = 'approved', approved_by = $2, approved_at = CURRENT_TIMESTAMP,
    class_roll_number = $3, university_roll_number = $4, registration_number = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at;

-- name: RejectEnrollment :one
UPDATE student_enrollments
SET status = 'rejected', approved_by = $2, approved_at = CURRENT_TIMESTAMP,
    rejection_reason = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at;

-- name: CountEnrollmentsByStatus :one
SELECT COUNT(*) FROM student_enrollments WHERE status = $1;

-- name: CountEnrollmentsByDepartmentAndStatus :one
SELECT COUNT(*) FROM student_enrollments
WHERE department_id = $1 AND status = $2;

-- Combined Queries for Dashboard and Reports
-- name: GetStudentWithEnrollments :many
SELECT
    s.id as student_id, s.name, s.email, s.status as student_status,
    e.id as enrollment_id, e.department_id, e.session, e.subject,
    e.class_roll_number, e.university_roll_number, e.registration_number,
    e.status as enrollment_status, e.created_at as enrollment_date,
    d.name as department_name, d.code as department_code
FROM students s
LEFT JOIN student_enrollments e ON s.id = e.student_id
LEFT JOIN departments d ON e.department_id = d.id
WHERE s.email = $1
ORDER BY e.created_at DESC;

-- name: GetDepartmentEnrollmentStats :one
SELECT
    d.id, d.name, d.code,
    COUNT(e.id) as total_enrollments,
    COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_enrollments,
    COUNT(CASE WHEN e.status = 'approved' THEN 1 END) as approved_enrollments,
    COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_enrollments
FROM departments d
LEFT JOIN student_enrollments e ON d.id = e.department_id
WHERE d.id = $1
GROUP BY d.id, d.name, d.code;
