-- name: CreateExamForm :one
INSERT INTO exam_form_degree_iii (
    roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING *;

-- name: GetExamFormByRoll :one
SELECT * FROM exam_form_degree_iii WHERE roll_number = $1;

-- name: UpdateExamFormByRoll :exec
UPDATE exam_form_degree_iii
SET
    category = $2,
    is_regular = $3,
    paper_i = $4,
    paper_ii = $5,
    paper_iii = $6,
    paper_iv = $7,
    fee = $8
WHERE roll_number = $1;

-- name: DeleteExamFormByRoll :exec
DELETE FROM exam_form_degree_iii WHERE roll_number = $1;

-- name: GetAllExamForms :many
SELECT * FROM exam_form_degree_iii ORDER BY id DESC;

-- name: GetExamFormByID :one
SELECT * FROM exam_form_degree_iii WHERE id = $1;

-- name: GetExamFormsByCategory :many
SELECT * FROM exam_form_degree_iii WHERE category = $1 ORDER BY id DESC;

-- name: GetExamFormsByType :many
SELECT * FROM exam_form_degree_iii WHERE is_regular = $1 ORDER BY id DESC;

-- name: CountExamForms :one
SELECT COUNT(*) FROM exam_form_degree_iii;

-- name: CountExamFormsByCategory :one
SELECT COUNT(*) FROM exam_form_degree_iii WHERE category = $1;

-- name: DeleteExamFormByID :exec
DELETE FROM exam_form_degree_iii WHERE id = $1;
