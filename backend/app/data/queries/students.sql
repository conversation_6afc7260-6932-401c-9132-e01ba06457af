-- Legacy Student Queries (Updated for New Schema)
-- Note: These are kept for backward compatibility but should use new_workflow.sql for new features

-- name: CreateStudentLegacy :one
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at;

-- name: GetStudentByIDLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE id = $1;

-- name: GetStudentByEmailLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE email = $1;

-- name: GetStudentByAadharLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE aadhar_number = $1;

-- name: GetStudentByABCIDLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE abc_id = $1;

-- name: GetAllStudentsLegacy :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2;

-- name: UpdateStudentLegacy :one
UPDATE students SET
    name = $2,
    email = $3,
    date_of_birth = $4,
    gender = $5,
    category = $6,
    mobile_number = $7,
    father_name = $8,
    mother_name = $9,
    pincode = $10,
    state = $11,
    aadhar_number = $12,
    abc_id = $13,
    aadhar_mobile = $14,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at;

-- name: DeleteStudentLegacy :exec
DELETE FROM students WHERE id = $1;

-- name: CountStudentsLegacy :one
SELECT COUNT(*) FROM students;
