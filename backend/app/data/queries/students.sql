-- queries.sql
-- name: CreateStudent :one
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    session, subject, class_roll_number, university_roll_number,
    registration_number, father_name, mother_name, pincode,
    state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
    $11, $12, $13, $14, $15, $16, $17, $18
) RETURNING *;

-- name: GetStudentByID :one
SELECT * FROM students WHERE id = $1;

-- name: GetStudentByEmail :one
SELECT * FROM students WHERE email = $1;

-- name: GetStudentByAadhar :one
SELECT * FROM students WHERE aadhar_number = $1;

-- name: GetStudentByABCID :one
SELECT * FROM students WHERE abc_id = $1;

-- name: GetStudentByRegistrationNumber :one
SELECT * FROM students WHERE registration_number = $1;

-- name: GetStudentByUniversityRollNumber :one
SELECT * FROM students WHERE university_roll_number = $1;

-- name: GetStudentByClassRoll :one
SELECT * FROM students WHERE class_roll_number = $1;

-- name: GetAllStudents :many
SELECT * FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2;

-- name: UpdateStudent :one
UPDATE students SET
    name = $2,
    email = $3,
    date_of_birth = $4,
    gender = $5,
    category = $6,
    mobile_number = $7,
    session = $8,
    subject = $9,
    class_roll_number = $10,
    university_roll_number = $11,
    registration_number = $12,
    father_name = $13,
    mother_name = $14,
    pincode = $15,
    state = $16,
    aadhar_number = $17,
    abc_id = $18,
    aadhar_mobile = $19,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING *;

-- name: DeleteStudent :exec
DELETE FROM students WHERE id = $1;

-- name: CountStudents :one
SELECT COUNT(*) FROM students;

-- name: GetStudentsBySubject :many
SELECT * FROM students WHERE subject = $1
ORDER BY created_at DESC;

-- name: GetStudentsBySession :many
SELECT * FROM students WHERE session = $1
ORDER BY created_at DESC;
