// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: students.sql

package data

import (
	"context"
	"time"
)

const countStudentsLegacy = `-- name: CountStudentsLegacy :one
SELECT COUNT(*) FROM students
`

func (q *Queries) CountStudentsLegacy(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, countStudentsLegacy)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createStudentLegacy = `-- name: CreateStudentLegacy :one

INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
`

type CreateStudentLegacyParams struct {
	Name         string
	Email        string
	DateOfBirth  time.Time
	Gender       string
	Category     string
	MobileNumber string
	FatherName   string
	MotherName   string
	Pincode      string
	State        string
	AadharNumber string
	AbcID        string
	AadharMobile string
}

// Legacy Student Queries (Updated for New Schema)
// Note: These are kept for backward compatibility but should use new_workflow.sql for new features
func (q *Queries) CreateStudentLegacy(ctx context.Context, arg CreateStudentLegacyParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, createStudentLegacy,
		arg.Name,
		arg.Email,
		arg.DateOfBirth,
		arg.Gender,
		arg.Category,
		arg.MobileNumber,
		arg.FatherName,
		arg.MotherName,
		arg.Pincode,
		arg.State,
		arg.AadharNumber,
		arg.AbcID,
		arg.AadharMobile,
	)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteStudentLegacy = `-- name: DeleteStudentLegacy :exec
DELETE FROM students WHERE id = $1
`

func (q *Queries) DeleteStudentLegacy(ctx context.Context, id int32) error {
	_, err := q.db.ExecContext(ctx, deleteStudentLegacy, id)
	return err
}

const getAllStudentsLegacy = `-- name: GetAllStudentsLegacy :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type GetAllStudentsLegacyParams struct {
	Limit  int32
	Offset int32
}

func (q *Queries) GetAllStudentsLegacy(ctx context.Context, arg GetAllStudentsLegacyParams) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getAllStudentsLegacy, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStudentByABCIDLegacy = `-- name: GetStudentByABCIDLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE abc_id = $1
`

func (q *Queries) GetStudentByABCIDLegacy(ctx context.Context, abcID string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByABCIDLegacy, abcID)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByAadharLegacy = `-- name: GetStudentByAadharLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE aadhar_number = $1
`

func (q *Queries) GetStudentByAadharLegacy(ctx context.Context, aadharNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByAadharLegacy, aadharNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByEmailLegacy = `-- name: GetStudentByEmailLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE email = $1
`

func (q *Queries) GetStudentByEmailLegacy(ctx context.Context, email string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByEmailLegacy, email)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByIDLegacy = `-- name: GetStudentByIDLegacy :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE id = $1
`

func (q *Queries) GetStudentByIDLegacy(ctx context.Context, id int32) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByIDLegacy, id)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateStudentLegacy = `-- name: UpdateStudentLegacy :one
UPDATE students SET
    name = $2,
    email = $3,
    date_of_birth = $4,
    gender = $5,
    category = $6,
    mobile_number = $7,
    father_name = $8,
    mother_name = $9,
    pincode = $10,
    state = $11,
    aadhar_number = $12,
    abc_id = $13,
    aadhar_mobile = $14,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
`

type UpdateStudentLegacyParams struct {
	ID           int32
	Name         string
	Email        string
	DateOfBirth  time.Time
	Gender       string
	Category     string
	MobileNumber string
	FatherName   string
	MotherName   string
	Pincode      string
	State        string
	AadharNumber string
	AbcID        string
	AadharMobile string
}

func (q *Queries) UpdateStudentLegacy(ctx context.Context, arg UpdateStudentLegacyParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, updateStudentLegacy,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.DateOfBirth,
		arg.Gender,
		arg.Category,
		arg.MobileNumber,
		arg.FatherName,
		arg.MotherName,
		arg.Pincode,
		arg.State,
		arg.AadharNumber,
		arg.AbcID,
		arg.AadharMobile,
	)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
