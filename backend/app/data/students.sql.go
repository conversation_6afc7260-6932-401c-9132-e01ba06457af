// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: students.sql

package data

import (
	"context"
	"time"
)

const countStudents = `-- name: CountStudents :one
SELECT COUNT(*) FROM students
`

func (q *Queries) CountStudents(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, countStudents)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createStudent = `-- name: CreateStudent :one
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    session, subject, class_roll_number, university_roll_number,
    registration_number, father_name, mother_name, pincode,
    state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
    $11, $12, $13, $14, $15, $16, $17, $18
) RETURNING id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at
`

type CreateStudentParams struct {
	Name                 string
	Email                string
	DateOfBirth          time.Time
	Gender               string
	Category             string
	MobileNumber         string
	Session              string
	Subject              string
	ClassRollNumber      string
	UniversityRollNumber string
	RegistrationNumber   string
	FatherName           string
	MotherName           string
	Pincode              string
	State                string
	AadharNumber         string
	AbcID                string
	AadharMobile         string
}

// queries.sql
func (q *Queries) CreateStudent(ctx context.Context, arg CreateStudentParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, createStudent,
		arg.Name,
		arg.Email,
		arg.DateOfBirth,
		arg.Gender,
		arg.Category,
		arg.MobileNumber,
		arg.Session,
		arg.Subject,
		arg.ClassRollNumber,
		arg.UniversityRollNumber,
		arg.RegistrationNumber,
		arg.FatherName,
		arg.MotherName,
		arg.Pincode,
		arg.State,
		arg.AadharNumber,
		arg.AbcID,
		arg.AadharMobile,
	)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteStudent = `-- name: DeleteStudent :exec
DELETE FROM students WHERE id = $1
`

func (q *Queries) DeleteStudent(ctx context.Context, id int32) error {
	_, err := q.db.ExecContext(ctx, deleteStudent, id)
	return err
}

const getAllStudents = `-- name: GetAllStudents :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type GetAllStudentsParams struct {
	Limit  int32
	Offset int32
}

func (q *Queries) GetAllStudents(ctx context.Context, arg GetAllStudentsParams) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getAllStudents, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.College,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStudentByABCID = `-- name: GetStudentByABCID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE abc_id = $1
`

func (q *Queries) GetStudentByABCID(ctx context.Context, abcID string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByABCID, abcID)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByAadhar = `-- name: GetStudentByAadhar :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE aadhar_number = $1
`

func (q *Queries) GetStudentByAadhar(ctx context.Context, aadharNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByAadhar, aadharNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByClassRoll = `-- name: GetStudentByClassRoll :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE class_roll_number = $1
`

func (q *Queries) GetStudentByClassRoll(ctx context.Context, classRollNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByClassRoll, classRollNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByEmail = `-- name: GetStudentByEmail :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE email = $1
`

func (q *Queries) GetStudentByEmail(ctx context.Context, email string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByEmail, email)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByID = `-- name: GetStudentByID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE id = $1
`

func (q *Queries) GetStudentByID(ctx context.Context, id int32) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByID, id)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByRegistrationNumber = `-- name: GetStudentByRegistrationNumber :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE registration_number = $1
`

func (q *Queries) GetStudentByRegistrationNumber(ctx context.Context, registrationNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByRegistrationNumber, registrationNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByUniversityRollNumber = `-- name: GetStudentByUniversityRollNumber :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE university_roll_number = $1
`

func (q *Queries) GetStudentByUniversityRollNumber(ctx context.Context, universityRollNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByUniversityRollNumber, universityRollNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentsBySession = `-- name: GetStudentsBySession :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE session = $1
ORDER BY created_at DESC
`

func (q *Queries) GetStudentsBySession(ctx context.Context, session string) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getStudentsBySession, session)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.College,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStudentsBySubject = `-- name: GetStudentsBySubject :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at FROM students WHERE subject = $1
ORDER BY created_at DESC
`

func (q *Queries) GetStudentsBySubject(ctx context.Context, subject string) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getStudentsBySubject, subject)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.College,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateStudent = `-- name: UpdateStudent :one
UPDATE students SET
    name = $2,
    email = $3,
    date_of_birth = $4,
    gender = $5,
    category = $6,
    mobile_number = $7,
    session = $8,
    subject = $9,
    class_roll_number = $10,
    university_roll_number = $11,
    registration_number = $12,
    father_name = $13,
    mother_name = $14,
    pincode = $15,
    state = $16,
    aadhar_number = $17,
    abc_id = $18,
    aadhar_mobile = $19,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, email, date_of_birth, gender, category, mobile_number, college, session, subject, class_roll_number, university_roll_number, registration_number, father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile, created_at, updated_at
`

type UpdateStudentParams struct {
	ID                   int32
	Name                 string
	Email                string
	DateOfBirth          time.Time
	Gender               string
	Category             string
	MobileNumber         string
	Session              string
	Subject              string
	ClassRollNumber      string
	UniversityRollNumber string
	RegistrationNumber   string
	FatherName           string
	MotherName           string
	Pincode              string
	State                string
	AadharNumber         string
	AbcID                string
	AadharMobile         string
}

func (q *Queries) UpdateStudent(ctx context.Context, arg UpdateStudentParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, updateStudent,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.DateOfBirth,
		arg.Gender,
		arg.Category,
		arg.MobileNumber,
		arg.Session,
		arg.Subject,
		arg.ClassRollNumber,
		arg.UniversityRollNumber,
		arg.RegistrationNumber,
		arg.FatherName,
		arg.MotherName,
		arg.Pincode,
		arg.State,
		arg.AadharNumber,
		arg.AbcID,
		arg.AadharMobile,
	)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.College,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
