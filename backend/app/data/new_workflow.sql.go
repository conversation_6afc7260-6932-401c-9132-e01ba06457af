// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: new_workflow.sql

package data

import (
	"context"
	"database/sql"
	"time"
)

const approveEnrollment = `-- name: ApproveEnrollment :one
UPDATE student_enrollments
SET status = 'approved', approved_by = $2, approved_at = CURRENT_TIMESTAMP,
    class_roll_number = $3, university_roll_number = $4, registration_number = $5,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
`

type ApproveEnrollmentParams struct {
	ID                   int32
	ApprovedBy           sql.NullInt32
	ClassRollNumber      sql.NullString
	UniversityRollNumber sql.NullString
	RegistrationNumber   sql.NullString
}

func (q *Queries) ApproveEnrollment(ctx context.Context, arg ApproveEnrollmentParams) (StudentEnrollment, error) {
	row := q.db.QueryRowContext(ctx, approveEnrollment,
		arg.ID,
		arg.ApprovedBy,
		arg.ClassRollNumber,
		arg.UniversityRollNumber,
		arg.RegistrationNumber,
	)
	var i StudentEnrollment
	err := row.Scan(
		&i.ID,
		&i.StudentID,
		&i.DepartmentID,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.Status,
		&i.ApprovedBy,
		&i.ApprovedAt,
		&i.RejectionReason,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const countEnrollmentsByDepartmentAndStatus = `-- name: CountEnrollmentsByDepartmentAndStatus :one
SELECT COUNT(*) FROM student_enrollments
WHERE department_id = $1 AND status = $2
`

type CountEnrollmentsByDepartmentAndStatusParams struct {
	DepartmentID int32
	Status       string
}

func (q *Queries) CountEnrollmentsByDepartmentAndStatus(ctx context.Context, arg CountEnrollmentsByDepartmentAndStatusParams) (int64, error) {
	row := q.db.QueryRowContext(ctx, countEnrollmentsByDepartmentAndStatus, arg.DepartmentID, arg.Status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countEnrollmentsByStatus = `-- name: CountEnrollmentsByStatus :one
SELECT COUNT(*) FROM student_enrollments WHERE status = $1
`

func (q *Queries) CountEnrollmentsByStatus(ctx context.Context, status string) (int64, error) {
	row := q.db.QueryRowContext(ctx, countEnrollmentsByStatus, status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countStudents = `-- name: CountStudents :one
SELECT COUNT(*) FROM students
`

func (q *Queries) CountStudents(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, countStudents)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createDepartment = `-- name: CreateDepartment :one
INSERT INTO departments (name, code, description)
VALUES ($1, $2, $3)
RETURNING id, name, code, description, created_at, updated_at
`

type CreateDepartmentParams struct {
	Name        string
	Code        string
	Description sql.NullString
}

func (q *Queries) CreateDepartment(ctx context.Context, arg CreateDepartmentParams) (Department, error) {
	row := q.db.QueryRowContext(ctx, createDepartment, arg.Name, arg.Code, arg.Description)
	var i Department
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Code,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createStudentEnrollment = `-- name: CreateStudentEnrollment :one
INSERT INTO student_enrollments (
    student_id, department_id, session, subject
) VALUES (
    $1, $2, $3, $4
) RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
`

type CreateStudentEnrollmentParams struct {
	StudentID    int32
	DepartmentID int32
	Session      string
	Subject      string
}

// Student Enrollment Queries
func (q *Queries) CreateStudentEnrollment(ctx context.Context, arg CreateStudentEnrollmentParams) (StudentEnrollment, error) {
	row := q.db.QueryRowContext(ctx, createStudentEnrollment,
		arg.StudentID,
		arg.DepartmentID,
		arg.Session,
		arg.Subject,
	)
	var i StudentEnrollment
	err := row.Scan(
		&i.ID,
		&i.StudentID,
		&i.DepartmentID,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.Status,
		&i.ApprovedBy,
		&i.ApprovedAt,
		&i.RejectionReason,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createStudentRegistration = `-- name: CreateStudentRegistration :one
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
) RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
`

type CreateStudentRegistrationParams struct {
	Name         string
	Email        string
	DateOfBirth  time.Time
	Gender       string
	Category     string
	MobileNumber string
	FatherName   string
	MotherName   string
	Pincode      string
	State        string
	AadharNumber string
	AbcID        string
	AadharMobile string
}

// Student Queries (New Workflow)
func (q *Queries) CreateStudentRegistration(ctx context.Context, arg CreateStudentRegistrationParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, createStudentRegistration,
		arg.Name,
		arg.Email,
		arg.DateOfBirth,
		arg.Gender,
		arg.Category,
		arg.MobileNumber,
		arg.FatherName,
		arg.MotherName,
		arg.Pincode,
		arg.State,
		arg.AadharNumber,
		arg.AbcID,
		arg.AadharMobile,
	)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createUser = `-- name: CreateUser :one
INSERT INTO users (email, password_hash, role, department_id, is_active)
VALUES ($1, $2, $3, $4, $5)
RETURNING id, email, password_hash, role, department_id, is_active, created_at, updated_at
`

type CreateUserParams struct {
	Email        string
	PasswordHash string
	Role         string
	DepartmentID sql.NullInt32
	IsActive     bool
}

func (q *Queries) CreateUser(ctx context.Context, arg CreateUserParams) (User, error) {
	row := q.db.QueryRowContext(ctx, createUser,
		arg.Email,
		arg.PasswordHash,
		arg.Role,
		arg.DepartmentID,
		arg.IsActive,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.PasswordHash,
		&i.Role,
		&i.DepartmentID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteDepartment = `-- name: DeleteDepartment :exec
DELETE FROM departments WHERE id = $1
`

func (q *Queries) DeleteDepartment(ctx context.Context, id int32) error {
	_, err := q.db.ExecContext(ctx, deleteDepartment, id)
	return err
}

const getAllDepartments = `-- name: GetAllDepartments :many

SELECT id, name, code, description, created_at, updated_at FROM departments
ORDER BY name
`

// New Workflow Queries for University Information Management System
// Department Queries
func (q *Queries) GetAllDepartments(ctx context.Context) ([]Department, error) {
	rows, err := q.db.QueryContext(ctx, getAllDepartments)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Department
	for rows.Next() {
		var i Department
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Code,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllStudents = `-- name: GetAllStudents :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students
ORDER BY created_at DESC
LIMIT $1 OFFSET $2
`

type GetAllStudentsParams struct {
	Limit  int32
	Offset int32
}

func (q *Queries) GetAllStudents(ctx context.Context, arg GetAllStudentsParams) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getAllStudents, arg.Limit, arg.Offset)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getDepartmentByCode = `-- name: GetDepartmentByCode :one
SELECT id, name, code, description, created_at, updated_at FROM departments
WHERE code = $1
`

func (q *Queries) GetDepartmentByCode(ctx context.Context, code string) (Department, error) {
	row := q.db.QueryRowContext(ctx, getDepartmentByCode, code)
	var i Department
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Code,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getDepartmentByID = `-- name: GetDepartmentByID :one
SELECT id, name, code, description, created_at, updated_at FROM departments
WHERE id = $1
`

func (q *Queries) GetDepartmentByID(ctx context.Context, id int32) (Department, error) {
	row := q.db.QueryRowContext(ctx, getDepartmentByID, id)
	var i Department
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Code,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getDepartmentEnrollmentStats = `-- name: GetDepartmentEnrollmentStats :one
SELECT
    d.id, d.name, d.code,
    COUNT(e.id) as total_enrollments,
    COUNT(CASE WHEN e.status = 'pending' THEN 1 END) as pending_enrollments,
    COUNT(CASE WHEN e.status = 'approved' THEN 1 END) as approved_enrollments,
    COUNT(CASE WHEN e.status = 'rejected' THEN 1 END) as rejected_enrollments
FROM departments d
LEFT JOIN student_enrollments e ON d.id = e.department_id
WHERE d.id = $1
GROUP BY d.id, d.name, d.code
`

type GetDepartmentEnrollmentStatsRow struct {
	ID                  int32
	Name                string
	Code                string
	TotalEnrollments    int64
	PendingEnrollments  int64
	ApprovedEnrollments int64
	RejectedEnrollments int64
}

func (q *Queries) GetDepartmentEnrollmentStats(ctx context.Context, id int32) (GetDepartmentEnrollmentStatsRow, error) {
	row := q.db.QueryRowContext(ctx, getDepartmentEnrollmentStats, id)
	var i GetDepartmentEnrollmentStatsRow
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Code,
		&i.TotalEnrollments,
		&i.PendingEnrollments,
		&i.ApprovedEnrollments,
		&i.RejectedEnrollments,
	)
	return i, err
}

const getEnrollmentByID = `-- name: GetEnrollmentByID :one
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE id = $1
`

func (q *Queries) GetEnrollmentByID(ctx context.Context, id int32) (StudentEnrollment, error) {
	row := q.db.QueryRowContext(ctx, getEnrollmentByID, id)
	var i StudentEnrollment
	err := row.Scan(
		&i.ID,
		&i.StudentID,
		&i.DepartmentID,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.Status,
		&i.ApprovedBy,
		&i.ApprovedAt,
		&i.RejectionReason,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getEnrollmentsByDepartment = `-- name: GetEnrollmentsByDepartment :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE department_id = $1
ORDER BY created_at DESC
`

func (q *Queries) GetEnrollmentsByDepartment(ctx context.Context, departmentID int32) ([]StudentEnrollment, error) {
	rows, err := q.db.QueryContext(ctx, getEnrollmentsByDepartment, departmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []StudentEnrollment
	for rows.Next() {
		var i StudentEnrollment
		if err := rows.Scan(
			&i.ID,
			&i.StudentID,
			&i.DepartmentID,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.Status,
			&i.ApprovedBy,
			&i.ApprovedAt,
			&i.RejectionReason,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEnrollmentsByStatus = `-- name: GetEnrollmentsByStatus :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE status = $1
ORDER BY created_at DESC
`

func (q *Queries) GetEnrollmentsByStatus(ctx context.Context, status string) ([]StudentEnrollment, error) {
	rows, err := q.db.QueryContext(ctx, getEnrollmentsByStatus, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []StudentEnrollment
	for rows.Next() {
		var i StudentEnrollment
		if err := rows.Scan(
			&i.ID,
			&i.StudentID,
			&i.DepartmentID,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.Status,
			&i.ApprovedBy,
			&i.ApprovedAt,
			&i.RejectionReason,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEnrollmentsByStudent = `-- name: GetEnrollmentsByStudent :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments WHERE student_id = $1
ORDER BY created_at DESC
`

func (q *Queries) GetEnrollmentsByStudent(ctx context.Context, studentID int32) ([]StudentEnrollment, error) {
	rows, err := q.db.QueryContext(ctx, getEnrollmentsByStudent, studentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []StudentEnrollment
	for rows.Next() {
		var i StudentEnrollment
		if err := rows.Scan(
			&i.ID,
			&i.StudentID,
			&i.DepartmentID,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.Status,
			&i.ApprovedBy,
			&i.ApprovedAt,
			&i.RejectionReason,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPendingEnrollmentsByDepartment = `-- name: GetPendingEnrollmentsByDepartment :many
SELECT id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
FROM student_enrollments
WHERE department_id = $1 AND status = 'pending'
ORDER BY created_at ASC
`

func (q *Queries) GetPendingEnrollmentsByDepartment(ctx context.Context, departmentID int32) ([]StudentEnrollment, error) {
	rows, err := q.db.QueryContext(ctx, getPendingEnrollmentsByDepartment, departmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []StudentEnrollment
	for rows.Next() {
		var i StudentEnrollment
		if err := rows.Scan(
			&i.ID,
			&i.StudentID,
			&i.DepartmentID,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.Status,
			&i.ApprovedBy,
			&i.ApprovedAt,
			&i.RejectionReason,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStudentByABCID = `-- name: GetStudentByABCID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE abc_id = $1
`

func (q *Queries) GetStudentByABCID(ctx context.Context, abcID string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByABCID, abcID)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByAadhar = `-- name: GetStudentByAadhar :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE aadhar_number = $1
`

func (q *Queries) GetStudentByAadhar(ctx context.Context, aadharNumber string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByAadhar, aadharNumber)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByEmail = `-- name: GetStudentByEmail :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE email = $1
`

func (q *Queries) GetStudentByEmail(ctx context.Context, email string) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByEmail, email)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentByID = `-- name: GetStudentByID :one
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE id = $1
`

func (q *Queries) GetStudentByID(ctx context.Context, id int32) (Student, error) {
	row := q.db.QueryRowContext(ctx, getStudentByID, id)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getStudentWithEnrollments = `-- name: GetStudentWithEnrollments :many
SELECT
    s.id as student_id, s.name, s.email, s.status as student_status,
    e.id as enrollment_id, e.department_id, e.session, e.subject,
    e.class_roll_number, e.university_roll_number, e.registration_number,
    e.status as enrollment_status, e.created_at as enrollment_date,
    d.name as department_name, d.code as department_code
FROM students s
LEFT JOIN student_enrollments e ON s.id = e.student_id
LEFT JOIN departments d ON e.department_id = d.id
WHERE s.email = $1
ORDER BY e.created_at DESC
`

type GetStudentWithEnrollmentsRow struct {
	StudentID            int32
	Name                 string
	Email                string
	StudentStatus        string
	EnrollmentID         sql.NullInt32
	DepartmentID         sql.NullInt32
	Session              sql.NullString
	Subject              sql.NullString
	ClassRollNumber      sql.NullString
	UniversityRollNumber sql.NullString
	RegistrationNumber   sql.NullString
	EnrollmentStatus     sql.NullString
	EnrollmentDate       sql.NullTime
	DepartmentName       sql.NullString
	DepartmentCode       sql.NullString
}

// Combined Queries for Dashboard and Reports
func (q *Queries) GetStudentWithEnrollments(ctx context.Context, email string) ([]GetStudentWithEnrollmentsRow, error) {
	rows, err := q.db.QueryContext(ctx, getStudentWithEnrollments, email)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetStudentWithEnrollmentsRow
	for rows.Next() {
		var i GetStudentWithEnrollmentsRow
		if err := rows.Scan(
			&i.StudentID,
			&i.Name,
			&i.Email,
			&i.StudentStatus,
			&i.EnrollmentID,
			&i.DepartmentID,
			&i.Session,
			&i.Subject,
			&i.ClassRollNumber,
			&i.UniversityRollNumber,
			&i.RegistrationNumber,
			&i.EnrollmentStatus,
			&i.EnrollmentDate,
			&i.DepartmentName,
			&i.DepartmentCode,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getStudentsByStatus = `-- name: GetStudentsByStatus :many
SELECT id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
FROM students WHERE status = $1
ORDER BY created_at DESC
`

func (q *Queries) GetStudentsByStatus(ctx context.Context, status string) ([]Student, error) {
	rows, err := q.db.QueryContext(ctx, getStudentsByStatus, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Student
	for rows.Next() {
		var i Student
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Email,
			&i.DateOfBirth,
			&i.Gender,
			&i.Category,
			&i.MobileNumber,
			&i.FatherName,
			&i.MotherName,
			&i.Pincode,
			&i.State,
			&i.AadharNumber,
			&i.AbcID,
			&i.AadharMobile,
			&i.Status,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserByEmail = `-- name: GetUserByEmail :one
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE email = $1
`

// User Queries (Admin and Department Admin)
func (q *Queries) GetUserByEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRowContext(ctx, getUserByEmail, email)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.PasswordHash,
		&i.Role,
		&i.DepartmentID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserByID = `-- name: GetUserByID :one
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE id = $1
`

func (q *Queries) GetUserByID(ctx context.Context, id int32) (User, error) {
	row := q.db.QueryRowContext(ctx, getUserByID, id)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.PasswordHash,
		&i.Role,
		&i.DepartmentID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUsersByDepartment = `-- name: GetUsersByDepartment :many
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE department_id = $1 ORDER BY created_at DESC
`

func (q *Queries) GetUsersByDepartment(ctx context.Context, departmentID sql.NullInt32) ([]User, error) {
	rows, err := q.db.QueryContext(ctx, getUsersByDepartment, departmentID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Email,
			&i.PasswordHash,
			&i.Role,
			&i.DepartmentID,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUsersByRole = `-- name: GetUsersByRole :many
SELECT id, email, password_hash, role, department_id, is_active, created_at, updated_at 
FROM users WHERE role = $1 ORDER BY created_at DESC
`

func (q *Queries) GetUsersByRole(ctx context.Context, role string) ([]User, error) {
	rows, err := q.db.QueryContext(ctx, getUsersByRole, role)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []User
	for rows.Next() {
		var i User
		if err := rows.Scan(
			&i.ID,
			&i.Email,
			&i.PasswordHash,
			&i.Role,
			&i.DepartmentID,
			&i.IsActive,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const rejectEnrollment = `-- name: RejectEnrollment :one
UPDATE student_enrollments
SET status = 'rejected', approved_by = $2, approved_at = CURRENT_TIMESTAMP,
    rejection_reason = $3, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, student_id, department_id, session, subject, class_roll_number,
    university_roll_number, registration_number, status, approved_by, approved_at,
    rejection_reason, created_at, updated_at
`

type RejectEnrollmentParams struct {
	ID              int32
	ApprovedBy      sql.NullInt32
	RejectionReason sql.NullString
}

func (q *Queries) RejectEnrollment(ctx context.Context, arg RejectEnrollmentParams) (StudentEnrollment, error) {
	row := q.db.QueryRowContext(ctx, rejectEnrollment, arg.ID, arg.ApprovedBy, arg.RejectionReason)
	var i StudentEnrollment
	err := row.Scan(
		&i.ID,
		&i.StudentID,
		&i.DepartmentID,
		&i.Session,
		&i.Subject,
		&i.ClassRollNumber,
		&i.UniversityRollNumber,
		&i.RegistrationNumber,
		&i.Status,
		&i.ApprovedBy,
		&i.ApprovedAt,
		&i.RejectionReason,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateDepartment = `-- name: UpdateDepartment :one
UPDATE departments 
SET name = $2, code = $3, description = $4, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, code, description, created_at, updated_at
`

type UpdateDepartmentParams struct {
	ID          int32
	Name        string
	Code        string
	Description sql.NullString
}

func (q *Queries) UpdateDepartment(ctx context.Context, arg UpdateDepartmentParams) (Department, error) {
	row := q.db.QueryRowContext(ctx, updateDepartment,
		arg.ID,
		arg.Name,
		arg.Code,
		arg.Description,
	)
	var i Department
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Code,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateStudentStatus = `-- name: UpdateStudentStatus :one
UPDATE students 
SET status = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
    father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
    status, created_at, updated_at
`

type UpdateStudentStatusParams struct {
	ID     int32
	Status string
}

func (q *Queries) UpdateStudentStatus(ctx context.Context, arg UpdateStudentStatusParams) (Student, error) {
	row := q.db.QueryRowContext(ctx, updateStudentStatus, arg.ID, arg.Status)
	var i Student
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.DateOfBirth,
		&i.Gender,
		&i.Category,
		&i.MobileNumber,
		&i.FatherName,
		&i.MotherName,
		&i.Pincode,
		&i.State,
		&i.AadharNumber,
		&i.AbcID,
		&i.AadharMobile,
		&i.Status,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUser = `-- name: UpdateUser :one
UPDATE users 
SET email = $2, password_hash = $3, role = $4, department_id = $5, is_active = $6, updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, email, password_hash, role, department_id, is_active, created_at, updated_at
`

type UpdateUserParams struct {
	ID           int32
	Email        string
	PasswordHash string
	Role         string
	DepartmentID sql.NullInt32
	IsActive     bool
}

func (q *Queries) UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error) {
	row := q.db.QueryRowContext(ctx, updateUser,
		arg.ID,
		arg.Email,
		arg.PasswordHash,
		arg.Role,
		arg.DepartmentID,
		arg.IsActive,
	)
	var i User
	err := row.Scan(
		&i.ID,
		&i.Email,
		&i.PasswordHash,
		&i.Role,
		&i.DepartmentID,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
