-- schema.sql

-- Departments table
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(100) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert default departments
INSERT INTO departments (name, code, description) VALUES
('Science Department', 'SCI', 'Physics, Chemistry, Mathematics, Botany, Zoology'),
('Language Department', 'LANG', 'Hindi, English, Urdu, Sanskrit'),
('Social Science Department', 'SOC', 'History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM');

-- Users table (for admin and department admins)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('super_admin', 'department_admin')),
    department_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert super admin (password will be hashed)
INSERT INTO users (email, password_hash, role) VALUES
('<EMAIL>', '$2a$10$placeholder_hash', 'super_admin');

-- Students table (updated for new workflow)
CREATE TABLE students (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    date_of_birth DATE NOT NULL,
    gender CHAR(1) NOT NULL CHECK (gender IN ('M', 'F')),
    category VARCHAR(10) NOT NULL CHECK (
        category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')
    ),
    mobile_number VARCHAR(10) NOT NULL,
    father_name VARCHAR(100) NOT NULL,
    mother_name VARCHAR(100) NOT NULL,
    pincode VARCHAR(6) NOT NULL,
    state VARCHAR(20) NOT NULL CHECK (state IN ('BIHAR', 'OTHER')),
    aadhar_number VARCHAR(12) NOT NULL UNIQUE,
    abc_id VARCHAR(12) NOT NULL UNIQUE,
    aadhar_mobile VARCHAR(10) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'registered' CHECK (status IN ('registered', 'enrolled', 'approved', 'rejected')),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Student enrollments table (for admission details)
CREATE TABLE student_enrollments (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    department_id INTEGER NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
    session VARCHAR(10) NOT NULL,
    subject VARCHAR(50) NOT NULL,
    class_roll_number VARCHAR(4),
    university_roll_number VARCHAR(10),
    registration_number VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    approved_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    approved_at TIMESTAMP,
    rejection_reason TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, department_id, session)
);

-- Add indexes for commonly queried fields
CREATE INDEX idx_students_email ON students (email);
CREATE INDEX idx_students_aadhar ON students (aadhar_number);
CREATE INDEX idx_students_status ON students (status);

CREATE INDEX idx_users_email ON users (email);
CREATE INDEX idx_users_role ON users (role);
CREATE INDEX idx_users_department ON users (department_id);

CREATE INDEX idx_departments_code ON departments (code);

CREATE INDEX idx_enrollments_student ON student_enrollments (student_id);
CREATE INDEX idx_enrollments_department ON student_enrollments (department_id);
CREATE INDEX idx_enrollments_status ON student_enrollments (status);
CREATE INDEX idx_enrollments_session ON student_enrollments (session);

CREATE TABLE exam_form_degree_iii (
    id SERIAL PRIMARY KEY,
    roll_number VARCHAR(10) NOT NULL UNIQUE CHECK (LENGTH (roll_number) BETWEEN 5 AND 10),
    category VARCHAR(10) NOT NULL CHECK (
        category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')
    ),
    is_regular BOOLEAN NOT NULL DEFAULT TRUE,
    paper_i BOOLEAN NOT NULL DEFAULT FALSE,
    paper_ii BOOLEAN NOT NULL DEFAULT FALSE,
    paper_iii BOOLEAN NOT NULL DEFAULT FALSE,
    paper_iv BOOLEAN NOT NULL DEFAULT FALSE,
    fee DECIMAL(10, 2) NOT NULL
);
