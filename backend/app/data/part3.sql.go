// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0
// source: part3.sql

package data

import (
	"context"
)

const countExamForms = `-- name: CountExamForms :one
SELECT COUNT(*) FROM exam_form_degree_iii
`

func (q *Queries) CountExamForms(ctx context.Context) (int64, error) {
	row := q.db.QueryRowContext(ctx, countExamForms)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const countExamFormsByCategory = `-- name: CountExamFormsByCategory :one
SELECT COUNT(*) FROM exam_form_degree_iii WHERE category = $1
`

func (q *Queries) CountExamFormsByCategory(ctx context.Context, category string) (int64, error) {
	row := q.db.QueryRowContext(ctx, countExamFormsByCategory, category)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createExamForm = `-- name: CreateExamForm :one
INSERT INTO exam_form_degree_iii (
    roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8
) RETURNING id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee
`

type CreateExamFormParams struct {
	RollNumber string
	Category   string
	IsRegular  bool
	PaperI     bool
	PaperIi    bool
	PaperIii   bool
	PaperIv    bool
	Fee        string
}

func (q *Queries) CreateExamForm(ctx context.Context, arg CreateExamFormParams) (ExamFormDegreeIii, error) {
	row := q.db.QueryRowContext(ctx, createExamForm,
		arg.RollNumber,
		arg.Category,
		arg.IsRegular,
		arg.PaperI,
		arg.PaperIi,
		arg.PaperIii,
		arg.PaperIv,
		arg.Fee,
	)
	var i ExamFormDegreeIii
	err := row.Scan(
		&i.ID,
		&i.RollNumber,
		&i.Category,
		&i.IsRegular,
		&i.PaperI,
		&i.PaperIi,
		&i.PaperIii,
		&i.PaperIv,
		&i.Fee,
	)
	return i, err
}

const deleteExamFormByID = `-- name: DeleteExamFormByID :exec
DELETE FROM exam_form_degree_iii WHERE id = $1
`

func (q *Queries) DeleteExamFormByID(ctx context.Context, id int32) error {
	_, err := q.db.ExecContext(ctx, deleteExamFormByID, id)
	return err
}

const deleteExamFormByRoll = `-- name: DeleteExamFormByRoll :exec
DELETE FROM exam_form_degree_iii WHERE roll_number = $1
`

func (q *Queries) DeleteExamFormByRoll(ctx context.Context, rollNumber string) error {
	_, err := q.db.ExecContext(ctx, deleteExamFormByRoll, rollNumber)
	return err
}

const getAllExamForms = `-- name: GetAllExamForms :many
SELECT id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee FROM exam_form_degree_iii ORDER BY id DESC
`

func (q *Queries) GetAllExamForms(ctx context.Context) ([]ExamFormDegreeIii, error) {
	rows, err := q.db.QueryContext(ctx, getAllExamForms)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ExamFormDegreeIii
	for rows.Next() {
		var i ExamFormDegreeIii
		if err := rows.Scan(
			&i.ID,
			&i.RollNumber,
			&i.Category,
			&i.IsRegular,
			&i.PaperI,
			&i.PaperIi,
			&i.PaperIii,
			&i.PaperIv,
			&i.Fee,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getExamFormByID = `-- name: GetExamFormByID :one
SELECT id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee FROM exam_form_degree_iii WHERE id = $1
`

func (q *Queries) GetExamFormByID(ctx context.Context, id int32) (ExamFormDegreeIii, error) {
	row := q.db.QueryRowContext(ctx, getExamFormByID, id)
	var i ExamFormDegreeIii
	err := row.Scan(
		&i.ID,
		&i.RollNumber,
		&i.Category,
		&i.IsRegular,
		&i.PaperI,
		&i.PaperIi,
		&i.PaperIii,
		&i.PaperIv,
		&i.Fee,
	)
	return i, err
}

const getExamFormByRoll = `-- name: GetExamFormByRoll :one
SELECT id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee FROM exam_form_degree_iii WHERE roll_number = $1
`

func (q *Queries) GetExamFormByRoll(ctx context.Context, rollNumber string) (ExamFormDegreeIii, error) {
	row := q.db.QueryRowContext(ctx, getExamFormByRoll, rollNumber)
	var i ExamFormDegreeIii
	err := row.Scan(
		&i.ID,
		&i.RollNumber,
		&i.Category,
		&i.IsRegular,
		&i.PaperI,
		&i.PaperIi,
		&i.PaperIii,
		&i.PaperIv,
		&i.Fee,
	)
	return i, err
}

const getExamFormsByCategory = `-- name: GetExamFormsByCategory :many
SELECT id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee FROM exam_form_degree_iii WHERE category = $1 ORDER BY id DESC
`

func (q *Queries) GetExamFormsByCategory(ctx context.Context, category string) ([]ExamFormDegreeIii, error) {
	rows, err := q.db.QueryContext(ctx, getExamFormsByCategory, category)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ExamFormDegreeIii
	for rows.Next() {
		var i ExamFormDegreeIii
		if err := rows.Scan(
			&i.ID,
			&i.RollNumber,
			&i.Category,
			&i.IsRegular,
			&i.PaperI,
			&i.PaperIi,
			&i.PaperIii,
			&i.PaperIv,
			&i.Fee,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getExamFormsByType = `-- name: GetExamFormsByType :many
SELECT id, roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee FROM exam_form_degree_iii WHERE is_regular = $1 ORDER BY id DESC
`

func (q *Queries) GetExamFormsByType(ctx context.Context, isRegular bool) ([]ExamFormDegreeIii, error) {
	rows, err := q.db.QueryContext(ctx, getExamFormsByType, isRegular)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ExamFormDegreeIii
	for rows.Next() {
		var i ExamFormDegreeIii
		if err := rows.Scan(
			&i.ID,
			&i.RollNumber,
			&i.Category,
			&i.IsRegular,
			&i.PaperI,
			&i.PaperIi,
			&i.PaperIii,
			&i.PaperIv,
			&i.Fee,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateExamFormByRoll = `-- name: UpdateExamFormByRoll :exec
UPDATE exam_form_degree_iii
SET
    category = $2,
    is_regular = $3,
    paper_i = $4,
    paper_ii = $5,
    paper_iii = $6,
    paper_iv = $7,
    fee = $8
WHERE roll_number = $1
`

type UpdateExamFormByRollParams struct {
	RollNumber string
	Category   string
	IsRegular  bool
	PaperI     bool
	PaperIi    bool
	PaperIii   bool
	PaperIv    bool
	Fee        string
}

func (q *Queries) UpdateExamFormByRoll(ctx context.Context, arg UpdateExamFormByRollParams) error {
	_, err := q.db.ExecContext(ctx, updateExamFormByRoll,
		arg.RollNumber,
		arg.Category,
		arg.IsRegular,
		arg.PaperI,
		arg.PaperIi,
		arg.PaperIii,
		arg.PaperIv,
		arg.Fee,
	)
	return err
}
