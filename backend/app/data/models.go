// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0

package data

import (
	"database/sql"
	"time"
)

type Department struct {
	ID          int32
	Name        string
	Code        string
	Description sql.NullString
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type ExamFormDegreeIii struct {
	ID         int32
	RollNumber string
	Category   string
	IsRegular  bool
	PaperI     bool
	PaperIi    bool
	PaperIii   bool
	PaperIv    bool
	Fee        string
}

type Student struct {
	ID           int32
	Name         string
	Email        string
	DateOfBirth  time.Time
	Gender       string
	Category     string
	MobileNumber string
	FatherName   string
	MotherName   string
	Pincode      string
	State        string
	AadharNumber string
	AbcID        string
	AadharMobile string
	Status       string
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

type StudentEnrollment struct {
	ID                   int32
	StudentID            int32
	DepartmentID         int32
	Session              string
	Subject              string
	ClassRollNumber      sql.NullString
	UniversityRollNumber sql.NullString
	RegistrationNumber   sql.NullString
	Status               string
	ApprovedBy           sql.NullInt32
	ApprovedAt           sql.NullTime
	RejectionReason      sql.NullString
	CreatedAt            time.Time
	UpdatedAt            time.Time
}

type User struct {
	ID           int32
	Email        string
	PasswordHash string
	Role         string
	DepartmentID sql.NullInt32
	IsActive     bool
	CreatedAt    time.Time
	UpdatedAt    time.Time
}
