// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0

package data

import (
	"time"
)

type ExamFormDegreeIii struct {
	ID         int32
	RollNumber string
	Category   string
	IsRegular  bool
	PaperI     bool
	PaperIi    bool
	PaperIii   bool
	PaperIv    bool
	Fee        string
}

type Student struct {
	ID                   int32
	Name                 string
	Email                string
	DateOfBirth          time.Time
	Gender               string
	Category             string
	MobileNumber         string
	College              string
	Session              string
	Subject              string
	ClassRollNumber      string
	UniversityRollNumber string
	RegistrationNumber   string
	FatherName           string
	MotherName           string
	Pincode              string
	State                string
	AadharNumber         string
	AbcID                string
	AadharMobile         string
	CreatedAt            time.Time
	UpdatedAt            time.Time
}
