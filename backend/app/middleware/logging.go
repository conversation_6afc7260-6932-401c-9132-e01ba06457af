package middleware

import (
	"log"
	"net/http"
	"strings"
	"time"
)

func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Define excluded URL paths
		excludedPaths := []string{"/static/", "/images/", "/favicon"}

		// Check if the current path should be excluded
		for _, path := range excludedPaths {
			if strings.HasPrefix(r.URL.Path, path) {
				next.ServeHTTP(w, r) // Call the next handler without logging
				return
			}
		}

		start := time.Now()
		// Call the next handler in the chain
		next.ServeHTTP(w, r)

		// Log request details with timestamp, method, path and execution duration
		duration := time.Since(start)
		log.Printf("[%s] %s %s | Duration: %v | Status: completed",
			time.Now().Format("02-01-2006 15:04:05"),
			r.Method,
			r.URL.Path,
			duration)
	})
}
