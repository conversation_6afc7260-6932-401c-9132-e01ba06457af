package middleware

import (
	"context"
	"net/http"
)

// Context<PERSON><PERSON> prevents key collisions in context storage
type Context<PERSON><PERSON> string

const Authenticated<PERSON>ey ContextKey = "authenticated"

// AuthStatusMiddleware adds authentication status to request context
func AuthStatusMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Default: User is not authenticated
		isAuthenticated := false

		// Check session cookie
		cookie, err := r.<PERSON>ie("session")
		if err == nil && cookie.Value == "authenticated" {
			isAuthenticated = true
		}

		// Store auth status in request context
		ctx := context.WithValue(r.Context(), AuthenticatedKey, isAuthenticated)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}

func AuthMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		cookie, err := r.<PERSON>("session")
		if err != nil || cookie.Value != "authenticated" {
			http.Redirect(w, r, "/login", http.StatusSeeOther)
			return
		}
		next.ServeHTTP(w, r)
	})
}
