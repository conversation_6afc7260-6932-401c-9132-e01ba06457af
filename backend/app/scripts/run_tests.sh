#!/bin/bash

# Test runner script for the University Exam Form Management System
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_DB_NAME="balena_test"
TEST_DB_USER="balena"
TEST_DB_PASSWORD="test_password"
TEST_DB_HOST="localhost"
TEST_DB_PORT="5433"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if PostgreSQL is running
check_postgres() {
    print_status "Checking PostgreSQL connection..."

    # Try to connect using netcat or telnet to check if port is open
    if command -v nc >/dev/null 2>&1; then
        if ! nc -z "$TEST_DB_HOST" "$TEST_DB_PORT" 2>/dev/null; then
            print_warning "PostgreSQL on port $TEST_DB_PORT not available, trying port 5432..."
            if ! nc -z "$TEST_DB_HOST" "5432" 2>/dev/null; then
                print_error "PostgreSQL is not running or not accessible on ports 5432 or $TEST_DB_PORT"
                print_error "Please start PostgreSQL and try again"
                print_error "For Docker: docker-compose up postgres -d"
                print_error "For local: brew services start postgresql (on macOS)"
                exit 1
            else
                # Update port to 5432 if that's where PostgreSQL is running
                TEST_DB_PORT="5432"
                export DB_PORT="5432"
            fi
        fi
    else
        # If nc is not available, try using docker to check
        if docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -q "5432"; then
            print_status "Found PostgreSQL container running on port 5432"
            TEST_DB_PORT="5432"
            export DB_PORT="5432"
        elif docker ps --format "table {{.Names}}\t{{.Ports}}" | grep -q "5433"; then
            print_status "Found PostgreSQL container running on port 5433"
            TEST_DB_PORT="5433"
            export DB_PORT="5433"
        else
            print_error "No PostgreSQL container found running"
            print_error "Please start PostgreSQL with: docker-compose up postgres -d"
            exit 1
        fi
    fi

    print_success "PostgreSQL is running on $TEST_DB_HOST:$TEST_DB_PORT"
}

# Function to set up test environment
setup_test_env() {
    print_status "Setting up test environment..."
    
    # Set test environment variables
    export DB_HOST="$TEST_DB_HOST"
    export DB_PORT="$TEST_DB_PORT"
    export DB_USER="$TEST_DB_USER"
    export DB_PASSWORD="$TEST_DB_PASSWORD"
    export DB_NAME="$TEST_DB_NAME"
    export JWT_SECRET="test_jwt_secret_for_testing_only"
    export APP_USERNAME="test_admin"
    export APP_PASSWORD="test_password"
    export APP_ENV="test"
    export LOG_LEVEL="error" # Reduce log noise during tests
    
    print_success "Test environment configured"
}

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    # Run tests that don't require database
    go test -v -tags=unit \
        ./internal/config/tests/... \
        ./internal/errors/tests/... \
        ./internal/models/tests/... \
        ./internal/auth/tests/... \
        ./internal/middleware/tests/...
    
    if [ $? -eq 0 ]; then
        print_success "Unit tests passed"
    else
        print_error "Unit tests failed"
        exit 1
    fi
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."

    # Run tests that require database
    go test -v -tags=integration \
        ./internal/repository/tests/... \
        ./internal/services/tests/... \
        ./tests/integration/...

    if [ $? -eq 0 ]; then
        print_success "Integration tests passed"
    else
        print_error "Integration tests failed"
        exit 1
    fi
}

# Function to run handler tests
run_handler_tests() {
    print_status "Running handler tests..."

    # Run handler tests
    go test -v ./tests/handlers/... -count=1

    if [ $? -eq 0 ]; then
        print_success "Handler tests passed"
    else
        print_error "Handler tests failed"
        exit 1
    fi
}

# Function to run template tests
run_template_tests() {
    print_status "Running template tests..."

    # Run template tests (no DB required)
    go test -v ./tests/templates/... -count=1

    if [ $? -eq 0 ]; then
        print_success "Template tests passed"
    else
        print_error "Template tests failed"
        exit 1
    fi
}

# Function to run workflow tests
run_workflow_tests() {
    print_status "Running workflow tests..."

    # Run workflow tests
    go test -v ./tests/workflows/... -count=1

    if [ $? -eq 0 ]; then
        print_success "Workflow tests passed"
    else
        print_error "Workflow tests failed"
        exit 1
    fi
}

# Function to run error handling tests
run_error_tests() {
    print_status "Running error handling tests..."

    # Run error tests
    go test -v ./tests/errors/... -count=1

    if [ $? -eq 0 ]; then
        print_success "Error handling tests passed"
    else
        print_error "Error handling tests failed"
        exit 1
    fi
}

# Function to run all tests
run_all_tests() {
    print_status "Running all tests..."
    
    go test -v ./...
    
    if [ $? -eq 0 ]; then
        print_success "All tests passed"
    else
        print_error "Some tests failed"
        exit 1
    fi
}

# Function to run tests with coverage
run_tests_with_coverage() {
    print_status "Running tests with coverage..."
    
    # Create coverage directory
    mkdir -p coverage
    
    # Run tests with coverage
    go test -v -coverprofile=coverage/coverage.out ./...
    
    if [ $? -eq 0 ]; then
        print_success "Tests with coverage completed"
        
        # Generate HTML coverage report
        go tool cover -html=coverage/coverage.out -o coverage/coverage.html
        print_success "Coverage report generated: coverage/coverage.html"
        
        # Show coverage summary
        go tool cover -func=coverage/coverage.out | tail -1
    else
        print_error "Tests with coverage failed"
        exit 1
    fi
}

# Function to run specific test
run_specific_test() {
    local test_pattern="$1"
    print_status "Running tests matching pattern: $test_pattern"
    
    go test -v -run "$test_pattern" ./...
    
    if [ $? -eq 0 ]; then
        print_success "Specific tests passed"
    else
        print_error "Specific tests failed"
        exit 1
    fi
}

# Function to run benchmarks
run_benchmarks() {
    print_status "Running benchmarks..."
    
    go test -v -bench=. -benchmem ./...
    
    if [ $? -eq 0 ]; then
        print_success "Benchmarks completed"
    else
        print_error "Benchmarks failed"
        exit 1
    fi
}

# Function to clean up test artifacts
cleanup() {
    print_status "Cleaning up test artifacts..."
    
    # Remove coverage files
    rm -rf coverage/
    
    # Remove test databases (if any were left behind)
    # This is handled by the test cleanup functions
    
    print_success "Cleanup completed"
}

# Function to show help
show_help() {
    echo "University Exam Form Management System - Test Runner"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  unit                Run unit tests only"
    echo "  integration         Run integration tests only"
    echo "  handlers            Run HTTP handler tests"
    echo "  templates           Run template rendering tests"
    echo "  workflows           Run complete workflow tests"
    echo "  errors              Run error handling tests"
    echo "  all                 Run all tests (default)"
    echo "  coverage            Run tests with coverage report"
    echo "  bench               Run benchmarks"
    echo "  specific PATTERN    Run tests matching PATTERN"
    echo "  cleanup             Clean up test artifacts"
    echo "  help                Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                  # Run all tests"
    echo "  $0 unit             # Run only unit tests"
    echo "  $0 coverage         # Run tests with coverage"
    echo "  $0 specific Student # Run tests with 'Student' in name"
    echo ""
    echo "Environment Variables:"
    echo "  TEST_DB_HOST        PostgreSQL host (default: localhost)"
    echo "  TEST_DB_PORT        PostgreSQL port (default: 5432)"
    echo "  TEST_DB_USER        PostgreSQL user (default: balena)"
    echo "  TEST_DB_PASSWORD    PostgreSQL password (default: test_password)"
}

# Main execution
main() {
    local command="${1:-all}"
    
    case "$command" in
        "unit")
            setup_test_env
            run_unit_tests
            ;;
        "integration")
            check_postgres
            setup_test_env
            run_integration_tests
            ;;
        "handlers")
            check_postgres
            setup_test_env
            run_handler_tests
            ;;
        "templates")
            setup_test_env
            run_template_tests
            ;;
        "workflows")
            check_postgres
            setup_test_env
            run_workflow_tests
            ;;
        "errors")
            check_postgres
            setup_test_env
            run_error_tests
            ;;
        "all")
            check_postgres
            setup_test_env
            run_all_tests
            ;;
        "coverage")
            check_postgres
            setup_test_env
            run_tests_with_coverage
            ;;
        "bench")
            check_postgres
            setup_test_env
            run_benchmarks
            ;;
        "specific")
            if [ -z "$2" ]; then
                print_error "Please provide a test pattern"
                show_help
                exit 1
            fi
            check_postgres
            setup_test_env
            run_specific_test "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
