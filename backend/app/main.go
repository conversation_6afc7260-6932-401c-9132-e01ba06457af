package main

import (
	"fmt"
	"log"
	"net/http"
	"net/url"
	"os"

	"app/handlers"
	"app/middleware"

	"database/sql"

	"github.com/joho/godotenv"
	_ "github.com/lib/pq" // PostgreSQL driver
)

func main() {
	// Load environment variables
	_ = godotenv.Load()
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		log.Fatal("JWT_SECRET environment variable not set")
	}

	username := os.Getenv("APP_USERNAME")
	password := os.Getenv("APP_PASSWORD")
	if username == "" || password == "" {
		log.Fatal("APP_USERNAME and APP_PASSWORD environment variables must be set")
	}

	// Database credentials
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbName := os.Getenv("DB_NAME")

	if dbUser == "" || dbHost == "" || dbPort == "" || dbName == "" {
		log.Fatal("Database environment variables not set correctly")
	}

	// URL-encode the password to avoid issues with special characters
	encodedPassword := url.QueryEscape(dbPassword)

	// Build connection string
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		dbUser, encodedPassword, dbHost, dbPort, dbName)

	// Connect to the database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Error connecting to the database: %v", err)
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		log.Fatalf("Could not ping the database: %v", err)
	}

	// Initialize ServeMux
	mux := http.NewServeMux()

	// Static file serving
	mux.Handle("/assets/", http.StripPrefix("/assets/", http.FileServer(http.Dir("assets"))))

	// Routes
	// Apply AuthStatusMiddleware to routes that need authentication status (but don't require login)
	mux.Handle("/", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.IndexHandler)))
	mux.Handle("/form", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.FormHandler(db))))

	// Login should be accessible to everyone but we track auth status
	mux.Handle("/login", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.LoginHandler)))
	mux.Handle("/logout", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.LogoutHandler)))

	// Apply AuthMiddleware to routes that require authentication
	mux.Handle("/generate", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.GenerateHandler(db))))
	mux.Handle("/update", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.UpdateHandler(db))))
	mux.Handle("/check-form", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.CheckFormHandler(db))))
	mux.Handle("/download", middleware.AuthStatusMiddleware(http.HandlerFunc(handlers.DownloadHandler(db))))

	// Health check endpoint (no auth required)
	mux.HandleFunc("/health", handlers.HealthHandler(db))

	// Apply middleware
	loggedMux := middleware.LoggingMiddleware(mux)
	corsMux := middleware.CORSMiddleware(loggedMux)

	log.Println("Starting server on :8080")
	log.Fatal(http.ListenAndServe(":8080", corsMux))
}
