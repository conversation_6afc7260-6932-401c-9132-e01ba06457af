package testutils

import (
	"database/sql"
	"fmt"
	"log/slog"
	"os"
	"testing"
	"time"

	"app/internal/config"
	"app/internal/models"
	"app/internal/repository"
	"app/internal/services"

	_ "github.com/lib/pq"
)

// TestConfig creates a test configuration
func TestConfig() *config.Config {
	return &config.Config{
		Server: config.ServerConfig{
			Host:         "localhost",
			Port:         8080,
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  120 * time.Second,
		},
		Database: config.DatabaseConfig{
			Host:     "localhost",
			Port:     5432,
			User:     "balena",
			Password: "test_password",
			Name:     "balena_test",
			SSLMode:  "disable",
		},
		Auth: config.AuthConfig{
			JWTSecret:      "test_jwt_secret_key_for_testing_only",
			SessionTimeout: 24 * time.Hour,
			AdminUsername:  "test_admin",
			AdminPassword:  "test_password",
			CookieName:     "test_session",
			CookieSecure:   false,
			CookieHTTPOnly: true,
		},
		App: config.AppConfig{
			Environment:     "test",
			LogLevel:        "debug",
			CloudflareToken: "",
			TemplateDir:     "templates",
			StaticDir:       "assets",
			UploadDir:       "test_uploads",
			MaxUploadSize:   10 * 1024 * 1024,
		},
	}
}

// TestLogger creates a test logger
func TestLogger() *slog.Logger {
	opts := &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)
	return slog.New(handler)
}

// SetupTestDB creates a test database connection
func SetupTestDB(t *testing.T) *sql.DB {
	cfg := TestConfig()

	// Connect to postgres database first to create test database
	adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%d/postgres?sslmode=%s",
		cfg.Database.User, cfg.Database.Password, cfg.Database.Host, cfg.Database.Port, cfg.Database.SSLMode)

	adminDB, err := sql.Open("postgres", adminConnStr)
	if err != nil {
		t.Skipf("Skipping test: cannot connect to PostgreSQL: %v", err)
	}
	defer adminDB.Close()

	// Create test database
	_, err = adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", cfg.Database.Name))
	if err != nil {
		t.Logf("Warning: could not drop test database: %v", err)
	}

	_, err = adminDB.Exec(fmt.Sprintf("CREATE DATABASE %s", cfg.Database.Name))
	if err != nil {
		t.Skipf("Skipping test: cannot create test database: %v", err)
	}

	// Connect to test database
	testDB, err := sql.Open("postgres", cfg.GetDatabaseURL())
	if err != nil {
		t.Fatalf("Failed to connect to test database: %v", err)
	}

	// Create tables
	if err := createTestTables(testDB); err != nil {
		t.Fatalf("Failed to create test tables: %v", err)
	}

	return testDB
}

// TeardownTestDB cleans up the test database
func TeardownTestDB(t *testing.T, db *sql.DB) {
	if db != nil {
		db.Close()
	}

	cfg := TestConfig()
	adminConnStr := fmt.Sprintf("postgres://%s:%s@%s:%d/postgres?sslmode=%s",
		cfg.Database.User, cfg.Database.Password, cfg.Database.Host, cfg.Database.Port, cfg.Database.SSLMode)

	adminDB, err := sql.Open("postgres", adminConnStr)
	if err != nil {
		t.Logf("Warning: could not connect to clean up test database: %v", err)
		return
	}
	defer adminDB.Close()

	_, err = adminDB.Exec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", cfg.Database.Name))
	if err != nil {
		t.Logf("Warning: could not drop test database: %v", err)
	}
}

// createTestTables creates the necessary tables for testing
func createTestTables(db *sql.DB) error {
	schema := `
	CREATE TABLE students (
		id SERIAL PRIMARY KEY,
		name VARCHAR(100) NOT NULL,
		email VARCHAR(100) UNIQUE NOT NULL,
		date_of_birth DATE NOT NULL,
		gender CHAR(1) NOT NULL CHECK (gender IN ('M', 'F')),
		category VARCHAR(10) NOT NULL CHECK (
			category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')
		),
		mobile_number VARCHAR(10) NOT NULL,
		college VARCHAR(100) NOT NULL DEFAULT 'SSV College',
		session VARCHAR(10) NOT NULL,
		subject VARCHAR(50) NOT NULL,
		class_roll_number VARCHAR(4) NOT NULL,
		university_roll_number VARCHAR(10) NOT NULL,
		registration_number VARCHAR(20) NOT NULL,
		father_name VARCHAR(100) NOT NULL,
		mother_name VARCHAR(100) NOT NULL,
		pincode VARCHAR(6) NOT NULL,
		state VARCHAR(20) NOT NULL CHECK (state IN ('BIHAR', 'OTHER')),
		aadhar_number VARCHAR(12) NOT NULL UNIQUE,
		abc_id VARCHAR(12) NOT NULL UNIQUE,
		aadhar_mobile VARCHAR(10) NOT NULL,
		created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
	);

	CREATE INDEX idx_students_email ON students (email);
	CREATE INDEX idx_students_aadhar ON students (aadhar_number);
	CREATE INDEX idx_students_registration ON students (registration_number);

	CREATE TABLE exam_form_degree_iii (
		id SERIAL PRIMARY KEY,
		roll_number VARCHAR(10) NOT NULL UNIQUE CHECK (LENGTH (roll_number) BETWEEN 5 AND 10),
		category VARCHAR(10) NOT NULL CHECK (
			category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')
		),
		is_regular BOOLEAN NOT NULL DEFAULT TRUE,
		paper_i BOOLEAN NOT NULL DEFAULT FALSE,
		paper_ii BOOLEAN NOT NULL DEFAULT FALSE,
		paper_iii BOOLEAN NOT NULL DEFAULT FALSE,
		paper_iv BOOLEAN NOT NULL DEFAULT FALSE,
		fee DECIMAL(10, 2) NOT NULL
	);
	`

	_, err := db.Exec(schema)
	return err
}

// SetupTestServices creates test services with dependencies
func SetupTestServices(t *testing.T) (services.ServiceManager, *sql.DB) {
	db := SetupTestDB(t)
	cfg := TestConfig()
	logger := TestLogger()

	repo := repository.NewRepository(db)
	deps := &services.Dependencies{
		Config:     cfg,
		Repository: repo,
		Logger:     logger,
	}

	svc := services.NewServiceManager(deps)
	return svc, db
}

// CleanupTestServices cleans up test services
func CleanupTestServices(t *testing.T, db *sql.DB) {
	TeardownTestDB(t, db)
}

// AssertNoError fails the test if err is not nil
func AssertNoError(t *testing.T, err error) {
	t.Helper()
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
}

// AssertError fails the test if err is nil
func AssertError(t *testing.T, err error) {
	t.Helper()
	if err == nil {
		t.Fatal("Expected an error, got nil")
	}
}

// AssertEqual fails the test if expected != actual
func AssertEqual(t *testing.T, expected, actual interface{}) {
	t.Helper()
	if expected != actual {
		t.Fatalf("Expected %v, got %v", expected, actual)
	}
}

// AssertNotEqual fails the test if expected == actual
func AssertNotEqual(t *testing.T, expected, actual interface{}) {
	t.Helper()
	if expected == actual {
		t.Fatalf("Expected %v to not equal %v", expected, actual)
	}
}

// AssertContains fails the test if str does not contain substr
func AssertContains(t *testing.T, str, substr string) {
	t.Helper()
	if !contains(str, substr) {
		t.Fatalf("Expected %q to contain %q", str, substr)
	}
}

// contains checks if a string contains a substring
func contains(str, substr string) bool {
	return len(str) >= len(substr) && (str == substr || len(substr) == 0 ||
		(len(substr) <= len(str) && str[:len(substr)] == substr) ||
		(len(str) > len(substr) && contains(str[1:], substr)))
}

// CreateTestStudent creates a test student for testing
func CreateTestStudent() *models.CreateStudentRequest {
	return &models.CreateStudentRequest{
		Name:                 "Test Student",
		Email:                "<EMAIL>",
		DateOfBirth:          "1995-01-01",
		Gender:               "M",
		Category:             "UR",
		MobileNumber:         "9876543210",
		College:              "SSV College",
		Session:              "2023-24",
		Subject:              "Computer Science",
		ClassRollNumber:      "001",
		UniversityRollNumber: "2023001",
		RegistrationNumber:   "REG2023001",
		FatherName:           "Test Father",
		MotherName:           "Test Mother",
		Pincode:              "123456",
		State:                "BIHAR",
		AadharNumber:         "123456789012",
		AbcID:                "ABC123456789",
		AadharMobile:         "9876543210",
	}
}

// CreateTestExamForm creates a test exam form for testing
func CreateTestExamForm() *models.CreateExamFormRequest {
	return &models.CreateExamFormRequest{
		RollNumber: "2023001",
		Category:   "UR",
		ExamType:   "Regular",
		PaperI:     true,
		PaperII:    true,
		PaperIII:   false,
		PaperIV:    false,
	}
}

// ParseDate parses a date string into time.Time for testing
func ParseDate(dateStr string) time.Time {
	t, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		panic(err)
	}
	return t
}
