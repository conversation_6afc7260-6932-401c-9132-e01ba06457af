package handlers_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"app/data"
	"app/handlers"
	"app/middleware"
	"app/tests/testutils"
)

func TestUpdateHandler(t *testing.T) {
	_, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	// Create a test student first
	queries := data.New(db)
	ctx := context.Background()

	studentParams := data.CreateStudentParams{
		Name:                 "Test Student",
		Email:                "<EMAIL>",
		DateOfBirth:          testutils.ParseDate("1995-01-01"),
		Gender:               "M",
		Category:             "UR",
		MobileNumber:         "9876543210",
		Session:              "2023-24",
		Subject:              "Computer Science",
		ClassRollNumber:      "001",
		UniversityRollNumber: "2023001",
		RegistrationNumber:   "REG2023001",
		FatherName:           "Test Father",
		MotherName:           "Test Mother",
		Pincode:              "123456",
		State:                "BIHAR",
		AadharNumber:         "123456789012",
		AbcID:                "ABC123456789",
		AadharMobile:         "9876543210",
	}

	student, err := queries.CreateStudent(ctx, studentParams)
	testutils.AssertNoError(t, err)

	tests := []struct {
		name           string
		method         string
		authenticated  bool
		formData       url.Values
		expectedStatus int
		expectedBody   string
		checkRedirect  bool
	}{
		{
			name:           "GET update page - unauthenticated (redirect)",
			method:         http.MethodGet,
			authenticated:  false,
			expectedStatus: http.StatusSeeOther,
			checkRedirect:  true,
		},
		{
			name:           "GET update page - authenticated",
			method:         http.MethodGet,
			authenticated:  true,
			expectedStatus: http.StatusOK,
			expectedBody:   "Search Student",
		},
		{
			name:          "POST search by university roll - found",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"exam_roll"},
				"search_value": {"2023001"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Update Student Record",
		},
		{
			name:          "POST search by ABC ID - found",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"abc_id"},
				"search_value": {"ABC123456789"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Update Student Record",
		},
		{
			name:          "POST search by Aadhaar - found",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"aadhaar"},
				"search_value": {"123456789012"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Update Student Record",
		},
		{
			name:          "POST search - not found",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"exam_roll"},
				"search_value": {"9999999"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Student not found",
		},
		{
			name:          "POST search - invalid search type",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"invalid"},
				"search_value": {"123"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Invalid search type",
		},
		{
			name:          "POST search - missing search value",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"search_type":  {"exam_roll"},
				"search_value": {""},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Please select a search type and enter a value",
		},
		{
			name:          "POST update student",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"update":                 {"true"},
				"id":                     {string(rune(student.ID))},
				"name":                   {"Updated Student Name"},
				"email":                  {"<EMAIL>"},
				"date_of_birth":          {"1995-01-01"},
				"gender":                 {"M"},
				"category":               {"UR"},
				"mobile_number":          {"9876543210"},
				"session":                {"2023-24"},
				"subject":                {"Updated Computer Science"},
				"class_roll_number":      {"001"},
				"university_roll_number": {"2023001"},
				"registration_number":    {"REG2023001"},
				"father_name":            {"Test Father"},
				"mother_name":            {"Test Mother"},
				"pincode":                {"123456"},
				"state":                  {"BIHAR"},
				"aadhar_number":          {"123456789012"},
				"abc_id":                 {"ABC123456789"},
				"aadhar_mobile":          {"9876543210"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Student record updated successfully",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req *http.Request

			if tt.method == http.MethodPost && tt.formData != nil {
				req = httptest.NewRequest(tt.method, "/update", strings.NewReader(tt.formData.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else {
				req = httptest.NewRequest(tt.method, "/update", nil)
			}

			// Set authentication context
			ctx := context.WithValue(req.Context(), middleware.AuthenticatedKey, tt.authenticated)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			handler := handlers.UpdateHandler(db)
			handler(rr, req)

			testutils.AssertEqual(t, tt.expectedStatus, rr.Code)

			if tt.expectedBody != "" {
				body := rr.Body.String()
				testutils.AssertContains(t, body, tt.expectedBody)
			}

			if tt.checkRedirect {
				location := rr.Header().Get("Location")
				testutils.AssertEqual(t, "/login", location)
			}
		})
	}
}
