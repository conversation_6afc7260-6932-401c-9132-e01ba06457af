package handlers_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"app/data"
	"app/handlers"
	"app/middleware"
	"app/tests/testutils"
)

func TestGenerateHandler(t *testing.T) {
	_, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	// Create a test student first
	queries := data.New(db)
	ctx := context.Background()

	studentParams := data.CreateStudentParams{
		Name:                 "Test Student",
		Email:                "<EMAIL>",
		DateOfBirth:          testutils.ParseDate("1995-01-01"),
		Gender:               "M",
		Category:             "UR",
		MobileNumber:         "9876543210",
		Session:              "2023-24",
		Subject:              "Computer Science",
		ClassRollNumber:      "001",
		UniversityRollNumber: "2023001",
		RegistrationNumber:   "REG2023001",
		FatherName:           "Test Father",
		MotherName:           "Test Mother",
		Pincode:              "123456",
		State:                "BIHAR",
		AadharNumber:         "123456789012",
		AbcID:                "ABC123456789",
		AadharMobile:         "9876543210",
	}

	_, err := queries.CreateStudent(ctx, studentParams)
	testutils.AssertNoError(t, err)

	tests := []struct {
		name           string
		method         string
		authenticated  bool
		formData       url.Values
		expectedStatus int
		expectedBody   string
		checkRedirect  bool
	}{
		{
			name:           "GET generate page - unauthenticated (redirect)",
			method:         http.MethodGet,
			authenticated:  false,
			expectedStatus: http.StatusSeeOther,
			checkRedirect:  true,
		},
		{
			name:           "GET generate page - authenticated",
			method:         http.MethodGet,
			authenticated:  true,
			expectedStatus: http.StatusOK,
			expectedBody:   "Download Exam Form",
		},
		{
			name:          "POST generate form - valid roll number",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"roll_number": {"2023001"},
				"category":    {"UR"},
				"exam_type":   {"regular"},
				"paper_1":     {"on"},
				"paper_2":     {"on"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Form generated successfully",
		},
		{
			name:          "POST generate form - invalid roll number",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"roll_number": {"9999999"},
				"category":    {"UR"},
				"exam_type":   {"regular"},
				"paper_1":     {"on"},
			},
			expectedStatus: http.StatusNotFound,
		},
		{
			name:          "POST generate form - missing roll number",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"category":  {"UR"},
				"exam_type": {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:          "POST generate form - invalid category",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"roll_number": {"2023001"},
				"category":    {"INVALID"},
				"exam_type":   {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:          "POST generate form - no papers selected",
			method:        http.MethodPost,
			authenticated: true,
			formData: url.Values{
				"roll_number": {"2023001"},
				"category":    {"UR"},
				"exam_type":   {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req *http.Request

			if tt.method == http.MethodPost && tt.formData != nil {
				req = httptest.NewRequest(tt.method, "/generate", strings.NewReader(tt.formData.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else {
				req = httptest.NewRequest(tt.method, "/generate", nil)
			}

			// Set authentication context
			ctx := context.WithValue(req.Context(), middleware.AuthenticatedKey, tt.authenticated)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			handler := handlers.GenerateHandler(db)
			handler(rr, req)

			testutils.AssertEqual(t, tt.expectedStatus, rr.Code)

			if tt.expectedBody != "" {
				body := rr.Body.String()
				testutils.AssertContains(t, body, tt.expectedBody)
			}

			if tt.checkRedirect {
				location := rr.Header().Get("Location")
				testutils.AssertEqual(t, "/login", location)
			}
		})
	}
}

func TestDownloadHandler(t *testing.T) {
	_, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	tests := []struct {
		name           string
		method         string
		authenticated  bool
		queryParams    string
		expectedStatus int
		checkRedirect  bool
	}{
		{
			name:           "GET download - unauthenticated (redirect)",
			method:         http.MethodGet,
			authenticated:  false,
			expectedStatus: http.StatusSeeOther,
			checkRedirect:  true,
		},
		{
			name:           "GET download - missing roll number",
			method:         http.MethodGet,
			authenticated:  true,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "GET download - invalid roll number",
			method:         http.MethodGet,
			authenticated:  true,
			queryParams:    "?roll=9999999",
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url := "/download" + tt.queryParams
			req := httptest.NewRequest(tt.method, url, nil)

			// Set authentication context
			ctx := context.WithValue(req.Context(), middleware.AuthenticatedKey, tt.authenticated)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			handler := handlers.DownloadHandler(db)
			handler(rr, req)

			testutils.AssertEqual(t, tt.expectedStatus, rr.Code)

			if tt.checkRedirect {
				location := rr.Header().Get("Location")
				testutils.AssertEqual(t, "/login", location)
			}
		})
	}
}
