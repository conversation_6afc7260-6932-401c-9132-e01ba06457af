package templates_test

import (
	"bytes"
	"html/template"
	"os"
	"path/filepath"
	"testing"
	"time"

	"app/data"
	"app/tests/testutils"
)

// getTemplatePath returns the correct path to templates
func getTemplatePath(filename string) string {
	// Get current working directory for debugging
	wd, _ := os.Getwd()

	// Check if templates directory exists in current directory
	templateDir := filepath.Join(wd, "templates")
	if _, err := os.Stat(templateDir); err == nil {
		return filepath.Join("templates", filename)
	}

	// Check if we're in a subdirectory and need to go up
	appTemplateDir := filepath.Join(wd, "..", "..", "templates")
	if _, err := os.Stat(appTemplateDir); err == nil {
		return filepath.Join("..", "..", "templates", filename)
	}

	// Fallback - try relative to current directory
	return filepath.Join("templates", filename)
}

// TestAllTemplatesParsing tests that all templates can be parsed without errors
func TestAllTemplatesParsing(t *testing.T) {
	templates := []struct {
		name      string
		templates []string
	}{
		{
			name:      "index template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("index.html")},
		},
		{
			name:      "login template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("login.html")},
		},
		{
			name:      "form template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("form.html")},
		},
		{
			name:      "generate template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("generate.html")},
		},
		{
			name:      "update_form template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("update_form.html")},
		},
		{
			name:      "update_form_simple template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("update_form_simple.html")},
		},
		{
			name:      "search_form template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("search_form.html")},
		},
		// Note: exam_form and admin templates require custom functions and are tested separately
		{
			name:      "message template",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("message.html")},
		},
	}

	for _, tt := range templates {
		t.Run(tt.name, func(t *testing.T) {
			_, err := template.ParseFiles(tt.templates...)
			if err != nil {
				t.Errorf("Failed to parse templates %v: %v", tt.templates, err)
			}
		})
	}
}

// TestTemplateExecution tests that templates can be executed with data
func TestTemplateExecution(t *testing.T) {
	tests := []struct {
		name         string
		templates    []string
		data         interface{}
		checkContent string
	}{
		{
			name:      "index template with data",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("index.html")},
			data: struct {
				Title           string
				IsAuthenticated bool
			}{
				Title:           "Test Index",
				IsAuthenticated: false,
			},
			checkContent: "Test Index",
		},
		{
			name:      "login template with error",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("login.html")},
			data: struct {
				Title           string
				IsAuthenticated bool
				Error           string
			}{
				Title:           "Login",
				IsAuthenticated: false,
				Error:           "Invalid credentials",
			},
			checkContent: "Invalid credentials",
		},
		{
			name:      "form template with success",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("form.html")},
			data: struct {
				Title           string
				IsAuthenticated bool
				Success         string
				Error           string
			}{
				Title:           "Student Registration Form",
				IsAuthenticated: false,
				Success:         "Student registered successfully",
				Error:           "",
			},
			checkContent: "Student registered successfully",
		},
		{
			name:      "update form with student data",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("update_form.html")},
			data: struct {
				Title           string
				IsAuthenticated bool
				Student         *data.Student
				Error           string
				Success         string
				SearchType      string
				SearchValue     string
			}{
				Title:           "Update Student Record",
				IsAuthenticated: true,
				Student: &data.Student{
					ID:                   1,
					Name:                 "Test Student",
					Email:                "<EMAIL>",
					DateOfBirth:          time.Date(1995, 1, 1, 0, 0, 0, 0, time.UTC),
					Gender:               "M",
					Category:             "UR",
					MobileNumber:         "9876543210",
					College:              "SSV College",
					Session:              "2023-24",
					Subject:              "Computer Science",
					ClassRollNumber:      "001",
					UniversityRollNumber: "2023001",
					RegistrationNumber:   "REG2023001",
					FatherName:           "Test Father",
					MotherName:           "Test Mother",
					Pincode:              "123456",
					State:                "BIHAR",
					AadharNumber:         "123456789012",
					AbcID:                "ABC123456789",
					AadharMobile:         "9876543210",
				},
				Error:       "",
				Success:     "",
				SearchType:  "exam_roll",
				SearchValue: "2023001",
			},
			checkContent: "Test Student",
		},
		{
			name:      "generate template authenticated",
			templates: []string{getTemplatePath("base.html"), getTemplatePath("generate.html")},
			data: struct {
				Title           string
				IsAuthenticated bool
				Error           string
				Success         string
				Roll            string
			}{
				Title:           "Download Exam Form",
				IsAuthenticated: true,
				Error:           "",
				Success:         "",
				Roll:            "",
			},
			checkContent: "Download Exam Form",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, err := template.ParseFiles(tt.templates...)
			if err != nil {
				t.Fatalf("Failed to parse templates: %v", err)
			}

			var buf bytes.Buffer
			err = tmpl.ExecuteTemplate(&buf, "base.html", tt.data)
			if err != nil {
				t.Fatalf("Failed to execute template: %v", err)
			}

			content := buf.String()
			if content == "" {
				t.Error("Template execution produced no output")
			}

			if tt.checkContent != "" {
				testutils.AssertContains(t, content, tt.checkContent)
			}
		})
	}
}

// TestTemplateErrorHandling tests template behavior with invalid data
func TestTemplateErrorHandling(t *testing.T) {
	tests := []struct {
		name        string
		templates   []string
		data        interface{}
		expectError bool
	}{
		{
			name:        "nil data should not cause panic",
			templates:   []string{getTemplatePath("base.html"), getTemplatePath("index.html")},
			data:        nil,
			expectError: false,
		},
		{
			name:        "empty struct should work for parsing",
			templates:   []string{getTemplatePath("base.html"), getTemplatePath("login.html")},
			data:        struct{}{},
			expectError: false,
		},
		{
			name:        "missing template file should error",
			templates:   []string{getTemplatePath("base.html"), getTemplatePath("nonexistent.html")},
			data:        struct{}{},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tmpl, err := template.ParseFiles(tt.templates...)

			if tt.expectError {
				testutils.AssertError(t, err)
				return
			}

			testutils.AssertNoError(t, err)

			var buf bytes.Buffer
			err = tmpl.ExecuteTemplate(&buf, "base.html", tt.data)
			// For empty struct, execution might fail due to missing fields, which is expected
			if tt.name == "empty struct should work for parsing" {
				// We only care that parsing worked, execution failure is expected
				return
			}
			testutils.AssertNoError(t, err)
		})
	}
}
