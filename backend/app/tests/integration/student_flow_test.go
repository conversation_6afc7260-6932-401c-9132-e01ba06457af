package integration_test

import (
	"context"
	"fmt"
	"testing"

	"app/internal/models"
	"app/tests/testutils"
)

func TestStudentCompleteFlow(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("complete student lifecycle", func(t *testing.T) {
		// 1. Create a student
		req := testutils.CreateTestStudent()
		req.Email = "<EMAIL>"
		req.AadharNumber = "123456789999"
		req.AbcID = "ABC123456999"

		student, err := svc.Student().CreateStudent(ctx, req)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, int32(0), student.ID)
		testutils.AssertEqual(t, req.Name, student.Name)
		testutils.AssertEqual(t, req.Email, student.Email)

		// 2. Retrieve the student by different methods
		retrievedByID, err := svc.Student().GetStudentByID(ctx, student.ID)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, student.ID, retrievedByID.ID)

		retrievedByEmail, err := svc.Student().GetStudentByEmail(ctx, student.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, student.ID, retrievedByEmail.ID)

		// 3. Search for the student
		searchReq := &models.StudentSearchRequest{
			SearchType:  "email",
			SearchValue: student.Email,
		}
		searchResult, err := svc.Student().SearchStudent(ctx, searchReq)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, student.ID, searchResult.ID)

		// 4. Update the student
		updateReq := &models.UpdateStudentRequest{
			ID:      student.ID,
			Name:    "Updated Integration Student",
			Subject: "Updated Computer Science",
		}
		updated, err := svc.Student().UpdateStudent(ctx, updateReq)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, "Updated Integration Student", updated.Name)
		testutils.AssertEqual(t, "Updated Computer Science", updated.Subject)
		testutils.AssertEqual(t, student.Email, updated.Email) // Should remain unchanged

		// 5. Verify the update persisted
		retrieved, err := svc.Student().GetStudentByID(ctx, student.ID)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, "Updated Integration Student", retrieved.Name)
		testutils.AssertEqual(t, "Updated Computer Science", retrieved.Subject)

		// 6. Create an exam form for the student
		examFormReq := &models.CreateExamFormRequest{
			RollNumber: student.UniversityRollNumber,
			Category:   student.Category,
			ExamType:   "Regular",
			PaperI:     true,
			PaperII:    true,
			PaperIII:   false,
			PaperIV:    false,
		}

		examForm, err := svc.ExamForm().CreateExamForm(ctx, examFormReq)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, student.UniversityRollNumber, examForm.RollNumber)
		testutils.AssertEqual(t, student.Category, examForm.Category)
		testutils.AssertEqual(t, true, examForm.IsRegular)
		testutils.AssertEqual(t, true, examForm.PaperI)
		testutils.AssertEqual(t, true, examForm.PaperII)

		// 7. Generate exam form with student data
		examFormWithStudent, err := svc.ExamForm().GenerateExamFormWithStudent(ctx, student.UniversityRollNumber)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, examForm.ID, examFormWithStudent.ExamForm.ID)
		testutils.AssertEqual(t, student.ID, examFormWithStudent.Student.ID)

		// 8. Validate student exists
		exists, err := svc.Student().ValidateStudentExists(ctx, "email", student.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, exists)

		// 9. Delete the exam form
		err = svc.ExamForm().DeleteExamFormByRollNumber(ctx, student.UniversityRollNumber)
		testutils.AssertNoError(t, err)

		// 10. Verify exam form is deleted
		_, err = svc.ExamForm().GetExamFormByRollNumber(ctx, student.UniversityRollNumber)
		testutils.AssertError(t, err)

		// 11. Delete the student
		err = svc.Student().DeleteStudent(ctx, student.ID)
		testutils.AssertNoError(t, err)

		// 12. Verify student is deleted
		_, err = svc.Student().GetStudentByID(ctx, student.ID)
		testutils.AssertError(t, err)

		// 13. Verify student no longer exists
		exists, err = svc.Student().ValidateStudentExists(ctx, "email", student.Email)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, exists)
	})
}

func TestMultipleStudentsFlow(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("multiple students management", func(t *testing.T) {
		// Create multiple students
		students := make([]*models.Student, 5)
		for i := 0; i < 5; i++ {
			req := testutils.CreateTestStudent()
			req.Email = fmt.Sprintf("<EMAIL>", i)
			req.AadharNumber = fmt.Sprintf("12345678900%d", i)
			req.AbcID = fmt.Sprintf("ABC12345600%d", i)
			req.UniversityRollNumber = fmt.Sprintf("202300%d", i)

			student, err := svc.Student().CreateStudent(ctx, req)
			testutils.AssertNoError(t, err)
			students[i] = student
		}

		// Get all students with pagination
		allStudents, total, err := svc.Student().GetAllStudents(ctx, 1, 10)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 5, len(allStudents))
		testutils.AssertEqual(t, int64(5), total)

		// Test pagination
		page1, total1, err := svc.Student().GetAllStudents(ctx, 1, 3)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 3, len(page1))
		testutils.AssertEqual(t, int64(5), total1)

		page2, total2, err := svc.Student().GetAllStudents(ctx, 2, 3)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 2, len(page2))
		testutils.AssertEqual(t, int64(5), total2)

		// Create exam forms for all students
		for i, student := range students {
			examFormReq := &models.CreateExamFormRequest{
				RollNumber: student.UniversityRollNumber,
				Category:   student.Category,
				ExamType:   "Regular",
				PaperI:     i%2 == 0, // Alternate papers
				PaperII:    i%2 == 1,
				PaperIII:   i%3 == 0,
				PaperIV:    i%4 == 0,
			}

			_, err := svc.ExamForm().CreateExamForm(ctx, examFormReq)
			testutils.AssertNoError(t, err)
		}

		// Get all exam forms
		allExamForms, totalForms, err := svc.ExamForm().GetAllExamForms(ctx, 1, 10)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 5, len(allExamForms))
		testutils.AssertEqual(t, int64(5), totalForms)

		// Search exam forms by category
		categoryForms, err := svc.ExamForm().GetExamFormsByCategory(ctx, "UR")
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 5, len(categoryForms)) // All test students have UR category

		// Clean up - delete all exam forms and students
		for _, student := range students {
			err := svc.ExamForm().DeleteExamFormByRollNumber(ctx, student.UniversityRollNumber)
			testutils.AssertNoError(t, err)

			err = svc.Student().DeleteStudent(ctx, student.ID)
			testutils.AssertNoError(t, err)
		}

		// Verify cleanup
		finalStudents, finalTotal, err := svc.Student().GetAllStudents(ctx, 1, 10)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 0, len(finalStudents))
		testutils.AssertEqual(t, int64(0), finalTotal)

		finalForms, finalFormsTotal, err := svc.ExamForm().GetAllExamForms(ctx, 1, 10)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, 0, len(finalForms))
		testutils.AssertEqual(t, int64(0), finalFormsTotal)
	})
}

func TestAuthenticationFlow(t *testing.T) {
	svc, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	ctx := context.Background()

	t.Run("authentication lifecycle", func(t *testing.T) {
		cfg := testutils.TestConfig()

		// 1. Login with valid credentials
		token, err := svc.Auth().Login(ctx, cfg.Auth.AdminUsername, cfg.Auth.AdminPassword)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", token)

		// 2. Validate session
		valid, err := svc.Auth().ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)

		// 3. Get session user
		username, err := svc.Auth().GetSessionUser(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, cfg.Auth.AdminUsername, username)

		// 4. Refresh session
		newToken, err := svc.Auth().RefreshSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertNotEqual(t, "", newToken)
		testutils.AssertNotEqual(t, token, newToken)

		// 5. Old token should be invalid
		valid, err = svc.Auth().ValidateSession(ctx, token)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)

		// 6. New token should be valid
		valid, err = svc.Auth().ValidateSession(ctx, newToken)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, true, valid)

		// 7. Logout
		err = svc.Auth().Logout(ctx, newToken)
		testutils.AssertNoError(t, err)

		// 8. Token should be invalid after logout
		valid, err = svc.Auth().ValidateSession(ctx, newToken)
		testutils.AssertNoError(t, err)
		testutils.AssertEqual(t, false, valid)
	})
}
