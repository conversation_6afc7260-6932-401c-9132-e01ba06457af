package errors_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"net/url"
	"strings"
	"testing"

	"app/handlers"
	"app/middleware"
	"app/tests/testutils"
)

// TestErrorHandling tests various error scenarios
func TestErrorHandling(t *testing.T) {
	_, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	tests := []struct {
		name           string
		handler        func() http.HandlerFunc
		method         string
		path           string
		authenticated  bool
		formData       url.Values
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "invalid method on index",
			handler: func() http.HandlerFunc {
				return handlers.IndexHandler
			},
			method:         http.MethodPost,
			path:           "/",
			authenticated:  false,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name: "invalid method on form",
			handler: func() http.HandlerFunc {
				return handlers.FormHandler(db)
			},
			method:         http.MethodPut,
			path:           "/form",
			authenticated:  false,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name: "invalid method on update",
			handler: func() http.HandlerFunc {
				return handlers.UpdateHandler(db)
			},
			method:         http.MethodPatch,
			path:           "/update",
			authenticated:  true,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name: "invalid method on generate",
			handler: func() http.HandlerFunc {
				return handlers.GenerateHandler(db)
			},
			method:         http.MethodDelete,
			path:           "/generate",
			authenticated:  true,
			expectedStatus: http.StatusMethodNotAllowed,
		},
		{
			name: "form submission with missing required fields",
			handler: func() http.HandlerFunc {
				return handlers.FormHandler(db)
			},
			method:        http.MethodPost,
			path:          "/form",
			authenticated: false,
			formData: url.Values{
				"name":  {"Test Student"},
				"email": {"<EMAIL>"},
				// Missing other required fields
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "error",
		},
		{
			name: "form submission with invalid email",
			handler: func() http.HandlerFunc {
				return handlers.FormHandler(db)
			},
			method:        http.MethodPost,
			path:          "/form",
			authenticated: false,
			formData: url.Values{
				"name":                   {"Test Student"},
				"email":                  {"invalid-email"},
				"date_of_birth":          {"1995-01-01"},
				"gender":                 {"M"},
				"category":               {"UR"},
				"mobile_number":          {"9876543210"},
				"session":                {"2023-24"},
				"subject":                {"Computer Science"},
				"class_roll_number":      {"001"},
				"university_roll_number": {"2023001"},
				"registration_number":    {"REG2023001"},
				"father_name":            {"Test Father"},
				"mother_name":            {"Test Mother"},
				"pincode":                {"123456"},
				"state":                  {"BIHAR"},
				"aadhar_number":          {"123456789012"},
				"abc_id":                 {"ABC123456789"},
				"aadhar_mobile":          {"9876543210"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "error",
		},
		{
			name: "update search with empty search value",
			handler: func() http.HandlerFunc {
				return handlers.UpdateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/update",
			authenticated: true,
			formData: url.Values{
				"search_type":  {"exam_roll"},
				"search_value": {""},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Please select a search type and enter a value",
		},
		{
			name: "update search with invalid search type",
			handler: func() http.HandlerFunc {
				return handlers.UpdateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/update",
			authenticated: true,
			formData: url.Values{
				"search_type":  {"invalid_type"},
				"search_value": {"123"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Invalid search type",
		},
		{
			name: "update search for non-existent student",
			handler: func() http.HandlerFunc {
				return handlers.UpdateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/update",
			authenticated: true,
			formData: url.Values{
				"search_type":  {"exam_roll"},
				"search_value": {"9999999"},
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "Student not found",
		},
		{
			name: "generate form with missing roll number",
			handler: func() http.HandlerFunc {
				return handlers.GenerateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/generate",
			authenticated: true,
			formData: url.Values{
				"category":  {"UR"},
				"exam_type": {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "generate form with invalid category",
			handler: func() http.HandlerFunc {
				return handlers.GenerateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/generate",
			authenticated: true,
			formData: url.Values{
				"roll_number": {"2023001"},
				"category":    {"INVALID"},
				"exam_type":   {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "generate form with no papers selected",
			handler: func() http.HandlerFunc {
				return handlers.GenerateHandler(db)
			},
			method:        http.MethodPost,
			path:          "/generate",
			authenticated: true,
			formData: url.Values{
				"roll_number": {"2023001"},
				"category":    {"UR"},
				"exam_type":   {"regular"},
			},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "download with missing roll parameter",
			handler: func() http.HandlerFunc {
				return handlers.DownloadHandler(db)
			},
			method:         http.MethodGet,
			path:           "/download",
			authenticated:  true,
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "download with non-existent roll number",
			handler: func() http.HandlerFunc {
				return handlers.DownloadHandler(db)
			},
			method:         http.MethodGet,
			path:           "/download?roll=9999999",
			authenticated:  true,
			expectedStatus: http.StatusNotFound,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var req *http.Request

			if tt.method == http.MethodPost && tt.formData != nil {
				req = httptest.NewRequest(tt.method, tt.path, strings.NewReader(tt.formData.Encode()))
				req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			} else {
				req = httptest.NewRequest(tt.method, tt.path, nil)
			}

			// Set authentication context
			ctx := context.WithValue(req.Context(), middleware.AuthenticatedKey, tt.authenticated)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			handler := tt.handler()
			handler(rr, req)

			testutils.AssertEqual(t, tt.expectedStatus, rr.Code)

			if tt.expectedBody != "" {
				body := rr.Body.String()
				testutils.AssertContains(t, body, tt.expectedBody)
			}
		})
	}
}

// TestAuthenticationErrors tests authentication-related errors
func TestAuthenticationErrors(t *testing.T) {
	_, db := testutils.SetupTestServices(t)
	defer testutils.CleanupTestServices(t, db)

	// Set wrong credentials
	t.Setenv("APP_USERNAME", "admin")
	t.Setenv("APP_PASSWORD", "secret")

	tests := []struct {
		name           string
		username       string
		password       string
		expectedStatus int
	}{
		{
			name:           "empty username",
			username:       "",
			password:       "secret",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "empty password",
			username:       "admin",
			password:       "",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "wrong username",
			username:       "wronguser",
			password:       "secret",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "wrong password",
			username:       "admin",
			password:       "wrongpass",
			expectedStatus: http.StatusUnauthorized,
		},
		{
			name:           "correct credentials",
			username:       "admin",
			password:       "secret",
			expectedStatus: http.StatusSeeOther,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			formData := url.Values{
				"username": {tt.username},
				"password": {tt.password},
			}

			req := httptest.NewRequest(http.MethodPost, "/login", strings.NewReader(formData.Encode()))
			req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
			ctx := context.WithValue(req.Context(), middleware.AuthenticatedKey, false)
			req = req.WithContext(ctx)

			rr := httptest.NewRecorder()

			handlers.LoginHandler(rr, req)

			testutils.AssertEqual(t, tt.expectedStatus, rr.Code)
		})
	}
}
