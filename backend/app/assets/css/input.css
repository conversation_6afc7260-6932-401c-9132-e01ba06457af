@tailwind base;
@tailwind components;
@tailwind utilities;

.course-content h1 {
    @apply text-4xl; /* Adjust the size as needed */
    padding-top: 1rem; /* Adjust the padding as needed */
    padding-right: 1rem;
    padding-bottom: 1rem;
}

.course-content ul,
.course-content ol {
    padding-left: 1rem; /* Simplified indent for list items */
    margin-bottom: 1rem; /* Simplified space between lists and other content */
}

.course-content li {
    @apply text-base leading-normal; /* Simplified text size and line height */
    margin-bottom: 0.5rem; /* Simplified space between list items */
    padding: 0.25rem 0; /* Simplified padding for list items */
    border-bottom: none; /* Removed border for a cleaner look */
}

.course-content li:last-child {
    margin-bottom: 0; /* Remove space below the last list item */
}

.course-content ol {
    @apply list-decimal; /* Use decimal numbers for ordered lists */
}

.course-content ul {
    @apply list-disc; /* Use disc bullets for unordered lists */
}

/* New styles for nested lists */
.course-content ul ul,
.course-content ol ol,
.course-content ul ol,
.course-content ol ul {
    padding-left: 1.5rem; /* Increase indent for nested lists */
    margin-bottom: 0.5rem; /* Adjust space between nested lists and other content */
}

.course-content ul ul li,
.course-content ol ol li,
.course-content ul ol li,
.course-content ol ul li {
    @apply text-sm leading-tight; /* Adjust text size and line height for nested list items */
    margin-bottom: 0.25rem; /* Adjust space between nested list items */
    padding: 0.125rem 0; /* Adjust padding for nested list items */
}

/* Styles for tables */
.course-content table {
    width: 100%; /* Make table full width */
    border-collapse: collapse; /* Remove space between table cells */
    margin-bottom: 1rem; /* Space below the table */
    table-layout: fixed; /* Ensure table columns have fixed width */
}

.course-content th,
.course-content td {
    border: 1px solid #ddd; /* Add border to table cells */
    padding: 0.5rem; /* Add padding to table cells */
    text-align: center; /* Center align text in table cells */
    word-wrap: break-word; /* Ensure long words break to fit in cells */
}

.course-content th {
    @apply bg-gray-200; /* Background color for table headers */
    font-weight: bold; /* Bold text for table headers */
}

.course-content tr:nth-child(even) {
    @apply bg-gray-100; /* Background color for even rows */
}
