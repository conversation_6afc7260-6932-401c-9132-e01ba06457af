package common

import (
	"database/sql"
	"log"

	_ "github.com/lib/pq" // PostgreSQL driver
)

var db *sql.DB

// InitDB initializes the database connection
func InitDB(connStr string) {
	var err error
	db, err = sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("failed to connect to the database: %v", err)
	}

	// Optionally, you can ping the database to ensure it's reachable
	if err := db.Ping(); err != nil {
		log.Fatalf("failed to ping the database: %v", err)
	}
}

// GetDB returns the database connection
func GetDB() *sql.DB {
	return db
}
