# 🧪 Testing Guide - University Exam Form Management System

## 📋 Overview

This document provides comprehensive information about the testing strategy, test structure, and how to run tests for the University Exam Form Management System (UMIS).

## 🏗️ Test Architecture

### **Test Categories**

1. **Unit Tests** - Test individual components in isolation
2. **Integration Tests** - Test component interactions and database operations
3. **End-to-End Tests** - Test complete workflows
4. **Performance Tests** - Benchmark critical operations

### **Test Structure**

```
backend/app/
├── tests/
│   ├── testutils/              # Test utilities and helpers
│   │   └── testutils.go       # Common test functions
│   └── integration/            # Integration test suites
│       └── student_flow_test.go
├── internal/
│   ├── config/tests/           # Configuration tests
│   ├── errors/tests/           # Error handling tests
│   ├── models/tests/           # Domain model tests
│   ├── auth/tests/             # Authentication tests
│   ├── repository/tests/       # Repository layer tests
│   ├── services/tests/         # Service layer tests
│   ├── handlers/tests/         # HTTP handler tests
│   └── middleware/tests/       # Middleware tests
└── scripts/
    └── run_tests.sh           # Test runner script
```

## 🚀 Running Tests

### **Quick Start**

```bash
# Run all tests
make test

# Run specific test categories
make test-unit          # Unit tests only
make test-integration   # Integration tests only
make test-coverage      # Tests with coverage report
make test-bench         # Performance benchmarks
```

### **Using Test Script Directly**

```bash
cd backend/app

# Run all tests
./scripts/run_tests.sh

# Run specific categories
./scripts/run_tests.sh unit
./scripts/run_tests.sh integration
./scripts/run_tests.sh coverage

# Run specific test patterns
./scripts/run_tests.sh specific Student
./scripts/run_tests.sh specific "TestAuth"
```

### **Using Go Test Directly**

```bash
cd backend/app

# Run all tests
go test ./...

# Run tests with verbose output
go test -v ./...

# Run specific package tests
go test -v ./internal/models/tests/
go test -v ./internal/services/tests/

# Run tests with coverage
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html
```

## 📊 Test Coverage

### **Coverage Goals**

- **Overall Coverage**: > 80%
- **Critical Paths**: > 95%
- **Business Logic**: > 90%
- **Error Handling**: > 85%

### **Generating Coverage Reports**

```bash
# Generate HTML coverage report
make test-coverage

# View coverage in browser
open backend/app/coverage/coverage.html

# Show coverage summary
go tool cover -func=backend/app/coverage/coverage.out
```

## 🧪 Test Categories Detail

### **1. Unit Tests**

Test individual components without external dependencies.

**Examples:**
- Model validation
- Configuration loading
- Error handling
- Authentication logic
- Utility functions

**Location:** `internal/*/tests/`

**Run:** `make test-unit`

### **2. Integration Tests**

Test component interactions with real dependencies (database, etc.).

**Examples:**
- Repository operations
- Service layer business logic
- Database transactions
- Authentication flows

**Location:** `internal/repository/tests/`, `internal/services/tests/`, `tests/integration/`

**Run:** `make test-integration`

### **3. HTTP Tests**

Test HTTP handlers and middleware.

**Examples:**
- Request/response handling
- Authentication middleware
- Error responses
- CORS handling

**Location:** `internal/handlers/tests/`, `internal/middleware/tests/`

**Run:** Included in `make test`

## 🛠️ Test Utilities

### **Test Helpers**

The `testutils` package provides common testing utilities:

```go
// Database setup
db := testutils.SetupTestDB(t)
defer testutils.TeardownTestDB(t, db)

// Service setup
svc, db := testutils.SetupTestServices(t)
defer testutils.CleanupTestServices(t, db)

// Assertions
testutils.AssertNoError(t, err)
testutils.AssertEqual(t, expected, actual)
testutils.AssertContains(t, str, substr)

// Test data creation
student := testutils.CreateTestStudent()
examForm := testutils.CreateTestExamForm()
```

### **Mock Services**

For testing components that depend on external services:

```go
// Mock authentication service
mockAuth := newMockAuthService()
mockAuth.addValidToken("token", "user")

// Use in middleware tests
middleware := middleware.AuthMiddleware(mockAuth)
```

## 📋 Test Requirements

### **Prerequisites**

1. **PostgreSQL** - Running on localhost:5432
2. **Go 1.23+** - For running tests
3. **Environment Variables** - Set automatically by test script

### **Database Setup**

Tests automatically create and clean up test databases:

```bash
# Test database configuration
TEST_DB_NAME="balena_test"
TEST_DB_USER="balena"
TEST_DB_PASSWORD="test_password"
TEST_DB_HOST="localhost"
TEST_DB_PORT="5432"
```

### **Environment Variables**

Test script automatically sets required environment variables:

```bash
export DB_HOST="localhost"
export DB_PORT="5432"
export DB_USER="balena"
export DB_PASSWORD="test_password"
export JWT_SECRET="test_jwt_secret_for_testing_only"
export APP_USERNAME="test_admin"
export APP_PASSWORD="test_password"
export APP_ENV="test"
export LOG_LEVEL="error"
```

## 🔍 Writing Tests

### **Test Naming Convention**

```go
func TestComponentName_MethodName(t *testing.T) {
    t.Run("specific scenario", func(t *testing.T) {
        // Test implementation
    })
}
```

### **Test Structure**

```go
func TestStudentService_CreateStudent(t *testing.T) {
    // Setup
    svc, db := testutils.SetupTestServices(t)
    defer testutils.CleanupTestServices(t, db)
    
    ctx := context.Background()
    
    t.Run("successful creation", func(t *testing.T) {
        // Arrange
        req := testutils.CreateTestStudent()
        
        // Act
        student, err := svc.Student().CreateStudent(ctx, req)
        
        // Assert
        testutils.AssertNoError(t, err)
        testutils.AssertNotEqual(t, int32(0), student.ID)
        testutils.AssertEqual(t, req.Name, student.Name)
    })
    
    t.Run("validation error", func(t *testing.T) {
        // Test error scenarios
    })
}
```

### **Best Practices**

1. **Use table-driven tests** for multiple scenarios
2. **Test both success and error paths**
3. **Use descriptive test names**
4. **Clean up resources** in defer statements
5. **Use test helpers** for common operations
6. **Mock external dependencies**
7. **Test edge cases** and boundary conditions

## 🚨 Troubleshooting

### **Common Issues**

1. **PostgreSQL not running**
   ```bash
   # Start PostgreSQL (macOS)
   brew services start postgresql
   
   # Check if running
   pg_isready -h localhost -p 5432
   ```

2. **Permission denied on test script**
   ```bash
   chmod +x backend/app/scripts/run_tests.sh
   ```

3. **Database connection errors**
   ```bash
   # Check PostgreSQL configuration
   psql -h localhost -p 5432 -U balena -d postgres
   ```

4. **Test database cleanup issues**
   ```bash
   # Manual cleanup
   make test-clean
   ```

### **Debug Mode**

Run tests with verbose output and debug information:

```bash
# Verbose test output
go test -v ./...

# Debug specific test
go test -v -run TestSpecificTest ./internal/services/tests/

# Enable debug logging
export LOG_LEVEL=debug
./scripts/run_tests.sh
```

## 📈 Performance Testing

### **Benchmarks**

Run performance benchmarks:

```bash
# Run all benchmarks
make test-bench

# Run specific benchmarks
go test -bench=BenchmarkStudentCreation ./internal/services/tests/

# Benchmark with memory profiling
go test -bench=. -benchmem ./...
```

### **Load Testing**

For HTTP endpoint load testing:

```bash
# Install hey (HTTP load testing tool)
go install github.com/rakyll/hey@latest

# Load test endpoints
hey -n 1000 -c 10 http://localhost:8080/health
```

## 📝 Test Reports

### **Coverage Reports**

```bash
# Generate coverage report
make test-coverage

# View in browser
open backend/app/coverage/coverage.html

# Coverage summary
go tool cover -func=backend/app/coverage/coverage.out | tail -1
```

### **Test Results**

Test results are displayed in the terminal with color-coded output:
- 🟢 **Green**: Tests passed
- 🔴 **Red**: Tests failed
- 🟡 **Yellow**: Warnings
- 🔵 **Blue**: Information

## 🎯 Continuous Integration

For CI/CD pipelines, use:

```bash
# Run tests in CI environment
./scripts/run_tests.sh all

# Generate coverage for CI
./scripts/run_tests.sh coverage

# Check exit codes
if [ $? -eq 0 ]; then
    echo "All tests passed"
else
    echo "Tests failed"
    exit 1
fi
```

## 📚 Additional Resources

- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [Testify Framework](https://github.com/stretchr/testify)
- [SQLC Documentation](https://docs.sqlc.dev/)
- [PostgreSQL Testing](https://www.postgresql.org/docs/current/regress.html)

---

**Happy Testing! 🧪✨**
