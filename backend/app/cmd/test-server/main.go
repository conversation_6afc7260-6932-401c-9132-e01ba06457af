package main

import (
	"encoding/json"
	"html/template"
	"log"
	"net/http"
	"os"
	"strconv"

	"github.com/gorilla/mux"
)

// Simple models for testing
type Department struct {
	ID          int32    `json:"id"`
	Name        string   `json:"name"`
	Code        string   `json:"code"`
	Description string   `json:"description"`
	Subjects    []string `json:"subjects"`
}

type Student struct {
	ID           int32  `json:"id"`
	Name         string `json:"name"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
	Status       string `json:"status"`
}

type Enrollment struct {
	ID           int32  `json:"id"`
	StudentID    int32  `json:"student_id"`
	DepartmentID int32  `json:"department_id"`
	Subject      string `json:"subject"`
	Session      string `json:"session"`
	Status       string `json:"status"`
}

// Mock data
var departments = []Department{
	{
		ID:          1,
		Name:        "Science Department",
		Code:        "SCI",
		Description: "Physics, Chemistry, Mathematics, Botany, Zoology",
		Subjects:    []string{"Physics", "Chemistry", "Mathematics", "Botany", "Zoology"},
	},
	{
		ID:          2,
		Name:        "Language Department",
		Code:        "LANG",
		Description: "Hindi, English, Urdu, Sanskrit",
		Subjects:    []string{"Hindi", "English", "Urdu", "Sanskrit"},
	},
	{
		ID:          3,
		Name:        "Social Science Department",
		Code:        "SOC",
		Description: "History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM",
		Subjects:    []string{"History", "Political Science", "Economics", "Sociology", "Philosophy", "Psychology", "AIHC", "IRPM"},
	},
}

var students = []Student{}
var enrollments = []Enrollment{}
var nextStudentID int32 = 1
var nextEnrollmentID int32 = 1

// Template data structure
type PageData struct {
	Title       string
	Departments []Department
	Students    []Student
	Enrollments []Enrollment
	Stats       map[string]interface{}
}

func main() {
	// Load templates
	templates, err := template.ParseGlob("templates/*.html")
	if err != nil {
		log.Fatal("Failed to parse templates:", err)
	}

	// Setup router
	r := mux.NewRouter()

	// Static files
	r.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("static/"))))

	// Template routes
	r.HandleFunc("/", func(w http.ResponseWriter, req *http.Request) {
		data := PageData{
			Title:       "University Information Management System",
			Departments: departments,
		}
		templates.ExecuteTemplate(w, "index.html", data)
	}).Methods("GET")

	r.HandleFunc("/student/register", func(w http.ResponseWriter, req *http.Request) {
		data := PageData{
			Title: "Student Registration",
		}
		templates.ExecuteTemplate(w, "student_registration.html", data)
	}).Methods("GET")

	r.HandleFunc("/student/enrollment", func(w http.ResponseWriter, req *http.Request) {
		data := PageData{
			Title:       "Student Enrollment",
			Departments: departments,
		}
		templates.ExecuteTemplate(w, "student_enrollment.html", data)
	}).Methods("GET")

	r.HandleFunc("/student/status", func(w http.ResponseWriter, req *http.Request) {
		data := PageData{
			Title: "Student Status",
		}
		templates.ExecuteTemplate(w, "student_status.html", data)
	}).Methods("GET")

	r.HandleFunc("/admin/login", func(w http.ResponseWriter, req *http.Request) {
		data := PageData{
			Title: "Admin Login",
		}
		templates.ExecuteTemplate(w, "admin_login.html", data)
	}).Methods("GET")

	r.HandleFunc("/admin/dashboard", func(w http.ResponseWriter, req *http.Request) {
		stats := map[string]interface{}{
			"TotalStudents":      len(students),
			"TotalDepartments":   len(departments),
			"PendingEnrollments": countPendingEnrollments(),
			"TotalAdmins":        1,
		}

		data := PageData{
			Title:       "Admin Dashboard",
			Departments: departments,
			Students:    students,
			Enrollments: enrollments,
			Stats:       stats,
		}
		templates.ExecuteTemplate(w, "admin_dashboard.html", data)
	}).Methods("GET")

	// API routes
	api := r.PathPrefix("/api").Subrouter()
	api.Use(jsonMiddleware)

	// Public API routes
	api.HandleFunc("/departments", getDepartments).Methods("GET")
	api.HandleFunc("/departments/{id}/subjects", getDepartmentSubjects).Methods("GET")
	api.HandleFunc("/students/register", registerStudent).Methods("POST")
	api.HandleFunc("/students/enroll", enrollStudent).Methods("POST")
	api.HandleFunc("/students/status", getStudentStatus).Methods("GET")

	// Mock auth API
	api.HandleFunc("/auth/login", mockLogin).Methods("POST")

	// Health check
	r.HandleFunc("/health", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "healthy",
			"service": "university-management-system-test",
		})
	}).Methods("GET")

	port := getEnv("PORT", "8080")
	log.Printf("🎓 Test server starting on port %s", port)
	log.Printf("📱 Access the application at: http://localhost:%s", port)
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// Middleware
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		next.ServeHTTP(w, r)
	})
}

// API Handlers
func getDepartments(w http.ResponseWriter, r *http.Request) {
	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    departments,
	})
}

func getDepartmentSubjects(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]

	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid department ID",
		})
		return
	}

	for _, dept := range departments {
		if dept.ID == int32(id) {
			sendJSON(w, http.StatusOK, map[string]interface{}{
				"success": true,
				"data":    dept.Subjects,
			})
			return
		}
	}

	sendJSON(w, http.StatusNotFound, map[string]interface{}{
		"success": false,
		"error":   "Department not found",
	})
}

func registerStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	// Basic validation
	name, ok := req["name"].(string)
	if !ok || name == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Name is required",
		})
		return
	}

	email, ok := req["email"].(string)
	if !ok || email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email is required",
		})
		return
	}

	// Check if student already exists
	for _, student := range students {
		if student.Email == email {
			sendJSON(w, http.StatusConflict, map[string]interface{}{
				"success": false,
				"error":   "Student with this email already exists",
			})
			return
		}
	}

	// Create new student
	student := Student{
		ID:           nextStudentID,
		Name:         name,
		Email:        email,
		MobileNumber: getStringFromMap(req, "mobile_number"),
		Status:       "registered",
	}
	nextStudentID++

	students = append(students, student)

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    student,
		"message": "Student registered successfully",
	})
}

func enrollStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	// Find student by email (simplified)
	email := getStringFromMap(req, "student_email")
	var studentID int32
	found := false
	for _, student := range students {
		if student.Email == email {
			studentID = student.ID
			found = true
			break
		}
	}

	if !found {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found. Please register first.",
		})
		return
	}

	// Create enrollment
	enrollment := Enrollment{
		ID:           nextEnrollmentID,
		StudentID:    studentID,
		DepartmentID: int32(getIntFromMap(req, "department_id")),
		Subject:      getStringFromMap(req, "subject"),
		Session:      getStringFromMap(req, "session"),
		Status:       "pending",
	}
	nextEnrollmentID++

	enrollments = append(enrollments, enrollment)

	// Update student status
	for i := range students {
		if students[i].ID == studentID {
			students[i].Status = "enrolled"
			break
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    enrollment,
		"message": "Student enrolled successfully",
	})
}

func getStudentStatus(w http.ResponseWriter, r *http.Request) {
	email := r.URL.Query().Get("email")
	if email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email parameter required",
		})
		return
	}

	// Find student
	var student *Student
	for i := range students {
		if students[i].Email == email {
			student = &students[i]
			break
		}
	}

	if student == nil {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found",
		})
		return
	}

	// Find enrollments
	var studentEnrollments []Enrollment
	for _, enrollment := range enrollments {
		if enrollment.StudentID == student.ID {
			studentEnrollments = append(studentEnrollments, enrollment)
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"student":     student,
			"enrollments": studentEnrollments,
		},
	})
}

func mockLogin(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	email := getStringFromMap(req, "email")
	password := getStringFromMap(req, "password")

	// Mock authentication
	if email == "<EMAIL>" && password == "admin123" {
		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data": map[string]interface{}{
				"token": "mock-jwt-token",
				"user": map[string]interface{}{
					"id":    1,
					"email": email,
					"role":  "super_admin",
				},
			},
			"message": "Login successful",
		})
	} else {
		sendJSON(w, http.StatusUnauthorized, map[string]interface{}{
			"success": false,
			"error":   "Invalid credentials",
		})
	}
}

// Helper functions
func sendJSON(w http.ResponseWriter, status int, data interface{}) {
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func getIntFromMap(m map[string]interface{}, key string) int {
	if val, ok := m[key].(float64); ok {
		return int(val)
	}
	return 0
}

func countPendingEnrollments() int {
	count := 0
	for _, enrollment := range enrollments {
		if enrollment.Status == "pending" {
			count++
		}
	}
	return count
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
