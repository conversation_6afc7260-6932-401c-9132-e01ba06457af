package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"

	"app/internal/models"
	"app/internal/services"

	"github.com/gorilla/mux"
	_ "github.com/lib/pq"
)

// SQLC Demo Server - Demonstrates the new workflow with SQLC integration
func main() {
	// Database connection
	dbURL := getEnv("DATABASE_URL", "postgres://balena:test@localhost:5432/balena?sslmode=disable")
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Test database connection
	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}
	log.Println("✅ Connected to database successfully")

	// Initialize SQLC service
	studentService := services.NewStudentServiceSQLC(db)

	// Setup router
	r := mux.NewRouter()

	// API routes using SQLC
	api := r.PathPrefix("/api/v2").Subrouter()
	api.Use(jsonMiddleware)

	// Department routes
	api.HandleFunc("/departments", func(w http.ResponseWriter, req *http.Request) {
		departments, err := studentService.GetAllDepartments(req.Context())
		if err != nil {
			sendError(w, http.StatusInternalServerError, "Failed to get departments", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    departments,
		})
	}).Methods("GET")

	// Student registration route
	api.HandleFunc("/students/register", func(w http.ResponseWriter, req *http.Request) {
		var regReq models.StudentRegistrationRequest
		if err := json.NewDecoder(req.Body).Decode(&regReq); err != nil {
			sendError(w, http.StatusBadRequest, "Invalid request body", err)
			return
		}

		student, err := studentService.RegisterStudent(req.Context(), &regReq)
		if err != nil {
			sendError(w, http.StatusInternalServerError, "Failed to register student", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    student,
			"message": "Student registered successfully",
		})
	}).Methods("POST")

	// Student enrollment route
	api.HandleFunc("/students/enroll", func(w http.ResponseWriter, req *http.Request) {
		var enrollReq models.EnrollmentRequest
		if err := json.NewDecoder(req.Body).Decode(&enrollReq); err != nil {
			sendError(w, http.StatusBadRequest, "Invalid request body", err)
			return
		}

		enrollment, err := studentService.EnrollStudent(req.Context(), &enrollReq)
		if err != nil {
			sendError(w, http.StatusInternalServerError, "Failed to enroll student", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    enrollment,
			"message": "Student enrolled successfully",
		})
	}).Methods("POST")

	// Student status route
	api.HandleFunc("/students/status", func(w http.ResponseWriter, req *http.Request) {
		email := req.URL.Query().Get("email")
		if email == "" {
			sendError(w, http.StatusBadRequest, "Email parameter required", nil)
			return
		}

		status, err := studentService.GetStudentEnrollments(req.Context(), email)
		if err != nil {
			sendError(w, http.StatusNotFound, "Student not found", err)
			return
		}

		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data":    status,
		})
	}).Methods("GET")

	// Health check
	r.HandleFunc("/health", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "healthy",
			"service": "university-management-system-sqlc-demo",
			"message": "SQLC integration working",
		})
	}).Methods("GET")

	// Documentation route
	r.HandleFunc("/", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "text/html")
		w.Write([]byte(`
<!DOCTYPE html>
<html>
<head>
    <title>SQLC Demo - University Management System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .endpoint { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .method { color: #007bff; font-weight: bold; }
        .url { color: #28a745; font-family: monospace; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🎓 University Management System - SQLC Demo</h1>
    <p>This demo showcases the new workflow with SQLC integration for robust database operations.</p>
    
    <h2>Available Endpoints:</h2>
    
    <div class="endpoint">
        <span class="method">GET</span> <span class="url">/health</span>
        <p>Health check endpoint</p>
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <span class="url">/api/v2/departments</span>
        <p>Get all departments</p>
    </div>
    
    <div class="endpoint">
        <span class="method">POST</span> <span class="url">/api/v2/students/register</span>
        <p>Register a new student</p>
        <pre>{
  "name": "Test Student",
  "email": "<EMAIL>",
  "date_of_birth": "2000-01-01",
  "gender": "M",
  "category": "UR",
  "mobile_number": "**********",
  "father_name": "Test Father",
  "mother_name": "Test Mother",
  "pincode": "123456",
  "state": "BIHAR",
  "aadhar_number": "123456789012",
  "abc_id": "ABC123456789",
  "aadhar_mobile": "**********"
}</pre>
    </div>
    
    <div class="endpoint">
        <span class="method">POST</span> <span class="url">/api/v2/students/enroll</span>
        <p>Enroll student in a department</p>
        <pre>{
  "student_email": "<EMAIL>",
  "department_id": 1,
  "session": "2024-25",
  "subject": "Physics"
}</pre>
    </div>
    
    <div class="endpoint">
        <span class="method">GET</span> <span class="url">/api/v2/students/status?email=<EMAIL></span>
        <p>Get student status and enrollments</p>
    </div>
    
    <h2>Features:</h2>
    <ul>
        <li>✅ SQLC for type-safe database operations</li>
        <li>✅ Proper error handling and validation</li>
        <li>✅ Two-stage workflow (registration → enrollment)</li>
        <li>✅ Department-wise administration support</li>
        <li>✅ Comprehensive API documentation</li>
    </ul>
    
    <h2>Testing:</h2>
    <p>Use curl or any HTTP client to test the endpoints. Make sure PostgreSQL is running with the new schema.</p>
    
    <h3>Quick Test:</h3>
    <pre>curl http://localhost:8081/health
curl http://localhost:8081/api/v2/departments</pre>
</body>
</html>
		`))
	}).Methods("GET")

	port := getEnv("PORT", "8081")
	log.Printf("🎓 SQLC Demo Server starting on port %s", port)
	log.Printf("📱 Access the demo at: http://localhost:%s", port)
	log.Printf("🔧 This demonstrates SQLC integration with the new workflow")
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// Middleware
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		next.ServeHTTP(w, r)
	})
}

// Helper functions
func sendJSON(w http.ResponseWriter, status int, data interface{}) {
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func sendError(w http.ResponseWriter, status int, message string, err error) {
	response := map[string]interface{}{
		"success": false,
		"error":   message,
	}
	if err != nil {
		response["details"] = err.Error()
	}
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(response)
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
