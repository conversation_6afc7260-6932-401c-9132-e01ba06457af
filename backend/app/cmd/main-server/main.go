package main

import (
	"database/sql"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"strings"

	"app/data"

	"github.com/gorilla/mux"
	_ "github.com/lib/pq"
)

// PageContext holds common data for all page templates
type PageContext struct {
	Title           string
	CurrentPage     string
	IsAuthenticated bool
	UserRole        string
	UserName        string
	UserEmail       string
	Error           string
	Success         string
	// Additional data for specific pages
	Departments interface{}
	Students    interface{}
	Stats       map[string]interface{}
}

// Server represents the main server
type Server struct {
	db        *sql.DB
	queries   *data.Queries
	router    *mux.Router
	templates map[string]*template.Template
}

// NewServer creates a new server instance
func NewServer(dbURL string) (*Server, error) {
	// Connect to database
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	// Initialize SQLC queries
	queries := data.New(db)

	// Initialize router
	router := mux.NewRouter()

	server := &Server{
		db:        db,
		queries:   queries,
		router:    router,
		templates: make(map[string]*template.Template),
	}

	// Load templates
	if err := server.loadTemplates(); err != nil {
		log.Printf("Warning: Failed to load templates: %v", err)
	}

	// Setup routes
	server.setupRoutes()

	return server, nil
}

// loadTemplates loads all templates
func (s *Server) loadTemplates() error {
	templateDir := "templates"

	// Template files to load
	templateFiles := map[string][]string{
		"index": {
			templateDir + "/base.html",
			templateDir + "/index.html",
			templateDir + "/components/navbar.html",
		},
		"student_registration": {
			templateDir + "/base.html",
			templateDir + "/student_registration.html",
			templateDir + "/components/navbar.html",
		},
		"student_enrollment": {
			templateDir + "/base.html",
			templateDir + "/student_enrollment.html",
			templateDir + "/components/navbar.html",
		},
		"student_status": {
			templateDir + "/base.html",
			templateDir + "/student_status.html",
			templateDir + "/components/navbar.html",
		},
		"admin_login": {
			templateDir + "/base.html",
			templateDir + "/admin_login.html",
			templateDir + "/components/navbar.html",
		},
		"admin_dashboard": {
			templateDir + "/base.html",
			templateDir + "/admin_dashboard.html",
			templateDir + "/components/navbar.html",
		},
	}

	for name, files := range templateFiles {
		tmpl, err := template.ParseFiles(files...)
		if err != nil {
			return fmt.Errorf("failed to parse template %s: %w", name, err)
		}
		s.templates[name] = tmpl
	}

	log.Printf("✅ Loaded %d templates successfully", len(s.templates))
	return nil
}

// createPageContext creates a page context with authentication info
func (s *Server) createPageContext(title, currentPage string, r *http.Request) PageContext {
	// Check for demo authentication cookie
	isAuthenticated := false
	userRole := ""
	userName := ""
	userEmail := ""

	if cookie, err := r.Cookie("demo_auth"); err == nil && cookie.Value != "" {
		isAuthenticated = true
		// Parse demo auth cookie (format: "role:name:email")
		parts := strings.Split(cookie.Value, ":")
		if len(parts) >= 3 {
			userRole = parts[0]
			userName = parts[1]
			userEmail = parts[2]
		}
	}

	return PageContext{
		Title:           title,
		CurrentPage:     currentPage,
		IsAuthenticated: isAuthenticated,
		UserRole:        userRole,
		UserName:        userName,
		UserEmail:       userEmail,
		Error:           r.URL.Query().Get("error"),
		Success:         r.URL.Query().Get("success"),
	}
}

// renderTemplate renders a template with data
func (s *Server) renderTemplate(w http.ResponseWriter, templateName string, data PageContext) {
	tmpl, exists := s.templates[templateName]
	if !exists {
		log.Printf("Template %s not found", templateName)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	if err := tmpl.ExecuteTemplate(w, "base.html", data); err != nil {
		log.Printf("Failed to execute template %s: %v", templateName, err)
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

// setupRoutes configures all routes
func (s *Server) setupRoutes() {
	// Static files
	s.router.PathPrefix("/assets/").Handler(http.StripPrefix("/assets/", http.FileServer(http.Dir("assets/"))))

	// Template routes
	s.router.HandleFunc("/", s.handleIndex).Methods("GET")
	s.router.HandleFunc("/student/register", s.handleStudentRegistration).Methods("GET")
	s.router.HandleFunc("/student/enrollment", s.handleStudentEnrollment).Methods("GET")
	s.router.HandleFunc("/student/status", s.handleStudentStatus).Methods("GET")
	s.router.HandleFunc("/admin/login", s.handleAdminLogin).Methods("GET")
	s.router.HandleFunc("/admin/dashboard", s.handleAdminDashboard).Methods("GET")

	// Authentication routes
	s.router.HandleFunc("/logout", s.handleLogout).Methods("GET")

	// API routes
	api := s.router.PathPrefix("/api").Subrouter()
	api.HandleFunc("/auth/login", s.handleAPILogin).Methods("POST")
	api.HandleFunc("/auth/logout", s.handleAPILogout).Methods("POST")
	api.HandleFunc("/departments", s.handleAPIDepartments).Methods("GET")

	// Health check
	s.router.HandleFunc("/health", s.handleHealth).Methods("GET")
}

// Route handlers
func (s *Server) handleIndex(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("University Information Management System", "home", r)
	s.renderTemplate(w, "index", data)
}

func (s *Server) handleStudentRegistration(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("Student Registration", "register", r)
	s.renderTemplate(w, "student_registration", data)
}

func (s *Server) handleStudentEnrollment(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("Student Enrollment", "enrollment", r)

	// Get departments using SQLC
	departments, err := s.queries.GetAllDepartments(r.Context())
	if err != nil {
		log.Printf("Failed to get departments: %v", err)
	} else {
		data.Departments = departments
	}

	s.renderTemplate(w, "student_enrollment", data)
}

func (s *Server) handleStudentStatus(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("Student Status", "status", r)
	s.renderTemplate(w, "student_status", data)
}

func (s *Server) handleAdminLogin(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("Admin Login", "login", r)
	s.renderTemplate(w, "admin_login", data)
}

func (s *Server) handleAdminDashboard(w http.ResponseWriter, r *http.Request) {
	data := s.createPageContext("Admin Dashboard", "admin-dashboard", r)

	// Get dashboard stats using SQLC
	studentCount, _ := s.queries.CountStudents(r.Context())
	departments, _ := s.queries.GetAllDepartments(r.Context())
	pendingCount, _ := s.queries.CountEnrollmentsByStatus(r.Context(), "pending")

	data.Stats = map[string]interface{}{
		"TotalStudents":      studentCount,
		"TotalDepartments":   len(departments),
		"PendingEnrollments": pendingCount,
		"TotalAdmins":        2,
	}
	data.Departments = departments

	s.renderTemplate(w, "admin_dashboard", data)
}

func (s *Server) handleLogout(w http.ResponseWriter, r *http.Request) {
	// Clear authentication cookie
	cookie := &http.Cookie{
		Name:     "demo_auth",
		Value:    "",
		Path:     "/",
		HttpOnly: false,
		MaxAge:   -1,
	}
	http.SetCookie(w, cookie)

	http.Redirect(w, r, "/", http.StatusSeeOther)
}

func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	fmt.Fprintf(w, `{"status":"healthy","service":"university-management-system-main","version":"2.0.0"}`)
}

// API handlers
func (s *Server) handleAPILogin(w http.ResponseWriter, r *http.Request) {
	// Simple demo login - in production, use proper authentication
	var loginReq struct {
		Email    string `json:"email"`
		Password string `json:"password"`
	}

	// For demo, accept <EMAIL> / admin123
	if loginReq.Email == "<EMAIL>" && loginReq.Password == "admin123" {
		cookie := &http.Cookie{
			Name:     "demo_auth",
			Value:    "admin:Admin User:<EMAIL>",
			Path:     "/",
			HttpOnly: false,
			MaxAge:   3600,
		}
		http.SetCookie(w, cookie)

		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"success":true,"message":"Login successful"}`)
	} else {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusUnauthorized)
		fmt.Fprintf(w, `{"success":false,"error":"Invalid credentials"}`)
	}
}

func (s *Server) handleAPILogout(w http.ResponseWriter, r *http.Request) {
	cookie := &http.Cookie{
		Name:     "demo_auth",
		Value:    "",
		Path:     "/",
		HttpOnly: false,
		MaxAge:   -1,
	}
	http.SetCookie(w, cookie)

	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"success":true,"message":"Logout successful"}`)
}

func (s *Server) handleAPIDepartments(w http.ResponseWriter, r *http.Request) {
	departments, err := s.queries.GetAllDepartments(r.Context())
	if err != nil {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		fmt.Fprintf(w, `{"success":false,"error":"Failed to get departments"}`)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	fmt.Fprintf(w, `{"success":true,"data":%v}`, departments)
}

// Start starts the server
func (s *Server) Start(port string) error {
	log.Printf("🎓 University Management System (Main Server) starting on port %s", port)
	log.Printf("📱 Access the application at: http://localhost:%s", port)
	log.Printf("🔧 Features: Modular Navbar + SQLC Integration")
	return http.ListenAndServe(":"+port, s.router)
}

func main() {
	// Database URL
	dbURL := getEnv("DATABASE_URL", "postgres://balena:test@localhost:5432/balena?sslmode=disable")

	// Create server
	server, err := NewServer(dbURL)
	if err != nil {
		log.Fatal("Failed to create server:", err)
	}

	// Start server
	port := getEnv("PORT", "8081")
	if err := server.Start(port); err != nil {
		log.Fatal("Server error:", err)
	}
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
