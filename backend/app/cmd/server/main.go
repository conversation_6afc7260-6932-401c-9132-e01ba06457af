package main

import (
	"context"
	"log/slog"
	"os"

	"app/internal/config"
	"app/internal/server"
)

func main() {
	// Initialize logger
	logger := initLogger()

	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.Error("Failed to load configuration", "error", err)
		os.Exit(1)
	}

	logger.Info("Configuration loaded successfully",
		"environment", cfg.App.Environment,
		"server_address", cfg.GetServerAddress(),
		"database_host", cfg.Database.Host)

	// Create server
	srv, err := server.New(cfg, logger)
	if err != nil {
		logger.Error("Failed to create server", "error", err)
		os.Exit(1)
	}

	// Perform health check
	ctx := context.Background()
	if err := srv.HealthCheck(ctx); err != nil {
		logger.Error("Health check failed", "error", err)
		os.Exit(1)
	}

	logger.Info("Health check passed")

	// Start server
	logger.Info("Starting University Information Management System (UMIS)")
	if err := srv.Start(ctx); err != nil {
		logger.Error("Server error", "error", err)
		os.Exit(1)
	}
}

// initLogger initializes the structured logger
func initLogger() *slog.Logger {
	// Get log level from environment
	logLevel := os.Getenv("LOG_LEVEL")
	if logLevel == "" {
		logLevel = "info"
	}

	var level slog.Level
	switch logLevel {
	case "debug":
		level = slog.LevelDebug
	case "info":
		level = slog.LevelInfo
	case "warn":
		level = slog.LevelWarn
	case "error":
		level = slog.LevelError
	default:
		level = slog.LevelInfo
	}

	// Create logger options
	opts := &slog.HandlerOptions{
		Level:     level,
		AddSource: level == slog.LevelDebug,
	}

	// Create appropriate handler based on environment
	var handler slog.Handler
	if os.Getenv("APP_ENV") == "production" {
		// JSON format for production
		handler = slog.NewJSONHandler(os.Stdout, opts)
	} else {
		// Text format for development
		handler = slog.NewTextHandler(os.Stdout, opts)
	}

	return slog.New(handler)
}
