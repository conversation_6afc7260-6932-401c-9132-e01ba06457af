{"name": "go_app", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build:css": "tailwindcss -i ./app/assets/css/input.css -o ./app/assets/css/output.css --watch"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.4.45", "postcss-cli": "^11.0.0", "tailwindcss": "^3.4.10"}}