# Stage 1: Build the Go application
FROM golang:1.23-alpine AS builder

WORKDIR /app

# Copy go.mod and go.sum files first to leverage Docker cache
COPY app/go.mod app/go.sum ./
RUN go mod download

# Copy the source code
COPY app ./

# Build the Go application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -a -installsuffix cgo -o myapp .
# RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o myapp .

# Stage 2: Create the final image
FROM nginx:alpine

WORKDIR /app

# Install WeasyPrint dependencies
# Install WeasyPrint, fonts, and dependencies
RUN apk add --no-cache \
    python3 \
    py3-weasyprint \
    cairo \
    pango \
    gdk-pixbuf \
    libffi \
    fontconfig \
    ttf-dejavu \
    font-noto-devanagari \
    ttf-freefont \
    ghostscript-fonts

# Copy the Nginx configuration file
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port 80
EXPOSE 80

# Copy the Go binary from the builder stage
COPY --from=builder /app/myapp .

# Copy source and assets folders from the builder stage
COPY --from=builder /app/assets/css/output.css ./assets/css/output.css

# Copy the templates folder
COPY --from=builder /app/templates ./templates

# Start the Go application and Nginx
CMD ["sh", "-c", "./myapp & nginx -g 'daemon off;'"]
