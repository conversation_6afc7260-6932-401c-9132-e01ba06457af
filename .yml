services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: university_user
      POSTGRES_PASSWORD: university_password
      POSTGRES_DB: ssv
    volumes:
      - db_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  app:
    build: ./backend
    depends_on:
      - postgres
    environment:
      DATABASE_URL: ************************************************************/ssv?sslmode=disable
      JWT_SECRET: fkldsfjlalk
    ports:
      - "8085:80"
volumes:
  db_data:
