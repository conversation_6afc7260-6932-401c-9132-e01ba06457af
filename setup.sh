#!/bin/bash

# University Information Management System - Setup Script
# This script helps set up the refactored system for development and testing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🎓 $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_header "Checking Prerequisites"
    
    # Check Docker
    if command -v docker &> /dev/null; then
        log_success "Docker is installed"
    else
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if command -v docker-compose &> /dev/null; then
        log_success "Docker Compose is installed"
    else
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check Go
    if command -v go &> /dev/null; then
        log_success "Go is installed ($(go version))"
    else
        log_error "Go is not installed. Please install Go 1.21+ first."
        exit 1
    fi
    
    # Check Make
    if command -v make &> /dev/null; then
        log_success "Make is installed"
    else
        log_error "Make is not installed. Please install Make first."
        exit 1
    fi
    
    echo
}

# Setup database
setup_database() {
    log_header "Setting Up Database"
    
    log_info "Starting PostgreSQL with Docker..."
    cd backend
    
    # Start only PostgreSQL
    if make up-postgres; then
        log_success "PostgreSQL started successfully"
    else
        log_error "Failed to start PostgreSQL"
        exit 1
    fi
    
    # Wait for database to be ready
    log_info "Waiting for database to be ready..."
    sleep 10
    
    # Run database migration
    log_info "Running database migration..."
    if docker exec -i exam-form-postgres psql -U postgres -d university_db < ../database_migration.sql; then
        log_success "Database migration completed"
    else
        log_warning "Database migration failed or already applied"
    fi
    
    cd ..
    echo
}

# Setup environment
setup_environment() {
    log_header "Setting Up Environment"
    
    # Create .env file if it doesn't exist
    if [ ! -f "backend/.env" ]; then
        log_info "Creating .env file..."
        cat > backend/.env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=university_db

# Application Configuration
PORT=8080
APP_ENV=development

# JWT Secret (change in production)
JWT_SECRET=your-secret-key-change-in-production

# Template and Static Directories
TEMPLATE_DIR=app/templates
STATIC_DIR=app/static
EOF
        log_success "Environment file created"
    else
        log_info "Environment file already exists"
    fi
    
    echo
}

# Build application
build_application() {
    log_header "Building Application"
    
    cd backend
    
    log_info "Installing Go dependencies..."
    if go mod tidy; then
        log_success "Dependencies installed"
    else
        log_error "Failed to install dependencies"
        exit 1
    fi
    
    log_info "Building application..."
    if go build -o bin/server ./app/cmd/server; then
        log_success "Application built successfully"
    else
        log_error "Failed to build application"
        exit 1
    fi
    
    cd ..
    echo
}

# Start application
start_application() {
    log_header "Starting Application"
    
    cd backend
    
    log_info "Starting the server..."
    log_info "The server will start on http://localhost:8080"
    log_info "Press Ctrl+C to stop the server"
    echo
    
    # Start the server
    ./bin/server
}

# Test setup
test_setup() {
    log_header "Testing Setup"
    
    log_info "Running comprehensive tests..."
    if ./test_workflow.sh; then
        log_success "All tests passed!"
    else
        log_warning "Some tests failed. Check the output above."
    fi
    
    echo
}

# Show usage information
show_usage() {
    echo "University Information Management System - Setup Script"
    echo
    echo "Usage: $0 [OPTION]"
    echo
    echo "Options:"
    echo "  setup     - Complete setup (database, environment, build)"
    echo "  db        - Setup database only"
    echo "  env       - Setup environment only"
    echo "  build     - Build application only"
    echo "  start     - Start application"
    echo "  test      - Run tests"
    echo "  clean     - Clean up (stop containers, remove build files)"
    echo "  help      - Show this help message"
    echo
    echo "Examples:"
    echo "  $0 setup     # Complete setup"
    echo "  $0 start     # Start the application"
    echo "  $0 test      # Run tests"
    echo
}

# Clean up
clean_up() {
    log_header "Cleaning Up"
    
    cd backend
    
    log_info "Stopping Docker containers..."
    make down || true
    
    log_info "Removing build files..."
    rm -f bin/server
    
    cd ..
    
    log_success "Cleanup completed"
    echo
}

# Show system information
show_info() {
    log_header "System Information"
    
    echo "🎓 University Information Management System"
    echo "📁 Project Structure:"
    echo "   ├── backend/           - Go backend application"
    echo "   ├── database_migration.sql - Database setup"
    echo "   ├── test_workflow.sh  - Testing script"
    echo "   └── setup.sh          - This setup script"
    echo
    echo "🌐 Application URLs:"
    echo "   • Homepage: http://localhost:8080"
    echo "   • Student Registration: http://localhost:8080/student/register"
    echo "   • Student Enrollment: http://localhost:8080/student/enrollment"
    echo "   • Admin Login: http://localhost:8080/admin/login"
    echo "   • Student Status: http://localhost:8080/student/status"
    echo "   • Health Check: http://localhost:8080/health"
    echo
    echo "🔑 Default Admin Credentials:"
    echo "   • Email: <EMAIL>"
    echo "   • Password: admin123"
    echo
    echo "📊 Database:"
    echo "   • Host: localhost:5432"
    echo "   • Database: university_db"
    echo "   • Username: postgres"
    echo "   • Password: password"
    echo
}

# Main function
main() {
    case "${1:-setup}" in
        "setup")
            log_header "University Information Management System - Complete Setup"
            echo
            check_prerequisites
            setup_database
            setup_environment
            build_application
            show_info
            echo
            log_success "🎉 Setup completed successfully!"
            echo
            log_info "Next steps:"
            echo "1. Run: $0 start    # Start the application"
            echo "2. Run: $0 test     # Test the application"
            echo "3. Open: http://localhost:8080"
            ;;
        "db")
            check_prerequisites
            setup_database
            ;;
        "env")
            setup_environment
            ;;
        "build")
            check_prerequisites
            build_application
            ;;
        "start")
            check_prerequisites
            start_application
            ;;
        "test")
            test_setup
            ;;
        "clean")
            clean_up
            ;;
        "info")
            show_info
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            log_error "Unknown option: $1"
            echo
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
