package main

import (
	"encoding/json"
	"html/template"
	"log"
	"net/http"
	"os"
	"strconv"

	"github.com/gorilla/mux"
)

// Simple models for testing the new workflow
type Department struct {
	ID          int32    `json:"id"`
	Name        string   `json:"name"`
	Code        string   `json:"code"`
	Description string   `json:"description"`
	Subjects    []string `json:"subjects"`
}

type Student struct {
	ID           int32  `json:"id"`
	Name         string `json:"name"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
	Status       string `json:"status"`
	CreatedAt    string `json:"created_at"`
}

type Enrollment struct {
	ID           int32       `json:"id"`
	StudentID    int32       `json:"student_id"`
	DepartmentID int32       `json:"department_id"`
	Subject      string      `json:"subject"`
	Session      string      `json:"session"`
	Status       string      `json:"status"`
	Student      *Student    `json:"student,omitempty"`
	Department   *Department `json:"department,omitempty"`
	CreatedAt    string      `json:"created_at"`
}

// Mock data storage
var departments = []Department{
	{
		ID:          1,
		Name:        "Science Department",
		Code:        "SCI",
		Description: "Physics, Chemistry, Mathematics, Botany, Zoology",
		Subjects:    []string{"Physics", "Chemistry", "Mathematics", "Botany", "Zoology"},
	},
	{
		ID:          2,
		Name:        "Language Department",
		Code:        "LANG",
		Description: "Hindi, English, Urdu, Sanskrit",
		Subjects:    []string{"Hindi", "English", "Urdu", "Sanskrit"},
	},
	{
		ID:          3,
		Name:        "Social Science Department",
		Code:        "SOC",
		Description: "History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM",
		Subjects:    []string{"History", "Political Science", "Economics", "Sociology", "Philosophy", "Psychology", "AIHC", "IRPM"},
	},
}

var students = []Student{}
var enrollments = []Enrollment{}
var nextStudentID int32 = 1
var nextEnrollmentID int32 = 1

// Template data structure
type PageData struct {
	Title       string
	Departments []Department
	Students    []Student
	Enrollments []Enrollment
	Stats       map[string]interface{}
}

// Template constants for fallback templates
const (
	indexTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-4xl font-bold mb-6 text-center text-blue-800">🎓 University Information Management System</h1>
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-xl font-bold text-blue-800 mb-4">छात्र सेवाएं</h3>
                    <ul class="space-y-2">
                        <li><a href="/student/register" class="text-blue-600 hover:underline">नया पंजीकरण</a></li>
                        <li><a href="/student/enrollment" class="text-blue-600 hover:underline">विभाग नामांकन</a></li>
                        <li><a href="/student/status" class="text-blue-600 hover:underline">स्थिति जांच</a></li>
                    </ul>
                </div>
                <div class="bg-green-50 p-6 rounded-lg">
                    <h3 class="text-xl font-bold text-green-800 mb-4">प्रशासन</h3>
                    <ul class="space-y-2">
                        <li><a href="/admin/login" class="text-green-600 hover:underline">एडमिन लॉगिन</a></li>
                        <li><a href="/admin/dashboard" class="text-green-600 hover:underline">डैशबोर्ड</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`

	studentRegistrationTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">छात्र पंजीकरण</h1>
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <form id="studentForm" class="space-y-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">पूरा नाम *</label>
                        <input type="text" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">ईमेल *</label>
                        <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">जन्म तिथि *</label>
                        <input type="date" name="date_of_birth" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">लिंग *</label>
                        <select name="gender" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें</option>
                            <option value="M">पुरुष</option>
                            <option value="F">महिला</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">मोबाइल नंबर *</label>
                        <input type="tel" name="mobile_number" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">श्रेणी *</label>
                        <select name="category" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें</option>
                            <option value="UR">सामान्य</option>
                            <option value="BC">पिछड़ा वर्ग</option>
                            <option value="SC">अनुसूचित जाति</option>
                            <option value="ST">अनुसूचित जनजाति</option>
                        </select>
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="px-8 py-3 bg-blue-600 text-white font-bold rounded-lg hover:bg-blue-700 transition duration-300">
                        पंजीकरण पूरा करें
                    </button>
                </div>
            </form>
        </div>
    </div>
    <script>
    document.getElementById('studentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        fetch('/api/students/register', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('पंजीकरण सफल!');
                window.location.href = '/student/enrollment';
            } else {
                alert('त्रुटि: ' + (data.message || 'पंजीकरण में समस्या हुई'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('पंजीकरण में त्रुटि हुई।');
        });
    });
    </script>
</body>
</html>`

	studentEnrollmentTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">विभाग नामांकन</h1>
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <form id="enrollmentForm" class="space-y-6">
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">छात्र ईमेल *</label>
                        <input type="email" name="student_email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">विभाग *</label>
                        <select name="department_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">चुनें</option>
                            {{range .Departments}}
                            <option value="{{.ID}}">{{.Name}}</option>
                            {{end}}
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">विषय *</label>
                        <input type="text" name="subject" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">सत्र *</label>
                        <input type="text" name="session" value="2024-25" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                <div class="text-center">
                    <button type="submit" class="px-8 py-3 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition duration-300">
                        नामांकन करें
                    </button>
                </div>
            </form>
        </div>
    </div>
    <script>
    document.getElementById('enrollmentForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        fetch('/api/students/enroll', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('नामांकन सफल!');
                window.location.href = '/student/status?email=' + encodeURIComponent(document.querySelector('[name="student_email"]').value));
            } else {
                alert('त्रुटि: ' + (data.message || 'नामांकन में समस्या हुई'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('नामांकन में त्रुटि हुई।');
        });
    });
    </script>
</body>
</html>`

	studentStatusTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">छात्र स्थिति</h1>
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <form id="statusForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">छात्र ईमेल *</label>
                    <input type="email" name="email" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="text-center">
                    <button type="submit" class="px-8 py-3 bg-purple-600 text-white font-bold rounded-lg hover:bg-purple-700 transition duration-300">
                        स्थिति जांचें
                    </button>
                </div>
            </form>
            <div id="statusResult" class="mt-8 hidden">
                <!-- Status results will be displayed here -->
            </div>
        </div>
    </div>
    <script>
    document.getElementById('statusForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const email = document.querySelector('[name="email"]').value;

        fetch('/api/students/status?email=' + encodeURIComponent(email))
        .then(response => response.json())
        .then(data => {
            const resultDiv = document.getElementById('statusResult');
            if (data.success) {
                resultDiv.innerHTML = '<h3 class="text-xl font-bold text-green-800 mb-4">छात्र जानकारी</h3>' +
                    '<p><strong>नाम:</strong> ' + data.data.student.name + '</p>' +
                    '<p><strong>स्थिति:</strong> ' + data.data.student.status + '</p>' +
                    '<p><strong>नामांकन:</strong> ' + data.data.enrollments.length + '</p>';
                resultDiv.classList.remove('hidden');
            } else {
                resultDiv.innerHTML = '<p class="text-red-600">त्रुटि: ' + data.error + '</p>';
                resultDiv.classList.remove('hidden');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('स्थिति जांच में त्रुटि हुई।');
        });
    });
    </script>
</body>
</html>`

	adminLoginTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">एडमिन लॉगिन</h1>
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-8">
            <form id="loginForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">ईमेल *</label>
                    <input type="email" name="email" value="<EMAIL>" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">पासवर्ड *</label>
                    <input type="password" name="password" value="admin123" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="text-center">
                    <button type="submit" class="px-8 py-3 bg-red-600 text-white font-bold rounded-lg hover:bg-red-700 transition duration-300">
                        लॉगिन करें
                    </button>
                </div>
            </form>
        </div>
    </div>
    <script>
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const formData = new FormData(this);
        const data = Object.fromEntries(formData);

        fetch('/api/auth/login', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('लॉगिन सफल!');
                window.location.href = '/admin/dashboard';
            } else {
                alert('त्रुटि: ' + (data.error || 'लॉगिन में समस्या हुई'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('लॉगिन में त्रुटि हुई।');
        });
    });
    </script>
</body>
</html>`

	adminDashboardTemplate = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8">
        <h1 class="text-3xl font-bold mb-6 text-center text-blue-800">एडमिन डैशबोर्ड</h1>
        <div class="max-w-6xl mx-auto">
            <div class="grid md:grid-cols-4 gap-6 mb-8">
                <div class="bg-blue-500 text-white p-6 rounded-lg">
                    <h3 class="text-xl font-bold">कुल छात्र</h3>
                    <p class="text-3xl">{{.Stats.TotalStudents}}</p>
                </div>
                <div class="bg-green-500 text-white p-6 rounded-lg">
                    <h3 class="text-xl font-bold">विभाग</h3>
                    <p class="text-3xl">{{.Stats.TotalDepartments}}</p>
                </div>
                <div class="bg-yellow-500 text-white p-6 rounded-lg">
                    <h3 class="text-xl font-bold">लंबित नामांकन</h3>
                    <p class="text-3xl">{{.Stats.PendingEnrollments}}</p>
                </div>
                <div class="bg-purple-500 text-white p-6 rounded-lg">
                    <h3 class="text-xl font-bold">एडमिन</h3>
                    <p class="text-3xl">{{.Stats.TotalAdmins}}</p>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold mb-4">हाल की गतिविधि</h2>
                <p class="text-gray-600">डैशबोर्ड सफलतापूर्वक लोड हो गया है।</p>
            </div>
        </div>
    </div>
</body>
</html>`
)

func main() {
	// Load templates - try multiple paths and create fallback templates
	var templates *template.Template
	var err error

	templatePaths := []string{
		"backend/app/templates/*.html",
		"app/templates/*.html",
		"templates/*.html",
	}

	for _, path := range templatePaths {
		templates, err = template.ParseGlob(path)
		if err == nil {
			log.Printf("✅ Templates loaded from: %s", path)
			break
		}
	}

	if err != nil {
		log.Printf("⚠️  Warning: Failed to parse templates from all paths: %v", err)
		log.Println("Creating fallback templates...")

		// Create fallback templates
		templates = template.New("fallback")

		// Define fallback templates
		templates.New("student_registration.html").Parse(studentRegistrationTemplate)
		templates.New("student_enrollment.html").Parse(studentEnrollmentTemplate)
		templates.New("student_status.html").Parse(studentStatusTemplate)
		templates.New("admin_login.html").Parse(adminLoginTemplate)
		templates.New("admin_dashboard.html").Parse(adminDashboardTemplate)
		templates.New("index.html").Parse(indexTemplate)

		log.Printf("✅ Fallback templates created")
	}

	// Setup router
	r := mux.NewRouter()

	// Template routes
	r.HandleFunc("/", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{
				Title:       "University Information Management System",
				Departments: departments,
			}
			err := templates.ExecuteTemplate(w, "index.html", data)
			if err != nil {
				log.Printf("Template error: %v", err)
				// Fallback to simple HTML
				w.Header().Set("Content-Type", "text/html")
				w.Write([]byte(`
<!DOCTYPE html>
<html>
<head><title>University Information Management System</title></head>
<body>
<h1>🎓 University Information Management System</h1>
<p>Template error, but server is running!</p>
<ul>
<li><a href="/student/register">Student Registration</a></li>
<li><a href="/student/enrollment">Student Enrollment</a></li>
<li><a href="/student/status">Student Status</a></li>
<li><a href="/admin/login">Admin Login</a></li>
<li><a href="/api/departments">Departments API</a></li>
</ul>
</body>
</html>
				`))
				return
			}
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`
<!DOCTYPE html>
<html>
<head><title>University Information Management System</title></head>
<body>
<h1>🎓 University Information Management System</h1>
<p>New workflow demonstration server is running!</p>
<ul>
<li><a href="/student/register">Student Registration</a></li>
<li><a href="/student/enrollment">Student Enrollment</a></li>
<li><a href="/student/status">Student Status</a></li>
<li><a href="/admin/login">Admin Login</a></li>
<li><a href="/api/departments">Departments API</a></li>
</ul>
</body>
</html>
			`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/register", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Registration"}
			templates.ExecuteTemplate(w, "student_registration.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Registration</h1><p>Template not loaded, but API works at /api/students/register</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/enrollment", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Enrollment", Departments: departments}
			templates.ExecuteTemplate(w, "student_enrollment.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Enrollment</h1><p>Template not loaded, but API works at /api/students/enroll</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/status", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Status"}
			templates.ExecuteTemplate(w, "student_status.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Status</h1><p>Template not loaded, but API works at /api/students/status</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/admin/login", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Admin Login"}
			templates.ExecuteTemplate(w, "admin_login.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Admin Login</h1><p>Template not loaded, but API works at /api/auth/login</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/admin/dashboard", func(w http.ResponseWriter, req *http.Request) {
		stats := map[string]interface{}{
			"TotalStudents":      len(students),
			"TotalDepartments":   len(departments),
			"PendingEnrollments": countPendingEnrollments(),
			"TotalAdmins":        1,
		}

		if templates != nil {
			data := PageData{
				Title:       "Admin Dashboard",
				Departments: departments,
				Students:    students,
				Enrollments: enrollments,
				Stats:       stats,
			}
			templates.ExecuteTemplate(w, "admin_dashboard.html", data)
		} else {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]interface{}{
				"title":       "Admin Dashboard",
				"departments": departments,
				"students":    students,
				"enrollments": enrollments,
				"stats":       stats,
			})
		}
	}).Methods("GET")

	// API routes
	api := r.PathPrefix("/api").Subrouter()
	api.Use(jsonMiddleware)

	api.HandleFunc("/departments", getDepartments).Methods("GET")
	api.HandleFunc("/departments/{id}/subjects", getDepartmentSubjects).Methods("GET")
	api.HandleFunc("/students/register", registerStudent).Methods("POST")
	api.HandleFunc("/students/enroll", enrollStudent).Methods("POST")
	api.HandleFunc("/students/status", getStudentStatus).Methods("GET")
	api.HandleFunc("/auth/login", mockLogin).Methods("POST")

	// Health check
	r.HandleFunc("/health", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "healthy",
			"service": "university-management-system-test",
			"message": "New workflow demonstration server",
		})
	}).Methods("GET")

	port := getEnv("PORT", "8080")
	log.Printf("🎓 University Management Test Server starting on port %s", port)
	log.Printf("📱 Access the application at: http://localhost:%s", port)
	log.Printf("🔧 This is a demonstration server for the new workflow")
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// Middleware and handlers (same as before)
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		next.ServeHTTP(w, r)
	})
}

func getDepartments(w http.ResponseWriter, r *http.Request) {
	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    departments,
	})
}

func getDepartmentSubjects(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]

	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid department ID",
		})
		return
	}

	for _, dept := range departments {
		if dept.ID == int32(id) {
			sendJSON(w, http.StatusOK, map[string]interface{}{
				"success": true,
				"data":    dept.Subjects,
			})
			return
		}
	}

	sendJSON(w, http.StatusNotFound, map[string]interface{}{
		"success": false,
		"error":   "Department not found",
	})
}

func registerStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	name, ok := req["name"].(string)
	if !ok || name == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Name is required",
		})
		return
	}

	email, ok := req["email"].(string)
	if !ok || email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email is required",
		})
		return
	}

	// Check if student already exists
	for _, student := range students {
		if student.Email == email {
			sendJSON(w, http.StatusConflict, map[string]interface{}{
				"success": false,
				"error":   "Student with this email already exists",
			})
			return
		}
	}

	// Create new student
	student := Student{
		ID:           nextStudentID,
		Name:         name,
		Email:        email,
		MobileNumber: getStringFromMap(req, "mobile_number"),
		Status:       "registered",
		CreatedAt:    "2024-01-01T00:00:00Z",
	}
	nextStudentID++

	students = append(students, student)

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    student,
		"message": "Student registered successfully",
	})
}

func enrollStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	// Find student by email
	email := getStringFromMap(req, "student_email")
	var studentID int32
	var student *Student
	found := false
	for i := range students {
		if students[i].Email == email {
			studentID = students[i].ID
			student = &students[i]
			found = true
			break
		}
	}

	if !found {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found. Please register first.",
		})
		return
	}

	// Create enrollment
	deptID := int32(getIntFromMap(req, "department_id"))
	enrollment := Enrollment{
		ID:           nextEnrollmentID,
		StudentID:    studentID,
		DepartmentID: deptID,
		Subject:      getStringFromMap(req, "subject"),
		Session:      getStringFromMap(req, "session"),
		Status:       "pending",
		Student:      student,
		CreatedAt:    "2024-01-01T00:00:00Z",
	}

	// Add department info
	for _, dept := range departments {
		if dept.ID == deptID {
			enrollment.Department = &dept
			break
		}
	}

	nextEnrollmentID++
	enrollments = append(enrollments, enrollment)

	// Update student status
	for i := range students {
		if students[i].ID == studentID {
			students[i].Status = "enrolled"
			break
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    enrollment,
		"message": "Student enrolled successfully",
	})
}

func getStudentStatus(w http.ResponseWriter, r *http.Request) {
	email := r.URL.Query().Get("email")
	if email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email parameter required",
		})
		return
	}

	var student *Student
	for i := range students {
		if students[i].Email == email {
			student = &students[i]
			break
		}
	}

	if student == nil {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found",
		})
		return
	}

	var studentEnrollments []Enrollment
	for _, enrollment := range enrollments {
		if enrollment.StudentID == student.ID {
			studentEnrollments = append(studentEnrollments, enrollment)
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"student":     student,
			"enrollments": studentEnrollments,
		},
	})
}

func mockLogin(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	email := getStringFromMap(req, "email")
	password := getStringFromMap(req, "password")

	if email == "<EMAIL>" && password == "admin123" {
		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data": map[string]interface{}{
				"token": "mock-jwt-token",
				"user": map[string]interface{}{
					"id":    1,
					"email": email,
					"role":  "super_admin",
				},
			},
			"message": "Login successful",
		})
	} else {
		sendJSON(w, http.StatusUnauthorized, map[string]interface{}{
			"success": false,
			"error":   "Invalid credentials",
		})
	}
}

// Helper functions
func sendJSON(w http.ResponseWriter, status int, data interface{}) {
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func getIntFromMap(m map[string]interface{}, key string) int {
	if val, ok := m[key].(float64); ok {
		return int(val)
	}
	return 0
}

func countPendingEnrollments() int {
	count := 0
	for _, enrollment := range enrollments {
		if enrollment.Status == "pending" {
			count++
		}
	}
	return count
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
