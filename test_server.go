package main

import (
	"encoding/json"
	"html/template"
	"log"
	"net/http"
	"os"
	"strconv"

	"github.com/gorilla/mux"
)

// Simple models for testing the new workflow
type Department struct {
	ID          int32    `json:"id"`
	Name        string   `json:"name"`
	Code        string   `json:"code"`
	Description string   `json:"description"`
	Subjects    []string `json:"subjects"`
}

type Student struct {
	ID           int32  `json:"id"`
	Name         string `json:"name"`
	Email        string `json:"email"`
	MobileNumber string `json:"mobile_number"`
	Status       string `json:"status"`
	CreatedAt    string `json:"created_at"`
}

type Enrollment struct {
	ID           int32       `json:"id"`
	StudentID    int32       `json:"student_id"`
	DepartmentID int32       `json:"department_id"`
	Subject      string      `json:"subject"`
	Session      string      `json:"session"`
	Status       string      `json:"status"`
	Student      *Student    `json:"student,omitempty"`
	Department   *Department `json:"department,omitempty"`
	CreatedAt    string      `json:"created_at"`
}

// Mock data storage
var departments = []Department{
	{
		ID:          1,
		Name:        "Science Department",
		Code:        "SCI",
		Description: "Physics, Chemistry, Mathematics, Botany, Zoology",
		Subjects:    []string{"Physics", "Chemistry", "Mathematics", "Botany", "Zoology"},
	},
	{
		ID:          2,
		Name:        "Language Department",
		Code:        "LANG",
		Description: "Hindi, English, Urdu, Sanskrit",
		Subjects:    []string{"Hindi", "English", "Urdu", "Sanskrit"},
	},
	{
		ID:          3,
		Name:        "Social Science Department",
		Code:        "SOC",
		Description: "History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM",
		Subjects:    []string{"History", "Political Science", "Economics", "Sociology", "Philosophy", "Psychology", "AIHC", "IRPM"},
	},
}

var students = []Student{}
var enrollments = []Enrollment{}
var nextStudentID int32 = 1
var nextEnrollmentID int32 = 1

// Template data structure
type PageData struct {
	Title       string
	Departments []Department
	Students    []Student
	Enrollments []Enrollment
	Stats       map[string]interface{}
}

func main() {
	// Load templates - try multiple paths
	var templates *template.Template
	var err error

	templatePaths := []string{
		"backend/app/templates/*.html",
		"app/templates/*.html",
		"templates/*.html",
	}

	for _, path := range templatePaths {
		templates, err = template.ParseGlob(path)
		if err == nil {
			log.Printf("✅ Templates loaded from: %s", path)
			break
		}
	}

	if err != nil {
		log.Printf("⚠️  Warning: Failed to parse templates from all paths: %v", err)
		log.Println("Continuing without templates - API endpoints will still work...")
	}

	// Setup router
	r := mux.NewRouter()

	// Template routes
	r.HandleFunc("/", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{
				Title:       "University Information Management System",
				Departments: departments,
			}
			err := templates.ExecuteTemplate(w, "simple_index.html", data)
			if err != nil {
				log.Printf("Template error: %v", err)
				// Fallback to simple HTML
				w.Header().Set("Content-Type", "text/html")
				w.Write([]byte(`
<!DOCTYPE html>
<html>
<head><title>University Information Management System</title></head>
<body>
<h1>🎓 University Information Management System</h1>
<p>Template error, but server is running!</p>
<ul>
<li><a href="/student/register">Student Registration</a></li>
<li><a href="/student/enrollment">Student Enrollment</a></li>
<li><a href="/student/status">Student Status</a></li>
<li><a href="/admin/login">Admin Login</a></li>
<li><a href="/api/departments">Departments API</a></li>
</ul>
</body>
</html>
				`))
				return
			}
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`
<!DOCTYPE html>
<html>
<head><title>University Information Management System</title></head>
<body>
<h1>🎓 University Information Management System</h1>
<p>New workflow demonstration server is running!</p>
<ul>
<li><a href="/student/register">Student Registration</a></li>
<li><a href="/student/enrollment">Student Enrollment</a></li>
<li><a href="/student/status">Student Status</a></li>
<li><a href="/admin/login">Admin Login</a></li>
<li><a href="/api/departments">Departments API</a></li>
</ul>
</body>
</html>
			`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/register", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Registration"}
			templates.ExecuteTemplate(w, "student_registration.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Registration</h1><p>Template not loaded, but API works at /api/students/register</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/enrollment", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Enrollment", Departments: departments}
			templates.ExecuteTemplate(w, "student_enrollment.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Enrollment</h1><p>Template not loaded, but API works at /api/students/enroll</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/student/status", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Student Status"}
			templates.ExecuteTemplate(w, "student_status.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Student Status</h1><p>Template not loaded, but API works at /api/students/status</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/admin/login", func(w http.ResponseWriter, req *http.Request) {
		if templates != nil {
			data := PageData{Title: "Admin Login"}
			templates.ExecuteTemplate(w, "admin_login.html", data)
		} else {
			w.Header().Set("Content-Type", "text/html")
			w.Write([]byte(`<h1>Admin Login</h1><p>Template not loaded, but API works at /api/auth/login</p>`))
		}
	}).Methods("GET")

	r.HandleFunc("/admin/dashboard", func(w http.ResponseWriter, req *http.Request) {
		stats := map[string]interface{}{
			"TotalStudents":      len(students),
			"TotalDepartments":   len(departments),
			"PendingEnrollments": countPendingEnrollments(),
			"TotalAdmins":        1,
		}

		if templates != nil {
			data := PageData{
				Title:       "Admin Dashboard",
				Departments: departments,
				Students:    students,
				Enrollments: enrollments,
				Stats:       stats,
			}
			templates.ExecuteTemplate(w, "admin_dashboard.html", data)
		} else {
			w.Header().Set("Content-Type", "application/json")
			json.NewEncoder(w).Encode(map[string]interface{}{
				"title":       "Admin Dashboard",
				"departments": departments,
				"students":    students,
				"enrollments": enrollments,
				"stats":       stats,
			})
		}
	}).Methods("GET")

	// API routes
	api := r.PathPrefix("/api").Subrouter()
	api.Use(jsonMiddleware)

	api.HandleFunc("/departments", getDepartments).Methods("GET")
	api.HandleFunc("/departments/{id}/subjects", getDepartmentSubjects).Methods("GET")
	api.HandleFunc("/students/register", registerStudent).Methods("POST")
	api.HandleFunc("/students/enroll", enrollStudent).Methods("POST")
	api.HandleFunc("/students/status", getStudentStatus).Methods("GET")
	api.HandleFunc("/auth/login", mockLogin).Methods("POST")

	// Health check
	r.HandleFunc("/health", func(w http.ResponseWriter, req *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusOK)
		json.NewEncoder(w).Encode(map[string]string{
			"status":  "healthy",
			"service": "university-management-system-test",
			"message": "New workflow demonstration server",
		})
	}).Methods("GET")

	port := getEnv("PORT", "8080")
	log.Printf("🎓 University Management Test Server starting on port %s", port)
	log.Printf("📱 Access the application at: http://localhost:%s", port)
	log.Printf("🔧 This is a demonstration server for the new workflow")
	log.Fatal(http.ListenAndServe(":"+port, r))
}

// Middleware and handlers (same as before)
func jsonMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		next.ServeHTTP(w, r)
	})
}

func getDepartments(w http.ResponseWriter, r *http.Request) {
	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    departments,
	})
}

func getDepartmentSubjects(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]

	id, err := strconv.ParseInt(idStr, 10, 32)
	if err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid department ID",
		})
		return
	}

	for _, dept := range departments {
		if dept.ID == int32(id) {
			sendJSON(w, http.StatusOK, map[string]interface{}{
				"success": true,
				"data":    dept.Subjects,
			})
			return
		}
	}

	sendJSON(w, http.StatusNotFound, map[string]interface{}{
		"success": false,
		"error":   "Department not found",
	})
}

func registerStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	name, ok := req["name"].(string)
	if !ok || name == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Name is required",
		})
		return
	}

	email, ok := req["email"].(string)
	if !ok || email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email is required",
		})
		return
	}

	// Check if student already exists
	for _, student := range students {
		if student.Email == email {
			sendJSON(w, http.StatusConflict, map[string]interface{}{
				"success": false,
				"error":   "Student with this email already exists",
			})
			return
		}
	}

	// Create new student
	student := Student{
		ID:           nextStudentID,
		Name:         name,
		Email:        email,
		MobileNumber: getStringFromMap(req, "mobile_number"),
		Status:       "registered",
		CreatedAt:    "2024-01-01T00:00:00Z",
	}
	nextStudentID++

	students = append(students, student)

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    student,
		"message": "Student registered successfully",
	})
}

func enrollStudent(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	// Find student by email
	email := getStringFromMap(req, "student_email")
	var studentID int32
	var student *Student
	found := false
	for i := range students {
		if students[i].Email == email {
			studentID = students[i].ID
			student = &students[i]
			found = true
			break
		}
	}

	if !found {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found. Please register first.",
		})
		return
	}

	// Create enrollment
	deptID := int32(getIntFromMap(req, "department_id"))
	enrollment := Enrollment{
		ID:           nextEnrollmentID,
		StudentID:    studentID,
		DepartmentID: deptID,
		Subject:      getStringFromMap(req, "subject"),
		Session:      getStringFromMap(req, "session"),
		Status:       "pending",
		Student:      student,
		CreatedAt:    "2024-01-01T00:00:00Z",
	}

	// Add department info
	for _, dept := range departments {
		if dept.ID == deptID {
			enrollment.Department = &dept
			break
		}
	}

	nextEnrollmentID++
	enrollments = append(enrollments, enrollment)

	// Update student status
	for i := range students {
		if students[i].ID == studentID {
			students[i].Status = "enrolled"
			break
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data":    enrollment,
		"message": "Student enrolled successfully",
	})
}

func getStudentStatus(w http.ResponseWriter, r *http.Request) {
	email := r.URL.Query().Get("email")
	if email == "" {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Email parameter required",
		})
		return
	}

	var student *Student
	for i := range students {
		if students[i].Email == email {
			student = &students[i]
			break
		}
	}

	if student == nil {
		sendJSON(w, http.StatusNotFound, map[string]interface{}{
			"success": false,
			"error":   "Student not found",
		})
		return
	}

	var studentEnrollments []Enrollment
	for _, enrollment := range enrollments {
		if enrollment.StudentID == student.ID {
			studentEnrollments = append(studentEnrollments, enrollment)
		}
	}

	sendJSON(w, http.StatusOK, map[string]interface{}{
		"success": true,
		"data": map[string]interface{}{
			"student":     student,
			"enrollments": studentEnrollments,
		},
	})
}

func mockLogin(w http.ResponseWriter, r *http.Request) {
	var req map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		sendJSON(w, http.StatusBadRequest, map[string]interface{}{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}

	email := getStringFromMap(req, "email")
	password := getStringFromMap(req, "password")

	if email == "<EMAIL>" && password == "admin123" {
		sendJSON(w, http.StatusOK, map[string]interface{}{
			"success": true,
			"data": map[string]interface{}{
				"token": "mock-jwt-token",
				"user": map[string]interface{}{
					"id":    1,
					"email": email,
					"role":  "super_admin",
				},
			},
			"message": "Login successful",
		})
	} else {
		sendJSON(w, http.StatusUnauthorized, map[string]interface{}{
			"success": false,
			"error":   "Invalid credentials",
		})
	}
}

// Helper functions
func sendJSON(w http.ResponseWriter, status int, data interface{}) {
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(data)
}

func getStringFromMap(m map[string]interface{}, key string) string {
	if val, ok := m[key].(string); ok {
		return val
	}
	return ""
}

func getIntFromMap(m map[string]interface{}, key string) int {
	if val, ok := m[key].(float64); ok {
		return int(val)
	}
	return 0
}

func countPendingEnrollments() int {
	count := 0
	for _, enrollment := range enrollments {
		if enrollment.Status == "pending" {
			count++
		}
	}
	return count
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
