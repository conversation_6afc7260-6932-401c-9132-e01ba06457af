#!/usr/bin/env python3
"""
Advanced Test Data Generator for University Exam Form Management System
Generates realistic test data for comprehensive testing scenarios
"""

import json
import random
from datetime import datetime, timedelta
from typing import List, Dict, Any

class TestDataGenerator:
    def __init__(self):
        self.categories = ['UR', 'SC', 'ST', 'EBC', 'BC', 'EWS']
        self.genders = ['M', 'F']
        self.subjects = [
            'Computer Science', 'Mathematics', 'Physics', 'Chemistry', 
            'Biology', 'History', 'Geography', 'Political Science', 
            'Economics', 'Sociology', 'English', 'Hindi'
        ]
        self.first_names_male = [
            '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', 
            '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'
        ]
        self.first_names_female = [
            '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>'
        ]
        self.last_names = [
            '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>'
        ]
        self.father_prefixes = ['<PERSON>esh', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>j', '<PERSON>h']
        self.mother_prefixes = ['<PERSON>ita', 'Meera', '<PERSON>a', 'Rekha', '<PERSON>ta']
        
    def generate_name(self, gender: str) -> tuple:
        """Generate realistic Indian names"""
        if gender == 'M':
            first_name = random.choice(self.first_names_male)
            last_name = random.choice([n for n in self.last_names if n != 'Devi' and n != 'Kumari'])
        else:
            first_name = random.choice(self.first_names_female)
            if random.random() < 0.7:  # 70% chance of Kumari suffix
                last_name = 'Kumari'
            else:
                last_name = random.choice([n for n in self.last_names if n not in ['Kumar', 'Singh']])
        
        middle_name = random.choice(self.last_names[:-2])  # Exclude Devi and Kumari
        full_name = f"{first_name} {middle_name} {last_name}" if random.random() < 0.8 else f"{first_name} {last_name}"
        
        return full_name, first_name, middle_name, last_name
    
    def generate_parents_names(self, last_name: str) -> tuple:
        """Generate father and mother names"""
        father_first = random.choice(self.father_prefixes)
        father_name = f"{father_first} {last_name}"
        
        mother_first = random.choice(self.mother_prefixes)
        mother_name = f"{mother_first} Devi"
        
        return father_name, mother_name
    
    def generate_contact_info(self, base_number: int) -> dict:
        """Generate contact information"""
        mobile = f"9{base_number:09d}"
        aadhar = f"{base_number:012d}"
        abc_id = f"ABC{base_number:09d}"
        pincode = f"8132{base_number % 100:02d}"
        
        return {
            'mobile_number': mobile,
            'aadhar_number': aadhar,
            'abc_id': abc_id,
            'aadhar_mobile': mobile,
            'pincode': pincode
        }
    
    def generate_academic_info(self, student_id: int) -> dict:
        """Generate academic information"""
        class_roll = f"{student_id:03d}"
        university_roll = f"202300{student_id:04d}"
        registration = f"REG2023{student_id:03d}"
        subject = random.choice(self.subjects)
        
        return {
            'class_roll_number': class_roll,
            'university_roll_number': university_roll,
            'registration_number': registration,
            'subject': subject,
            'session': '2023-24',
            'college': 'SSV College'
        }
    
    def generate_birth_date(self) -> str:
        """Generate realistic birth date for college students"""
        start_date = datetime(1998, 1, 1)
        end_date = datetime(2003, 12, 31)
        
        time_between = end_date - start_date
        days_between = time_between.days
        random_days = random.randrange(days_between)
        
        birth_date = start_date + timedelta(days=random_days)
        return birth_date.strftime('%Y-%m-%d')
    
    def generate_students(self, count: int) -> List[Dict[str, Any]]:
        """Generate specified number of test students"""
        students = []
        
        for i in range(1, count + 1):
            gender = random.choice(self.genders)
            category = random.choice(self.categories)
            
            # Generate names
            full_name, first_name, middle_name, last_name = self.generate_name(gender)
            father_name, mother_name = self.generate_parents_names(last_name)
            
            # Generate contact info
            contact_info = self.generate_contact_info(123456789 + i)
            
            # Generate academic info
            academic_info = self.generate_academic_info(i)
            
            # Generate email
            email_name = first_name.lower()
            if middle_name:
                email_name += f".{middle_name.lower()}"
            email = f"{email_name}@test{i:03d}.com"
            
            student = {
                'id': i,
                'name': full_name,
                'email': email,
                'date_of_birth': self.generate_birth_date(),
                'gender': gender,
                'category': category,
                'father_name': father_name,
                'mother_name': mother_name,
                'state': 'BIHAR',
                **contact_info,
                **academic_info
            }
            
            students.append(student)
        
        return students
    
    def generate_exam_forms(self, students: List[Dict[str, Any]], percentage: float = 0.6) -> List[Dict[str, Any]]:
        """Generate exam forms for a percentage of students"""
        exam_forms = []
        selected_students = random.sample(students, int(len(students) * percentage))
        
        # Fee structure based on category
        fee_structure = {
            'UR': {'regular': 500, 'ex': 750},
            'EWS': {'regular': 375, 'ex': 562.5},
            'SC': {'regular': 250, 'ex': 375},
            'ST': {'regular': 250, 'ex': 375},
            'EBC': {'regular': 375, 'ex': 562.5},
            'BC': {'regular': 375, 'ex': 562.5}
        }
        
        for student in selected_students:
            is_regular = random.choice([True, True, True, False])  # 75% regular students
            
            # Random paper selection
            papers = {
                'paper_i': random.choice([True, False]),
                'paper_ii': random.choice([True, False]),
                'paper_iii': random.choice([True, False]),
                'paper_iv': random.choice([True, False])
            }
            
            # Ensure at least one paper is selected
            if not any(papers.values()):
                papers['paper_i'] = True
            
            # Calculate fee
            base_fee = fee_structure[student['category']]['regular' if is_regular else 'ex']
            paper_count = sum(papers.values())
            total_fee = base_fee * paper_count
            
            exam_form = {
                'roll_number': student['university_roll_number'],
                'category': student['category'],
                'is_regular': is_regular,
                'fee': total_fee,
                **papers
            }
            
            exam_forms.append(exam_form)
        
        return exam_forms
    
    def generate_test_scenarios(self) -> Dict[str, Any]:
        """Generate comprehensive test scenarios"""
        # Generate students
        students = self.generate_students(50)
        
        # Generate exam forms
        exam_forms = self.generate_exam_forms(students)
        
        # Generate test scenarios
        scenarios = {
            'valid_logins': [
                {'username': 'test_admin', 'password': 'test123'},
                {'username': 'admin', 'password': 'abc'}
            ],
            'invalid_logins': [
                {'username': 'wrong_user', 'password': 'wrong_pass'},
                {'username': 'test_admin', 'password': 'wrong_pass'},
                {'username': '', 'password': ''},
            ],
            'search_tests': [
                {'type': 'university_roll', 'value': students[0]['university_roll_number']},
                {'type': 'abc_id', 'value': students[1]['abc_id']},
                {'type': 'aadhar', 'value': students[2]['aadhar_number']},
                {'type': 'university_roll', 'value': 'NONEXISTENT123'},
            ],
            'duplicate_tests': [
                {
                    'field': 'email',
                    'value': students[0]['email'],
                    'new_student': {**students[0], 'name': 'Duplicate Test', 'university_roll_number': '2023999999'}
                },
                {
                    'field': 'aadhar_number',
                    'value': students[1]['aadhar_number'],
                    'new_student': {**students[1], 'name': 'Duplicate Aadhar', 'email': '<EMAIL>'}
                }
            ]
        }
        
        return {
            'students': students,
            'exam_forms': exam_forms,
            'test_scenarios': scenarios,
            'metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_students': len(students),
                'total_exam_forms': len(exam_forms),
                'categories_distribution': {cat: len([s for s in students if s['category'] == cat]) for cat in self.categories}
            }
        }

def main():
    """Generate and save test data"""
    generator = TestDataGenerator()
    test_data = generator.generate_test_scenarios()
    
    # Save to JSON file
    with open('tests/generated_test_data.json', 'w') as f:
        json.dump(test_data, f, indent=2)
    
    print(f"Generated test data:")
    print(f"  - {test_data['metadata']['total_students']} students")
    print(f"  - {test_data['metadata']['total_exam_forms']} exam forms")
    print(f"  - Category distribution: {test_data['metadata']['categories_distribution']}")
    print(f"  - Saved to: tests/generated_test_data.json")

if __name__ == "__main__":
    main()
