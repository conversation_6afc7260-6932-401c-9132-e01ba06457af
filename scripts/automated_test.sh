#!/bin/bash

# Automated Testing Script for University Exam Form Management System
# This script creates a fresh PostgreSQL instance, tests all functionality, and cleans up

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test configuration
TEST_DB_HOST="localhost"
TEST_DB_PORT="5434"
TEST_DB_USER="balena_test"
TEST_DB_PASSWORD="test123"
TEST_DB_NAME="balena_test"
TEST_APP_PORT="8001"
TEST_APP_URL="http://localhost:${TEST_APP_PORT}"

# Test credentials
TEST_USERNAME="test_admin"
TEST_PASSWORD="test123"

# Counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_test_header() {
    echo -e "\n${BLUE}========================================${NC}"
    echo -e "${BLUE} $1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to run a test
run_test() {
    local test_name="$1"
    local test_command="$2"
    local expected_result="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    print_status "Running test: $test_name"
    
    if eval "$test_command"; then
        if [ -n "$expected_result" ]; then
            # Additional validation if expected result is provided
            if eval "$expected_result"; then
                print_success "✅ $test_name - PASSED"
                PASSED_TESTS=$((PASSED_TESTS + 1))
                return 0
            else
                print_error "❌ $test_name - FAILED (validation failed)"
                FAILED_TESTS=$((FAILED_TESTS + 1))
                return 1
            fi
        else
            print_success "✅ $test_name - PASSED"
            PASSED_TESTS=$((PASSED_TESTS + 1))
            return 0
        fi
    else
        print_error "❌ $test_name - FAILED"
        FAILED_TESTS=$((FAILED_TESTS + 1))
        return 1
    fi
}

# Function to wait for service to be ready
wait_for_service() {
    local service_name="$1"
    local check_command="$2"
    local max_attempts=30
    local attempt=1
    
    print_status "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if eval "$check_command" >/dev/null 2>&1; then
            print_success "$service_name is ready!"
            return 0
        fi
        
        print_status "Attempt $attempt/$max_attempts - $service_name not ready yet..."
        sleep 2
        attempt=$((attempt + 1))
    done
    
    print_error "$service_name failed to start within expected time"
    return 1
}

# Function to cleanup test environment
cleanup() {
    print_test_header "CLEANING UP TEST ENVIRONMENT"
    
    print_status "Stopping test containers..."
    docker-compose -f docker-compose.test.yml down -v --remove-orphans 2>/dev/null || true
    
    print_status "Removing test volumes..."
    docker volume rm exam-form_test_db_data 2>/dev/null || true
    
    print_status "Removing test networks..."
    docker network rm exam-form_test-network 2>/dev/null || true
    
    print_status "Cleaning up temporary files..."
    rm -f test_cookies.txt test_output.html 2>/dev/null || true
    
    print_success "Cleanup completed!"
}

# Function to setup test environment
setup_test_environment() {
    print_test_header "SETTING UP TEST ENVIRONMENT"
    
    # Cleanup any existing test environment
    cleanup
    
    print_status "Starting test PostgreSQL and application..."
    docker-compose -f docker-compose.test.yml up -d
    
    # Wait for PostgreSQL to be ready
    wait_for_service "PostgreSQL" "docker exec exam-form-test-postgres pg_isready -U $TEST_DB_USER -d $TEST_DB_NAME"
    
    # Wait for application to be ready
    wait_for_service "Application" "curl -f $TEST_APP_URL/health"
    
    print_success "Test environment setup completed!"
}

# Function to test database connectivity
test_database() {
    print_test_header "TESTING DATABASE CONNECTIVITY"
    
    run_test "Database Connection" \
        "docker exec -e PGPASSWORD=$TEST_DB_PASSWORD exam-form-test-postgres psql -U $TEST_DB_USER -d $TEST_DB_NAME -c 'SELECT 1;' >/dev/null"
    
    run_test "Students Table Exists" \
        "docker exec -e PGPASSWORD=$TEST_DB_PASSWORD exam-form-test-postgres psql -U $TEST_DB_USER -d $TEST_DB_NAME -c 'SELECT COUNT(*) FROM students;' >/dev/null"
    
    run_test "Exam Forms Table Exists" \
        "docker exec -e PGPASSWORD=$TEST_DB_PASSWORD exam-form-test-postgres psql -U $TEST_DB_USER -d $TEST_DB_NAME -c 'SELECT COUNT(*) FROM exam_form_degree_iii;' >/dev/null"
    
    # Check test data
    local student_count=$(docker exec -e PGPASSWORD=$TEST_DB_PASSWORD exam-form-test-postgres psql -U $TEST_DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM students;" | tr -d ' ')
    run_test "Test Data Loaded (Students)" \
        "[ '$student_count' -gt '0' ]"
    
    local exam_count=$(docker exec -e PGPASSWORD=$TEST_DB_PASSWORD exam-form-test-postgres psql -U $TEST_DB_USER -d $TEST_DB_NAME -t -c "SELECT COUNT(*) FROM exam_form_degree_iii;" | tr -d ' ')
    run_test "Test Data Loaded (Exam Forms)" \
        "[ '$exam_count' -gt '0' ]"
}

# Function to test application endpoints
test_application() {
    print_test_header "TESTING APPLICATION ENDPOINTS"
    
    # Test home page
    run_test "Home Page Access" \
        "curl -s -o /dev/null -w '%{http_code}' $TEST_APP_URL/ | grep -q '200'"
    
    # Test login
    run_test "Admin Login" \
        "curl -X POST $TEST_APP_URL/login -H 'Content-Type: application/x-www-form-urlencoded' -d 'username=$TEST_USERNAME&password=$TEST_PASSWORD' -c test_cookies.txt -s -o /dev/null -w '%{http_code}' | grep -q '303'"
    
    # Test authenticated access
    run_test "Authenticated Access" \
        "curl -b test_cookies.txt -s -o /dev/null -w '%{http_code}' $TEST_APP_URL/form | grep -q '200'"
}

# Function to test CRUD operations
test_crud_operations() {
    print_test_header "TESTING CRUD OPERATIONS"
    
    # Test student creation
    run_test "Student Registration" \
        "curl -X POST $TEST_APP_URL/form -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'name=Test Student&email=<EMAIL>&date_of_birth=2000-01-01&gender=M&category=UR&mobile_number=9999999999&college=SSV College&session=2023-24&subject=Test Subject&class_roll_number=999&university_roll_number=2023999999&registration_number=REGTEST999&father_name=Test Father&mother_name=Test Mother&pincode=999999&state=BIHAR&aadhar_number=************&abc_id=ABCTEST99999&aadhar_mobile=9999999999' -s -o /dev/null -w '%{http_code}' | grep -q '303'"
    
    # Test student search
    run_test "Student Search" \
        "curl -X POST $TEST_APP_URL/update -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'search_type=university_roll&search_value=2023001001' -s -o test_output.html -w '%{http_code}' | grep -q '200'"
    
    # Test exam form generation
    run_test "Exam Form Generation" \
        "curl -X POST $TEST_APP_URL/generate -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'roll_number=2023001002&category=UR&exam_type=regular&paper_1=on&paper_2=on' -s -o /dev/null -w '%{http_code}' | grep -q '200'"
}

# Function to test different categories and fee calculations
test_fee_calculations() {
    print_test_header "TESTING FEE CALCULATIONS"
    
    # Test UR category
    run_test "UR Category Fee Calculation" \
        "curl -X POST $TEST_APP_URL/generate -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'roll_number=2023001011&category=UR&exam_type=regular&paper_1=on&paper_2=on' -s -o /dev/null -w '%{http_code}' | grep -q '200'"
    
    # Test SC category
    run_test "SC Category Fee Calculation" \
        "curl -X POST $TEST_APP_URL/generate -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'roll_number=2023001003&category=SC&exam_type=regular&paper_1=on&paper_2=on&paper_3=on' -s -o /dev/null -w '%{http_code}' | grep -q '200'"
    
    # Test EBC category
    run_test "EBC Category Fee Calculation" \
        "curl -X POST $TEST_APP_URL/generate -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'roll_number=2023001007&category=EBC&exam_type=regular&paper_1=on' -s -o /dev/null -w '%{http_code}' | grep -q '200'"
}

# Function to test error scenarios
test_error_scenarios() {
    print_test_header "TESTING ERROR SCENARIOS"
    
    # Test invalid login
    run_test "Invalid Login" \
        "curl -X POST $TEST_APP_URL/login -H 'Content-Type: application/x-www-form-urlencoded' -d 'username=invalid&password=invalid' -s -o /dev/null -w '%{http_code}' | grep -q '401'"
    
    # Test duplicate email
    run_test "Duplicate Email Validation" \
        "curl -X POST $TEST_APP_URL/form -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'name=Duplicate Test&email=<EMAIL>&date_of_birth=2000-01-01&gender=M&category=UR&mobile_number=8888888888&college=SSV College&session=2023-24&subject=Test Subject&class_roll_number=888&university_roll_number=2023888888&registration_number=REGDUP888&father_name=Test Father&mother_name=Test Mother&pincode=888888&state=BIHAR&aadhar_number=************&abc_id=ABCDUP888888&aadhar_mobile=8888888888' -s -o /dev/null -w '%{http_code}' | grep -q '400\|500'"
    
    # Test search for non-existent student
    run_test "Non-existent Student Search" \
        "curl -X POST $TEST_APP_URL/update -H 'Content-Type: application/x-www-form-urlencoded' -b test_cookies.txt -d 'search_type=university_roll&search_value=9999999999' -s -o /dev/null -w '%{http_code}' | grep -q '200\|404'"
}

# Function to generate test report
generate_report() {
    print_test_header "TEST EXECUTION SUMMARY"
    
    echo -e "\n📊 ${BLUE}Test Results:${NC}"
    echo -e "   Total Tests: ${TOTAL_TESTS}"
    echo -e "   ${GREEN}Passed: ${PASSED_TESTS}${NC}"
    echo -e "   ${RED}Failed: ${FAILED_TESTS}${NC}"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    echo -e "   Success Rate: ${success_rate}%"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        echo -e "\n🎉 ${GREEN}ALL TESTS PASSED! The application is working correctly.${NC}"
        return 0
    else
        echo -e "\n⚠️  ${YELLOW}Some tests failed. Please check the output above for details.${NC}"
        return 1
    fi
}

# Main execution function
main() {
    echo -e "${BLUE}🧪 University Exam Form Management System - Automated Testing${NC}"
    echo -e "${BLUE}================================================================${NC}\n"
    
    # Trap to ensure cleanup on exit
    trap cleanup EXIT
    
    # Setup test environment
    setup_test_environment
    
    # Run all tests
    test_database
    test_application
    test_crud_operations
    test_fee_calculations
    test_error_scenarios
    
    # Generate report
    generate_report
}

# Check if script is being run directly
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
