-- Insert comprehensive test data for all scenarios
-- This script populates the database with realistic test data

-- Insert test students covering all categories and scenarios
INSERT INTO students (
    name, email, date_of_birth, gender, category, mobile_number, 
    session, subject, class_roll_number, university_roll_number, 
    registration_number, father_name, mother_name, pincode, state, 
    aadhar_number, abc_id, aadhar_mobile
) VALUES 
-- UR Category Students
('<PERSON><PERSON>', '<EMAIL>', '2000-01-15', 'M', 'UR', '9876543210', 
 '2023-24', 'Computer Science', '101', '2023001001', 'REG2023001', 
 '<PERSON><PERSON>', '<PERSON><PERSON>', '813203', 'BIHAR', '123456789012', 'ABC123456789', '9876543210'),

('<PERSON><PERSON><PERSON>', '<EMAIL>', '2001-02-20', 'F', 'UR', '9876543211', 
 '2023-24', 'Mathematics', '102', '2023001002', 'REG2023002', 
 '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '813204', 'BIHA<PERSON>', '123456789013', 'ABC123456790', '9876543211'),

-- SC Category Students  
('<PERSON>riya <PERSON>i', 'priya.kuma<PERSON>@test.com', '2001-03-22', 'F', '<PERSON>', '8765432109', 
 '2023-24', 'Physics', '103', '2023001003', 'REG2023003', 
 'Suresh Kumar', 'Meera Devi', '813222', 'BIHAR', '************', 'ABC234567890', '8765432109'),

('Ravi Kumar', '<EMAIL>', '1999-12-10', 'M', 'SC', '8765432108', 
 '2023-24', 'Chemistry', '104', '2023001004', 'REG2023004', 
 'Ramesh Kumar', 'Sita Devi', '813223', 'BIHAR', '234567890124', 'ABC234567891', '8765432108'),

-- ST Category Students
('Anita Kumari Minj', '<EMAIL>', '2000-11-05', 'F', 'ST', '6543210987', 
 '2023-24', 'Biology', '105', '2023001005', 'REG2023005', 
 'Sunil Minj', 'Kamala Devi', '813205', 'BIHAR', '456789012345', 'ABC456789012', '6543210987'),

('Arjun Oraon', '<EMAIL>', '2000-08-18', 'M', 'ST', '6543210986', 
 '2023-24', 'Geography', '106', '2023001006', 'REG2023006', 
 'Birsa Oraon', 'Sushila Oraon', '813206', 'BIHAR', '456789012346', 'ABC456789013', '6543210986'),

-- EBC Category Students
('Amit Kumar Yadav', '<EMAIL>', '1999-07-10', 'M', 'EBC', '7654321098', 
 '2023-24', 'History', '107', '2023001007', 'REG2023007', 
 'Ramesh Yadav', 'Sita Devi', '813204', 'BIHAR', '345678901234', 'ABC345678901', '7654321098'),

('Sunita Kumari', '<EMAIL>', '2001-04-25', 'F', 'EBC', '7654321097', 
 '2023-24', 'Political Science', '108', '2023001008', 'REG2023008', 
 'Manoj Yadav', 'Rekha Devi', '813207', 'BIHAR', '345678901235', 'ABC345678902', '7654321097'),

-- BC Category Students
('Vikash Kumar Mandal', '<EMAIL>', '2001-09-18', 'M', 'BC', '5432109876', 
 '2023-24', 'Economics', '109', '2023001009', 'REG2023009', 
 'Manoj Mandal', 'Rekha Devi', '813206', 'BIHAR', '567890123456', 'ABC567890123', '5432109876'),

('Pooja Kumari', '<EMAIL>', '2000-06-12', 'F', 'BC', '5432109875', 
 '2023-24', 'Sociology', '110', '2023001010', 'REG2023010', 
 'Santosh Mandal', 'Geeta Devi', '813208', 'BIHAR', '567890123457', 'ABC567890124', '5432109875'),

-- EWS Category Students
('Deepak Kumar', '<EMAIL>', '2000-03-08', 'M', 'EWS', '4321098765', 
 '2023-24', 'English', '111', '2023001011', 'REG2023011', 
 'Rajesh Kumar', 'Sunita Devi', '813209', 'BIHAR', '678901234567', 'ABC678901234', '4321098765'),

('Neha Kumari', '<EMAIL>', '2001-01-30', 'F', 'EWS', '4321098764', 
 '2023-24', 'Hindi', '112', '2023001012', 'REG2023012', 
 'Suresh Kumar', 'Meera Devi', '813210', 'BIHAR', '678901234568', 'ABC678901235', '4321098764');

-- Insert some exam forms for testing
INSERT INTO exam_form_degree_iii (
    roll_number, category, is_regular, paper_i, paper_ii, paper_iii, paper_iv, fee
) VALUES 
-- Regular students with different paper combinations
('2023001001', 'UR', true, true, true, false, false, 1000.00),
('2023001003', 'SC', true, true, true, true, false, 750.00),
('2023001005', 'ST', true, true, false, false, false, 500.00),
('2023001007', 'EBC', true, true, true, true, true, 1000.00),
('2023001009', 'BC', true, true, true, false, false, 750.00),

-- Ex-students (higher fees)
('2023001002', 'UR', false, true, true, true, false, 1500.00),
('2023001004', 'SC', false, true, true, false, false, 1125.00);

-- Update test status
INSERT INTO test_status (test_name, status, message) 
VALUES ('test_data_insertion', 'completed', 'Test data inserted: 12 students, 7 exam forms');
