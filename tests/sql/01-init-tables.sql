-- Initialize database tables for testing
-- This script creates all necessary tables for the exam form management system

-- Create students table
CREATE TABLE students (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    date_of_birth DATE NOT NULL,
    gender CHAR(1) NOT NULL CHECK (gender IN ('M', 'F')),
    category VARCHAR(10) NOT NULL CHECK (category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')),
    mobile_number VARCHAR(10) NOT NULL,
    college VARCHAR(100) NOT NULL DEFAULT 'SSV College',
    session VARCHAR(10) NOT NULL,
    subject VARCHAR(50) NOT NULL,
    class_roll_number VARCHAR(4) NOT NULL,
    university_roll_number VARCHAR(10) NOT NULL UNIQUE,
    registration_number VARCHAR(20) NOT NULL UNIQUE,
    father_name VA<PERSON>HA<PERSON>(100) NOT NULL,
    mother_name VARCHAR(100) NOT NULL,
    pincode VARCHAR(6) NOT NULL,
    state VARCHAR(20) NOT NULL CHECK (state IN ('BIHAR', 'OTHER')),
    aadhar_number VARCHAR(12) NOT NULL UNIQUE,
    abc_id VARCHAR(12) NOT NULL UNIQUE,
    aadhar_mobile VARCHAR(10) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create exam form table
CREATE TABLE exam_form_degree_iii (
    id SERIAL PRIMARY KEY,
    roll_number VARCHAR(10) NOT NULL UNIQUE CHECK (LENGTH(roll_number) BETWEEN 5 AND 10),
    category VARCHAR(10) NOT NULL CHECK (category IN ('UR', 'EWS', 'BC', 'EBC', 'SC', 'ST')),
    is_regular BOOLEAN NOT NULL DEFAULT TRUE,
    paper_i BOOLEAN NOT NULL DEFAULT FALSE,
    paper_ii BOOLEAN NOT NULL DEFAULT FALSE,
    paper_iii BOOLEAN NOT NULL DEFAULT FALSE,
    paper_iv BOOLEAN NOT NULL DEFAULT FALSE,
    fee NUMERIC(10, 2) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_students_email ON students(email);
CREATE INDEX idx_students_university_roll ON students(university_roll_number);
CREATE INDEX idx_students_aadhar ON students(aadhar_number);
CREATE INDEX idx_students_abc_id ON students(abc_id);
CREATE INDEX idx_exam_form_roll_number ON exam_form_degree_iii(roll_number);

-- Create a test verification table
CREATE TABLE test_status (
    id SERIAL PRIMARY KEY,
    test_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL,
    message TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Insert initial test status
INSERT INTO test_status (test_name, status, message) 
VALUES ('database_initialization', 'completed', 'Database tables created successfully');
