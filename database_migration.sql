-- University Information Management System - Database Migration
-- This script migrates the existing database to the new refactored schema

-- ============================================================================
-- BACKUP EXISTING DATA
-- ============================================================================

-- Create backup table for existing students
CREATE TABLE IF NOT EXISTS students_backup AS 
SELECT * FROM students;

-- ============================================================================
-- CREATE NEW TABLES
-- ============================================================================

-- Departments table
CREATE TABLE IF NOT EXISTS departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Users table for multi-role authentication
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('super_admin', 'department_admin')),
    department_id INTEGER REFERENCES departments(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Student enrollments table
CREATE TABLE IF NOT EXISTS student_enrollments (
    id SERIAL PRIMARY KEY,
    student_id INTEGER NOT NULL REFERENCES students(id) ON DELETE CASCADE,
    department_id INTEGER NOT NULL REFERENCES departments(id) ON DELETE CASCADE,
    session VARCHAR(20) NOT NULL,
    subject VARCHAR(100) NOT NULL,
    class_roll_number VARCHAR(10),
    university_roll_number VARCHAR(20),
    registration_number VARCHAR(30),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    approved_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    approved_at TIMESTAMP,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(student_id, department_id, session)
);

-- ============================================================================
-- INSERT DEFAULT DATA
-- ============================================================================

-- Insert default departments
INSERT INTO departments (name, code, description) VALUES
('Science Department', 'SCI', 'Physics, Chemistry, Mathematics, Botany, Zoology'),
('Language Department', 'LANG', 'Hindi, English, Urdu, Sanskrit'),
('Social Science Department', 'SOC', 'History, Political Science, Economics, Sociology, Philosophy, Psychology, AIHC, IRPM')
ON CONFLICT (code) DO NOTHING;

-- Insert default super admin (password: admin123)
-- Note: In production, use a secure password and proper hashing
INSERT INTO users (email, password_hash, role, is_active) VALUES
('<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'super_admin', true)
ON CONFLICT (email) DO NOTHING;

-- ============================================================================
-- MODIFY EXISTING STUDENTS TABLE
-- ============================================================================

-- Add status column to students table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'students' AND column_name = 'status') THEN
        ALTER TABLE students ADD COLUMN status VARCHAR(20) DEFAULT 'registered' 
        CHECK (status IN ('registered', 'enrolled', 'approved', 'rejected'));
    END IF;
END $$;

-- Remove academic fields from students table (they'll be in enrollments)
-- Note: This is commented out to preserve existing data
-- Uncomment these lines after migrating data to enrollments table

/*
DO $$ 
BEGIN
    -- Remove college column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'college') THEN
        ALTER TABLE students DROP COLUMN college;
    END IF;
    
    -- Remove session column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'session') THEN
        ALTER TABLE students DROP COLUMN session;
    END IF;
    
    -- Remove subject column if it exists
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'subject') THEN
        ALTER TABLE students DROP COLUMN subject;
    END IF;
    
    -- Remove roll number columns if they exist
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'class_roll_number') THEN
        ALTER TABLE students DROP COLUMN class_roll_number;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'university_roll_number') THEN
        ALTER TABLE students DROP COLUMN university_roll_number;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'students' AND column_name = 'registration_number') THEN
        ALTER TABLE students DROP COLUMN registration_number;
    END IF;
END $$;
*/

-- ============================================================================
-- CREATE INDEXES FOR PERFORMANCE
-- ============================================================================

-- Indexes for departments
CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code);
CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name);

-- Indexes for users
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_department_id ON users(department_id);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active);

-- Indexes for students
CREATE INDEX IF NOT EXISTS idx_students_email ON students(email);
CREATE INDEX IF NOT EXISTS idx_students_aadhar_number ON students(aadhar_number);
CREATE INDEX IF NOT EXISTS idx_students_status ON students(status);

-- Indexes for student_enrollments
CREATE INDEX IF NOT EXISTS idx_enrollments_student_id ON student_enrollments(student_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_department_id ON student_enrollments(department_id);
CREATE INDEX IF NOT EXISTS idx_enrollments_status ON student_enrollments(status);
CREATE INDEX IF NOT EXISTS idx_enrollments_session ON student_enrollments(session);
CREATE INDEX IF NOT EXISTS idx_enrollments_approved_by ON student_enrollments(approved_by);

-- ============================================================================
-- CREATE TRIGGERS FOR UPDATED_AT
-- ============================================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_departments_updated_at 
    BEFORE UPDATE ON departments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_students_updated_at 
    BEFORE UPDATE ON students 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_enrollments_updated_at 
    BEFORE UPDATE ON student_enrollments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- DATA MIGRATION (OPTIONAL)
-- ============================================================================

-- Migrate existing student academic data to enrollments table
-- This is commented out - run manually after reviewing existing data

/*
INSERT INTO student_enrollments (
    student_id, 
    department_id, 
    session, 
    subject, 
    class_roll_number, 
    university_roll_number, 
    registration_number,
    status
)
SELECT 
    s.id,
    CASE 
        WHEN s.subject IN ('Physics', 'Chemistry', 'Mathematics', 'Botany', 'Zoology') THEN 1
        WHEN s.subject IN ('Hindi', 'English', 'Urdu', 'Sanskrit') THEN 2
        WHEN s.subject IN ('History', 'Political Science', 'Economics', 'Sociology', 'Philosophy', 'Psychology', 'AIHC', 'IRPM') THEN 3
        ELSE 1
    END as department_id,
    COALESCE(s.session, '2024-25') as session,
    s.subject,
    s.class_roll_number,
    s.university_roll_number,
    s.registration_number,
    'approved' as status
FROM students_backup s
WHERE s.subject IS NOT NULL
ON CONFLICT (student_id, department_id, session) DO NOTHING;

-- Update student status based on enrollment
UPDATE students 
SET status = 'approved' 
WHERE id IN (SELECT student_id FROM student_enrollments WHERE status = 'approved');
*/

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- Check table creation
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_name IN ('departments', 'users', 'student_enrollments', 'students')
ORDER BY table_name;

-- Check departments data
SELECT 'Departments' as table_name, COUNT(*) as record_count FROM departments
UNION ALL
SELECT 'Users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'Students' as table_name, COUNT(*) as record_count FROM students
UNION ALL
SELECT 'Student Enrollments' as table_name, COUNT(*) as record_count FROM student_enrollments;

-- Check indexes
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND tablename IN ('departments', 'users', 'student_enrollments', 'students')
ORDER BY tablename, indexname;

-- ============================================================================
-- CLEANUP (OPTIONAL)
-- ============================================================================

-- Drop backup table after successful migration
-- DROP TABLE IF EXISTS students_backup;

COMMIT;

-- ============================================================================
-- MIGRATION COMPLETE
-- ============================================================================

SELECT 'Database migration completed successfully!' as status;
