# Development Guide

This guide provides detailed instructions for setting up and developing the University Information Management System locally.

## 🚀 Quick Start for Local Development

### Prerequisites
- Go 1.23 or later
- Docker and Docker Compose
- Make (optional, for convenience commands)
- Git

### Recommended Development Setup

For the best development experience, we recommend running only PostgreSQL in Docker and the Go application locally. This provides:
- Faster compilation and restart times
- Better debugging capabilities
- Hot reloading support
- Direct access to Go tooling

## 📋 Step-by-Step Setup

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd exam-form
```

### 2. Start PostgreSQL Only
```bash
# Start only PostgreSQL database
make up-postgres

# Verify PostgreSQL is running
docker ps | grep postgres
```

### 3. Set Environment Variables
```bash
# Required environment variables for local development
export POSTGRES_USER=balena
export POSTGRES_PASSWORD=test
export DB_NAME=balena
export DB_HOST=localhost
export DB_PORT=5432
export JWT_SECRET=your-jwt-secret-key-here
export APP_USERNAME=admin
export APP_PASSWORD=admin123
export LOG_LEVEL=debug
export APP_ENV=development
```

**Pro Tip**: Create a `.env` file in your project root and source it:
```bash
# Create .env file
cat > .env << EOF
export POSTGRES_USER=balena
export POSTGRES_PASSWORD=test
export DB_NAME=balena
export DB_HOST=localhost
export DB_PORT=5432
export JWT_SECRET=your-jwt-secret-key-here
export APP_USERNAME=admin
export APP_PASSWORD=admin123
export LOG_LEVEL=debug
export APP_ENV=development
EOF

# Source it
source .env
```

### 4. Run the Application Locally

#### Option A: Original Structure
```bash
cd backend/app
go run main.go
```

#### Option B: Refactored Structure (Recommended)
```bash
cd backend/app
go run cmd/server/main.go
```

The application will start on `http://localhost:8080`

### 5. Access the Application
- Open your browser to `http://localhost:8080`
- Login with admin credentials (username: admin, password: admin123)

## 🔄 Development Workflow

### Making Changes
1. Edit Go files in `backend/app/`
2. The application will need to be restarted manually
3. Refresh your browser to see changes

### Hot Reloading (Optional)
For automatic reloading on file changes:

```bash
# Install air
go install github.com/cosmtrek/air@latest

# Create air config (if not exists)
cd backend/app
air init

# Run with hot reload
air
```

### Database Changes
If you modify database schemas:
1. Update SQL files in `postgres/init-scripts/`
2. Restart PostgreSQL: `docker restart exam-form-postgres-1`
3. If using SQLC, regenerate Go code: `sqlc generate`

## 🧪 Testing

### Running Tests
```bash
cd backend/app

# Run all tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run specific test
go test ./internal/handlers -v
```

### Using Test Scripts
```bash
# Run comprehensive test suite
./scripts/run_tests.sh all

# Run specific categories
./scripts/run_tests.sh unit
./scripts/run_tests.sh handlers
./scripts/run_tests.sh templates
```

## 🐳 Docker Development (Alternative)

If you prefer to develop entirely in Docker:

```bash
# Start all services
make up

# View logs
make logs

# Rebuild after changes
make down
docker compose build --no-cache
make up
```

## 🚫 What's NOT Needed for Local Development

### Cloudflare Tunnel
- **Not required** for local development
- Only needed for production or external access
- Skip `make up-cloudflared` during development

### Nginx (when running locally)
- The Go app serves directly on port 8080
- Nginx is only used in the Docker container setup

## 🔧 Troubleshooting

### Common Issues

#### Database Connection Failed
```bash
# Check if PostgreSQL is running
docker ps | grep postgres

# Check PostgreSQL logs
docker logs exam-form-postgres-1

# Restart PostgreSQL
docker restart exam-form-postgres-1
```

#### Port Already in Use
```bash
# Check what's using port 8080
lsof -i :8080

# Kill the process or use a different port
export PORT=8081
go run cmd/server/main.go
```

#### Environment Variables Not Set
```bash
# Verify environment variables
env | grep -E "(POSTGRES|JWT|APP_)"

# Re-source your .env file
source .env
```

### Database Reset
If you need to reset the database:
```bash
# Stop and remove PostgreSQL container
docker stop exam-form-postgres-1
docker rm exam-form-postgres-1

# Remove the volume
docker volume rm exam-form_db_data

# Start fresh
make up-postgres
```

## 📝 Development Best Practices

### Code Organization
- Follow the established project structure
- Keep handlers thin, business logic in services
- Write tests for new functionality
- Use proper error handling

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/your-feature-name
```

### Before Submitting PR
1. Run full test suite: `./scripts/run_tests.sh all`
2. Check code formatting: `go fmt ./...`
3. Run linter: `golangci-lint run`
4. Update documentation if needed

## 🆘 Getting Help

- Check the main [README.md](README.md) for general information
- Review [TESTING.md](TESTING.md) for testing guidelines
- Create an issue for bugs or feature requests
- Check existing issues for known problems
