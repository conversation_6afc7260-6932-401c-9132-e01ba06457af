version: '3.8'

services:
  test-postgres:
    image: postgres:15
    container_name: exam-form-test-postgres
    environment:
      POSTGRES_DB: balena_test
      POSTGRES_USER: balena_test
      POSTGRES_PASSWORD: test123
      POSTGRES_HOST_AUTH_METHOD: md5
    ports:
      - "5434:5432"
    volumes:
      - test_db_data:/var/lib/postgresql/data
      - ./tests/sql:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U balena_test -d balena_test"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  test-app:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: exam-form-test-app
    environment:
      DB_HOST: test-postgres
      DB_PORT: 5432
      DB_USER: balena_test
      DB_PASSWORD: test123
      DB_NAME: balena_test
      APP_USERNAME: test_admin
      APP_PASSWORD: test123
      JWT_SECRET: test_jwt_secret_for_automated_testing
      GO_ENV: test
      APP_ENV: test
    ports:
      - "8001:8080"
    depends_on:
      test-postgres:
        condition: service_healthy
    networks:
      - test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 3

volumes:
  test_db_data:

networks:
  test-network:
    driver: bridge
